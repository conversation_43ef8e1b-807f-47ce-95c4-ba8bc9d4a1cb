import { SearchSelectComponent } from '../../../../../../../../components/search-select/search-select.component.js?v=#{BuildVersion}#';
export class ECCNCodeSearchSelectComponent extends SearchSelectComponent {
    constructor(searchInputId, inputHiddenValueId, mode, apiKeySearch, apiURL, charsToSearch = 2, filterSearchIndex = null) {
        super(searchInputId, inputHiddenValueId, mode, apiKeySearch, apiURL, charsToSearch, filterSearchIndex);
    }

    // Override
    createSelectedItem(item) {
        this.trigger('selectItem', item);
        let condition = item.eccnStatus; // Handle the description here

        const title = item.eccnWarning?.replace(/\n/g, "");
        const selectedItem = document.createElement("span");
        selectedItem.className = "selected-item";
        selectedItem.innerHTML = `
        <span class="selected-item-content">${item.label}</span>
        ${condition ? `<img src="/img/icons/circle-info-yellow.svg" class="ms-1 rounded-circle bg-white" height="14" data-bs-toggle="tooltip" data-bs-placement="bottom" title="${title}"/>` : ''}
        <button type="button" id="btn-close-${this.searchInputId}" class="btn-close" aria-label="Close"></button>
        `;

        // Add event listener for remove button
        selectedItem.querySelector(".btn-close").addEventListener("click", () => {
            this.removeItem(selectedItem, item);
        });

        return selectedItem;
    }

    removeItem(selectedItem, itemToRemove) {
        this.trigger('removeItem');
        super.removeItem(selectedItem, itemToRemove);
    }
}
