using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using MediatR;

namespace GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetMailGroupNoByName
{
    public class GetMailGroupNoByNameHandler(IBaseRepository<MailGroup> _repository) : IRequestHandler<GetMailGroupNoByNameQuery, int>
    {
        public async Task<int> Handle(GetMailGroupNoByNameQuery request, CancellationToken cancellationToken)
        {
            var mailGroup = await _repository.ListAsync(
               filter: mg => mg.ClientNo == request.ClientNo
               && mg.Name.ToLower().Equals(request.Name.ToLower()));
            if (mailGroup == null || mailGroup.Count == 0)
            {
                throw new ArgumentNullException($"Not found \"{request.Name}\" mail group in client {request.ClientNo}");
            }
            return mailGroup[0].MailGroupId;
        }
    }
}