﻿GlobalTrader.FormHelper = (function () {
    return {
        convertFormDataToOject: function (formObj) {
            let formData = formObj.serializeArray();
            let data = {};
            let formId = formObj.attr('id');

            formData.forEach(function (field) {
                let input = $(`#${formId} :input[name="${field.name}"]`);
                if (data[field.name]) { return; }
                if (input.is(':checkbox')) {
                    data[field.name] = $(input).filter("input[type='checkbox']").is(':checked');
                }
                else if (input.is('textarea')) {
                    data[field.name] = normalizeString(field.value) || null
                }
                else if (input.is('select')) {
                    data[field.name] = parseSelectValue(field.value) || null;
                }
                else {
                    data[field.name] = field.value.toString().trim() || null;
                }
            });

            return data;
        },
        sendPostRequestAsync: async function (url, formObj) {
            const header = { "RequestVerificationToken": $(formObj).find(':input[name="__RequestVerificationToken"]').val() }
            return await GlobalTrader.ApiClient.postAsync(url, GlobalTrader.FormHelper.convertFormDataToOject(formObj), header);
        },
        sendPutRequestAsync: async function (url, formObj) {
            const header = { "RequestVerificationToken": $(formObj).find(':input[name="__RequestVerificationToken"]').val() }
            return await GlobalTrader.ApiClient.putAsync(url, GlobalTrader.FormHelper.convertFormDataToOject(formObj), header);
        },
        sendPatchRequestAsync: async function (url, formObj) {
            const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() }
            return await GlobalTrader.ApiClient.patchAsync(url, GlobalTrader.FormHelper.convertFormDataToOject(formObj), header);
        },
        resetFormValues: function (form) {
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                if (input.type === 'checkbox' || input.type === 'radio') {
                    input.checked = false;
                } else {
                    input.value = '';
                }
            });
        }
    };
})();

function trimFormDataValues(formData) {
    const trimmedFormData = new FormData();

    for (const [key, value] of formData.entries()) {
        if (typeof value === 'string') {
            trimmedFormData.append(key, value.trim());
        } else {
            trimmedFormData.append(key, value);
        }
    }

    return trimmedFormData;
}

function parseSelectValue(value) {
    if (value === "") return null;
    if (value === "true") return true;
    if (value === "false") return false;
    if (value === '0') return 0;
    return value.toString().trim();
}

function focusFirstField($form) {
    const $formElements = $form.find('input, select, textarea');
    const $firstVisible = $formElements.filter(function () {
        const $el = $(this);

        return $el.attr('type') !== 'hidden' &&
            !$el.is(':hidden') &&
            $el.css('display') !== 'none' &&
            $el.parents(':hidden').length === 0 &&
            !$el.prop('disabled');
    }).first();

    if ($firstVisible.length > 0) {
        $firstVisible.focus();
    }
}
