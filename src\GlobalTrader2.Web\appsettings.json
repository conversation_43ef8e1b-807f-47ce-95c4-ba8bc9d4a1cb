{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.ApplicationInsights"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"}}, {"Name": "ApplicationInsights", "Args": {"connectionString": "#{ApplicationInsights}#", "telemetryConverter": "Serilog.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"}}], "Destructure": [{"Name": "With", "Args": {"policy": "GlobalTrader2.Web.Helper.SensitiveDataDestructuringPolicy, GlobalTrader2.Web"}}]}, "AzureMonitor": {"ConnectionString": "#{ApplicationInsights}#"}, "ConnectionStrings": {"MainDb": "#{MainDb}#", "ImportDb": "#{ImportDb}#", "AzureBlobStorage": "#{AzureBlobStorage}#"}, "AzureAd": {"ClientId": "#{ClientId}#", "TenantId": "#{TenantId}#", "Instance": "https://login.microsoftonline.com/", "Domain": ".onmicrosoft.com", "RedirectUri": "#{RedirectUri}#", "CallbackPath": "/signin-oidc", "EnableADAuth": "true"}, "UseDefaultLogger": false, "UseDistributedSqlServerCache": true, "CheckLastClientInDB": true, "UseLoginPage": false, "AllowedHosts": "*", "Version": "v2.0.0", "GTv1SiteUrl": "#{GTv1SiteUrl}#", "RetryPolicy": {"MaxRetryCount": 3, "MaxRetryDelay": 30, "MaxRetryDurationInSecond": 180}, "JobSchedulers": {"ToDoReminder": "0 0/5 * * * ?"}, "CachingPolicy": {"SlidingExpiration": 240, "AbsoluteExpiration": 480}, "WebOptimizer": {"EnableMinification": true, "EnableCaching": true, "EnableTagHelperBundling": true}, "IsIPOHUB": true, "DurationToastNotification": 15000, "Navigations": {"Dashboard": {"SalesPerformance": "", "Sales": ""}}, "IHS": {"BaseUrl": "#{IHSBaseUrl}#", "UserName": "#{IHSUserName}#", "Password": "#{IHSPassword}#", "AuthApi": "/parts-saas/auth", "SearchPartsApi": "/parts-saas/parts"}, "EmailSettings": {"SmtpServer": "#{EmailSettingsSmtpServer}#", "Port": "#{EmailSettingsPort}#", "SenderName": "#{EmailSettingsSenderName}#", "SenderEmail": "#{EmailSettingsSenderEmail}#", "Username": "#{EmailSettingsUsername}#", "Password": "#{EmailSettingsPassword}#", "UseSSL": "#{EmailSettingsUseSSL}#", "TestReceiverEmail": "#{EmailSettingsTestReceiverEmail}#", "SendOutEmail": true}, "DigiKey": {"BaseUrl": "#{DigiKeyBaseUrl}#", "ClientId": "#{DigiKeyClientId}#", "ClientSecret": "#{DigiKeyClientSecret}#", "TokenUrl": "#{DigiKeyTokenUrl}#", "ProductsUrl": "#{DigiKeyProductsUrl}#"}, "FE": {"BaseUrl": "#{FEBaseUrl}#"}, "Lytica": {"ApiUrl": "#{LyticaApiUrl}#", "UserName": "#{LyticaUserName}#", "Password": "#{LyticaPassword}#"}, "SrcLoginEmail": "<EMAIL>", "IpoPurchasingEmail": "<EMAIL>", "ReverseLogisticsEmail": "<EMAIL>", "IronPdf": {"LicenseKey": "#{PdfLicenseKey}#"}, "ImportData": {"MaxRowCount": 25000}, "Cdn": {"BaseUrl": "#{CdnBaseUrl}#"}, "EPPlus": {"ExcelPackage": {"License": "NonCommercialOrganization:GTv2"}}, "SendToSupplier": "#{SendToSupplier}#"}