﻿namespace GlobalTrader2.Orders.UserCases.Orders.PriceRequest
{
    public class GetPOQuoteLineDataListNuggetHandler : IRequestHandler<GetPOQuoteLineDataListNuggetQuery, BaseResponse<IEnumerable<POQuoteLineDataListNuggetDto>>>
    {
        private readonly IBaseRepository<POQuoteLineDataListNuggetReadModel> _repository;
        private readonly IMapper _mapper;

        public GetPOQuoteLineDataListNuggetHandler(IBaseRepository<POQuoteLineDataListNuggetReadModel> repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public async Task<BaseResponse<IEnumerable<POQuoteLineDataListNuggetDto>>> Handle(GetPOQuoteLineDataListNuggetQuery request, CancellationToken cancellationToken)
        {
            var parameters = new[]
            {
            new SqlParameter("@ClientId", request.ClientId ?? (object)0),
            new SqlParameter("@TeamId", request.TeamId ?? (object)System.DBNull.Value),
            new SqlParameter("@DivisionId", request.DivisionId ?? (object)System.DBNull.Value),
            new SqlParameter("@LoginId", request.LoginId ?? (object)System.DBNull.Value),
            new SqlParameter("@OrderBy", request.OrderBy ?? (object)System.DBNull.Value),
            new SqlParameter("@SortDir", request.SortDir ?? (object)System.DBNull.Value),
            new SqlParameter("@PageIndex", request.PageIndex ?? (object)System.DBNull.Value),
            new SqlParameter("@PageSize", request.PageSize ?? (object)System.DBNull.Value),
            new SqlParameter("@PartSearch", request.PartSearch ?? (object)System.DBNull.Value),
            new SqlParameter("@CMSearch", request.CmSearch ?? (object)System.DBNull.Value),
            new SqlParameter("@SalesmanSearch", request.SalesmanSearch ?? (object)System.DBNull.Value),
            new SqlParameter("@IncludeClosed", request.IncludeClosed ?? (object)false),
            new SqlParameter("@POQuoteNoLo", request.PoQuoteNoLo ?? (object)System.DBNull.Value),
            new SqlParameter("@POQuoteNoHi", request.PoQuoteNoHi ?? (object)System.DBNull.Value),
            new SqlParameter("@DatePOQuotedFrom", request.DateQuotedFrom ?? (object)System.DBNull.Value),
            new SqlParameter("@DatePOQuotedTo", request.DateQuotedTo ?? (object)System.DBNull.Value),
            new SqlParameter("@RecentOnly", request.RecentOnly ?? (object)false)
        };

            var result = await _repository.SqlQueryRawAsync(
                $"{StoredProcedures.DataListNugget_PurchaseRequestLine} @ClientId, @TeamId, @DivisionId, @LoginId, @OrderBy, @SortDir, @PageIndex, @PageSize, @PartSearch, @CMSearch, @SalesmanSearch, @IncludeClosed, @POQuoteNoLo, @POQuoteNoHi, @DatePOQuotedFrom, @DatePOQuotedTo, @RecentOnly",
                parameters);

            var response = new BaseResponse<IEnumerable<POQuoteLineDataListNuggetDto>>();

            response.Success = true;
            response.Data = _mapper.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(result);

            return response;
        }
    }
}
