﻿#customer-requrement-sourcing-results-box ::deep .dt-layout-row {
    margin: 0px !important;
}
#auto-sourcing-box ::deep .dt-layout-row {
    margin: 0px !important;
}
::deep .part-watch-match {
    background-color: rgb(211, 211, 211);
}

#customer-requrement-sourcing-results-box ::deep .dt-empty {
    font-style: italic;
}

::deep table.dataTable td.posted_First, table.dataTable td.unposted_First, table.dataTable td.allocated_First, table.dataTable td.shipped_First, table.dataTable td.partShipped_First, table.dataTable td.giInspected_First, table.dataTable td.inactive_First, table.dataTable td.readyToShip_First, table.dataTable td.notReadyToShip_First, table.dataTable td.received_First, table.dataTable td.partReceived_First {
    background-repeat: no-repeat;
    background-position: 5% 15px;
    padding-left: 19px;
}

::deep table.dataTable td.notReadyToShip_First {
    background-repeat: no-repeat;
    background-position: 5% 15px;
    padding-left: 19px;
    background-image: url(/img/icons/notok.gif);
}

::deep table.dataTable tr:hover td.notReadyToShip_First {
    background-repeat: no-repeat;
    background-position: 5% 15px;
    padding-left: 19px;
    background-image: url(/img/icons/notok_x.gif);
}

::deep table.dataTable tr.selected td.notReadyToShip_First {
    background-repeat: no-repeat;
    background-position: 5% 15px;
    padding-left: 19px;
    background-image: url(/img/icons/notok_sel.gif);
}

::deep table.dataTable td.readyToShip_First {
    background-repeat: no-repeat;
    background-position: 5% 15px;
    padding-left: 19px;
    background-image: url(/img/icons/ready.gif);
}

::deep table.dataTable tr:hover td.readyToShip_First {
    background-repeat: no-repeat;
    background-position: 5% 15px;
    padding-left: 19px;
    background-image: url(/img/icons/ready_x.gif);
}

::deep table.dataTable tr.selected td.readyToShip_First {
    background-repeat: no-repeat;
    background-position: 5% 15px;
    padding-left: 19px;
    background-image: url(/img/icons/ready_sel.gif);
}

::deep table.dataTable td.allocated_First {
    background-repeat: no-repeat;
    background-position: 5% 15px;
    padding-left: 19px;
    background-image: url(/img/icons/allocated.gif);
}

::deep table.dataTable tr:hover td.allocated_First {
    background-repeat: no-repeat;
    background-position: 5% 15px;
    padding-left: 19px;
    background-image: url(/img/icons/allocated_x.gif);
}

::deep table.dataTable tr.selected td.allocated_First {
    background-repeat: no-repeat;
    background-position: 5% 15px;
    padding-left: 19px;
    background-image: url(/img/icons/allocated_sel.gif);
}

::deep .readyToShip_First,
.notReadyToShip_First,
.allocated_First {
    text-align: center;
}

::deep .notReadyToShip[title] {
    cursor: pointer;
}

.section-error-summary {
    display: flex;
    gap: 5px;
    align-items: center;
    background: #F8D7DA;
    color: #EC3814;
    line-height: 14.48px;
    padding: 5px;
}

    .section-error-summary div p {
        margin-bottom: 0;
    }


    .section-error-summary img {
        width: 20px;
        height: 20px;
    }

::deep #hub-rfq-items-table .dt-empty{
    text-align: center !important;
}

::deep #hub-rfq-items-table td:first-child {
    text-align: right;
    align-items: center;
}

::deep #hub-rfq-items-table th:first-child {
    text-align: right;
    align-items: center;
}


#dialogContainer {
    position: absolute;
}

#dialogContainer .modal-body table tr td {
    padding: 0.5rem 1rem;
    word-wrap: break-word;
    word-break: break-word;
    vertical-align: top;
}

#dialogContainer .modal-footer p {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

#dialogContainer .modal-body table tr td span,
#dialogContainer .modal-footer {
    font-size: 11px;
}
#hub-rfq-item-wrapper{
    position: relative;
}
