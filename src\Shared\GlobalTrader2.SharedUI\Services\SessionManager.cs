﻿using GlobalTrader2.Aggregator.UseCases.DataList.GetApplicationSettings;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.LoginManager;
using GlobalTrader2.SharedUI.Constants;
using GlobalTrader2.SharedUI.Enums;
using GlobalTrader2.SharedUI.Models;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Commands;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries;
using MediatR;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Text.Json;

namespace GlobalTrader2.SharedUI.Services
{
    public class SessionManager
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ISession _session;
        private readonly ISender _mediator;
        private readonly ICacheManager _cacheManager;
        private readonly ILogger<SessionManager> _logger;

        public List<UserPermission>? GeneralPermissions
        {
            get
            {
                return _session.Get<List<UserPermission>>(SessionKey.GeneralPermissions);
            }
            set
            {
                _session.Set<List<UserPermission>>(SessionKey.GeneralPermissions, value);
            }
        }

        public List<UserPermission>? SectionPermissions
        {
            get
            {
                return _session.Get<List<UserPermission>>(SessionKey.SectionPermissions);
            }
            set
            {
                _session.Set<List<UserPermission>>(SessionKey.SectionPermissions, value);
            }
        }

        public List<UserPermission> WarehousePermissions
        {
            get
            {
                return _session?.Get<List<UserPermission>>(SessionKey.WarehousePermission) ?? new List<UserPermission>();
            }
            set
            {
                _session.Set<List<UserPermission>>(SessionKey.WarehousePermission, value);
            }
        }

        public bool IsUserAuthenticated
        {
            get
            {
                return _session != null && _session.GetInt32(SessionKey.LoginID) != null;
            }
        }

        public int LoginTimeoutInMinutes
        {
            get
            {
                return _session.GetInt32(SessionKey.IdleTimeout) ?? Identity.MaxIdleTimeoutInMinutes;
            }
        }

        public string LoginFullName
        {
            get
            {
                return $"{_session.GetString(SessionKey.LoginFirstName)} {_session.GetString(SessionKey.LoginLastName)}";
            }
        }

        public bool IsPOHub
        {
            get
            {
                return _session.Get<bool>(SessionKey.IsPOHub);
            }
        }

        public bool IsDivision
        {
            get
            {
                return _session.Get<bool>(SessionKey.IsDivision);
            }
        }

        public bool IsTeam
        {
            get
            {
                return _session.Get<bool>(SessionKey.IsTeam);
            }
        }

        public bool IsGlobalUser
        {
            get
            {
                return _session.Get<bool>(SessionKey.IsGlobalUser);
            }
        }

        //Is Global Sales Access group's member
        public bool IsGSA
        {
            get
            {
                return _session.Get<bool>(SessionKey.IsGSA);
            }
        }

        public bool IsGSAViewPermission
        {
            get
            {
                return _session.Get<bool>(SessionKey.IsGSAViewPermission);
            }
        }

        public int? LoginTeamID
        {
            get
            {
                return _session.GetInt32(SessionKey.LoginTeamID);
            }
        }

        public int? LoginDivisionID
        {
            get
            {
                return _session.GetInt32(SessionKey.LoginDivisionID);
            }
        }

        public int? ClientID
        {
            get
            {
                return _session.GetInt32(SessionKey.ClientID);
            }
        }

        public string? ClientName
        {
            get
            {
                return _session.GetString(SessionKey.ClientName);
            }
        }

        public int? LoginID
        {
            get
            {
                return _session.GetInt32(SessionKey.LoginID);
            }
        }

        public int? MasterLoginId
        {
            get
            {
                return _session.GetInt32(SessionKey.MasterLoginNo);
            }

        }
        
        public int? ClientCurrencyID
        {
            get
            {
                return _session.GetInt32(SessionKey.ClientCurrencyID);
            }
        }

        public string? ClientCurrencyCode
        {
            get
            {
                return _session.GetString(SessionKey.ClientCurrencyCode);
            }
        }

        public string? Culture
        {
            get
            {
                return _session.GetString(SessionKey.Culture);
            }
        }
       
        public List<UserPermission>? OrdersPermission
        {
            get
            {
                return _session.Get<List<UserPermission>>(SessionKey.OrdersPermission);
            }
            set
            {
                _session.Set<List<UserPermission>>(SessionKey.OrdersPermission, value);
            }
        }

        public bool IsLeftPanelVisible
        {
            get
            {
                return _session.Get<bool?>(SessionKey.LeftPanelVisible) ?? true;
            }
        }

        public List<UserPermission>? UtilityPermission
        {
            get
            {
                return _session.Get<List<UserPermission>>(SessionKey.UtilityPermission);
            }
            set
            {
                _session.Set<List<UserPermission>>(SessionKey.UtilityPermission, value);
            }
        }

        public ViewLevelList DefaultListPageView
        {
            get
            {
                var value = _session.GetInt32(SessionKey.DefaultListPageView);
                if (value == null)
                {
                    return ViewLevelList.My;
                }
                else
                {
                    return (ViewLevelList)value;
                }
            }
        }

        public int? ClientCurrencyId
        {
            get
            {
                return _session.GetInt32(SessionKey.ClientCurrencyID);
            }
        }


        public SessionManager(IHttpContextAccessor httpContextAccessor, ISender mediator, ICacheManager cacheManager, ILogger<SessionManager> logger)
        {
            if (httpContextAccessor == null || httpContextAccessor.HttpContext == null)
            {
                throw new ArgumentNullException(nameof(httpContextAccessor));
            }

            _httpContextAccessor = httpContextAccessor;
            _session = _httpContextAccessor.HttpContext.Session;
            _mediator = mediator;
            _cacheManager = cacheManager;
            _logger = logger;
        }

        public async Task<bool> LoadUserSettings(string adEmail)
        {
            var activeLoginAccounts = await GetActiveLoginAccountsByADEmail(adEmail);
            if (!activeLoginAccounts.Success || activeLoginAccounts.Data == null || !activeLoginAccounts.Data.Any())
            {
                _logger.LogError("Could not found any active login account of user: {ADLoginName}", adEmail.Substring(0, 5));
                return false;
            }

            var masterLogin = await DoMasterLogin(adEmail);
            if (!masterLogin.Success || masterLogin.Data == null)
            {
                return false;
            }

            var isLoginAccountActive = activeLoginAccounts.Data.Any(activeAccount => activeAccount.LoginName.Equals(masterLogin.Data.LoginName));
            BaseResponse<LoginDetailsDto> loginDetails;
            if (isLoginAccountActive)
            {
                loginDetails = await GetLoginDetails(masterLogin.Data.LoginName);
            }
            else
            {
                _logger.LogWarning("Could not found any active login account of current master login user: {0}", this.MasterLoginId);
                var defaultLoginAccount = activeLoginAccounts.Data.FirstOrDefault(activeAccount => activeAccount.MasterLoginNo > 0 && activeAccount.ClientId > 0);
                loginDetails = await DoMasterLoginWithOtherClient(defaultLoginAccount?.MasterLoginNo ?? 0, defaultLoginAccount?.ClientId ?? 0);
            }

            if (loginDetails.Success && loginDetails.Data != null)
            {
                StoreGeneralLoginItems(loginDetails.Data);
                var loginPreferenceDetails = await GetLoginPreferenceDetails(loginDetails.Data.LoginId);
                if (loginPreferenceDetails.Success && loginPreferenceDetails.Data != null)
                {
                    StoreLoginPreferences(loginPreferenceDetails.Data);
                }
            }
            await SetSuccessfulSaveMessageTime();
            return true;
        }

        public async Task LoadOtherClientUserSettings(int masterLoginNo, int clientId)
        {
            var loginDetails = await DoMasterLoginWithOtherClient(masterLoginNo, clientId);

            if (loginDetails.Success && loginDetails.Data != null)
            {
                StoreGeneralLoginItems(loginDetails.Data);

                var loginPreferenceDetails = await GetLoginPreferenceDetails(loginDetails.Data.LoginId);
                if (loginPreferenceDetails.Success && loginPreferenceDetails.Data != null)
                {
                    StoreLoginPreferences(loginPreferenceDetails.Data);
                }
            }
        }

        private async Task SetSuccessfulSaveMessageTime()
        {
            var filterKeys = new List<string>()
            {
                AppSettingKeys.SuccessfulSaveMessageTime,
            };

            var settingItems = await _mediator.Send(new GetApplicationSettingsByKeysQuery { FilterKeys = filterKeys });
            if (settingItems?.Data != null)
            {
                var successfulSaveMessageTime = settingItems.Data.FirstOrDefault(x => x.Key == AppSettingKeys.SuccessfulSaveMessageTime)?.Value;
                if (int.TryParse(successfulSaveMessageTime, out int successfulSaveMessageTimeValue))
                {
                    _session.SetInt32(SessionKey.SuccessfulSaveMessageTime, successfulSaveMessageTimeValue);
                }
            }
        }

        private async Task<BaseResponse<IList<ActiveLoginAccountDto>>> GetActiveLoginAccountsByADEmail(string ADEmail)
        {
            var activeLoginsQuery = new GetActiveLoginByADEmailQuery
            {
                ADEmail = ADEmail
            };
            var activeLogins = await _mediator.Send(activeLoginsQuery);
            return activeLogins;
        }

        private async Task<BaseResponse<MasterLoginResultDto>> DoMasterLogin(string loginName)
        {
            var masterLoginCommand = new MasterLoginCommand
            {
                LoginName = loginName,
                SessionId = GetSesssionId(),
                IpAddress = _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.MapToIPv4().ToString() ?? string.Empty,
                ServerIP = _httpContextAccessor.HttpContext?.Connection?.LocalIpAddress?.MapToIPv4().ToString() ?? string.Empty,
            };
            var masterLogin = await _mediator.Send(masterLoginCommand);

            return masterLogin;
        }

        private async Task<BaseResponse<LoginDetailsDto>> GetLoginDetails(string loginName)
        {
            var loginDetailsCommand = new LoginDetailsCommand
            {
                LoginName = loginName
            };

            var loginDetails = await _mediator.Send(loginDetailsCommand);

            return loginDetails;
        }

        private async Task<BaseResponse<LoginPreferenceDetailsDto>> GetLoginPreferenceDetails(int loginId)
        {
            var loginPreferenceDetailsCommand = new LoginPreferenceDetailsCommand
            {
                LoginNo = loginId
            };
            var loginPreferenceDetails = await _mediator.Send(loginPreferenceDetailsCommand);

            return loginPreferenceDetails;
        }
        private async Task<BaseResponse<LoginDetailsDto>> DoMasterLoginWithOtherClient(int masterLoginNo, int clientId)
        {
            var loginByOtherClientCommand = new LoginByOtherClientCommand
            {
                MasterLoginNo = masterLoginNo,
                ClientId = clientId,
                SessionId = GetSesssionId(),
                IpAddress = _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.MapToIPv4().ToString() ?? string.Empty,
                ServerIP = _httpContextAccessor.HttpContext?.Connection?.LocalIpAddress?.MapToIPv4().ToString() ?? string.Empty,
            };

            var loginDetails = await _mediator.Send(loginByOtherClientCommand);

            return loginDetails;
        }

        public string? GetString(string key)
        {
            return _session.GetString(key);
        }

        public int? GetInt32(string key)
        {
            return _session.GetInt32(key);
        }

        public bool GetBool(string key)
        {
            return _session.Get<bool>(key);
        }

        public void StoreGeneralLoginItems(LoginDetailsDto loginDetail)
        {
            _session.SetInt32(SessionKey.LoginID, loginDetail.LoginId);
            _session.SetString(SessionKey.LoginFirstName, loginDetail.FirstName ?? string.Empty);
            _session.SetString(SessionKey.LoginLastName, loginDetail.LastName ?? string.Empty);
            _session.SetString(SessionKey.LoginFullName, loginDetail.EmployeeName ?? string.Empty);
            _session.SetString(SessionKey.LoginEmail, loginDetail.EMail ?? string.Empty);
            _session.SetString(SessionKey.ADLogin, loginDetail.ADLogin ?? string.Empty);
            _session.SetInt32(SessionKey.ClientNoForDropdown, loginDetail.DefaultClientNo.GetValueOrDefault());
            _session.SetInt32(SessionKey.LoginDivisionID, loginDetail.DivisionNo.GetValueOrDefault());
            _session.SetString(SessionKey.LoginDivisionName, loginDetail.DivisionName ?? string.Empty);
            _session.SetString(SessionKey.ClientName, loginDetail.ClientName ?? string.Empty);
            _session.SetInt32(SessionKey.MasterLoginNo, loginDetail.MasterLoginNo ?? 0);
            _session.SetString(SessionKey.TeamName, loginDetail.TeamName ?? string.Empty);
            _session.SetInt32(SessionKey.ClientID, loginDetail.ClientNo.GetValueOrDefault());
            _session.SetInt32(SessionKey.LoginTeamID, loginDetail.TeamNo.GetValueOrDefault());
            _session.Set<bool>(SessionKey.IsGSA, loginDetail.IsGSA.GetValueOrDefault());
            _session.Set<bool>(SessionKey.IsGSAViewPermission, loginDetail.IsGSAViewPermission.GetValueOrDefault());
            _session.Set<bool>(SessionKey.IsGlobalUser, loginDetail.IsGlobal.GetValueOrDefault());
            _session.Set<bool>(SessionKey.IsPOHub, loginDetail.IsPOHub.GetValueOrDefault());
            _session.SetString(SessionKey.ClientCurrencyCode, loginDetail.ClientCurrencyCode ?? string.Empty);
            _session.SetInt32(SessionKey.ClientCurrencyID, loginDetail.ClientCurrencyNo.GetValueOrDefault());
            _session.SetString(SessionKey.ClientCode, loginDetail.ClientCode ?? string.Empty);
            _session.Set<bool>(SessionKey.IsDivision, loginDetail.isDivision.GetValueOrDefault());
            _session.Set<bool>(SessionKey.IsTeam, loginDetail.isTeam.GetValueOrDefault());
            _session.SetInt32(SessionKey.POHubMailGroupId, loginDetail.POHubMailGroupId ?? 0);
        }

        private void StoreLoginPreferences(LoginPreferenceDetailsDto loginPreferenceDetails)
        {
            _session.SetInt32(SessionKey.IdleTimeout, loginPreferenceDetails.LoginTimeout);
            _session.SetString(SessionKey.Culture, loginPreferenceDetails.DefaultSiteLanguageCode ?? GlobalLanguage.GetLanguageCode(GlobalLanguage.List.English));
            _session.SetString(SessionKey.BackgroundImage, loginPreferenceDetails.BackgroundImage ?? string.Empty);
            _session.Set<bool>(SessionKey.ShowMessageAlert, loginPreferenceDetails.ShowMessageAlert.GetValueOrDefault());
            _session.SetInt32(SessionKey.DefaultListPageSize, loginPreferenceDetails.DefaultListPageSize);
            _session.Set<bool>(SessionKey.SaveDataListNuggetStateByDefault, loginPreferenceDetails.SaveDataListNuggetStateByDefault);
            _session.SetInt32(SessionKey.DefaultListPageView, loginPreferenceDetails.DefaultListPageView);
        }


        public void ClearSession()
        {
            _session.Clear();
        }

        public void ClearCookies()
        {
            if (_httpContextAccessor?.HttpContext?.Request.Cookies is null)
            {
                return;
            }

            foreach (var cookie in _httpContextAccessor.HttpContext.Request.Cookies.Keys)
            {
                _httpContextAccessor.HttpContext.Response.Cookies.Delete(cookie);
            }
        }

        public async Task ClearCaches()
        {
            var loginName = GetString(SessionKey.LoginEmail);
            await _cacheManager.RemoveByPatternAsync($",LoginId:{GetInt32(SessionKey.LoginID).GetValueOrDefault()},");
            await _cacheManager.RemoveByPatternAsync($",MasterLoginId:{GetInt32(SessionKey.MasterLoginNo).GetValueOrDefault()},");
            await _cacheManager.RemoveByPatternAsync($",ADEmail:{loginName},");
            if (_httpContextAccessor.HttpContext?.User?.Identity?.Name != null && _httpContextAccessor.HttpContext?.User?.Identity?.Name != loginName)
            {
                await _cacheManager.RemoveByPatternAsync($",ADEmail:{_httpContextAccessor.HttpContext?.User?.Identity?.Name ?? ""},");
            }
        }

        public string GetSesssionId()
        {
            return _session.Id.Replace("-", "").Substring(0, 20);
        }

        public async Task LogUserOut(int loginId, string sessionName)
        {
            var logoutCommand = new LogoutCommand
            {
                LoginId = loginId,
                SessionName = sessionName
            };
            var result = await _mediator.Send(logoutCommand);
            if (result.Success)
            {
                await ClearCaches();
                ClearSession();
                ClearCookies();
            }
        }

        public static void MaintainStateWithoutFullPostback(HttpContext context)
        {
            // Copies all request cookies to the response cookies collection  
            foreach (var key in context.Request.Cookies.Keys)
            {
                var cookieValue = context.Request.Cookies[key];
                if (cookieValue != null)
                {
                    context.Response.Cookies.Append(key, cookieValue);
                }
            }
        }

        public async Task ExtendAuthenticationTicketExpire()
        {
            if (_httpContextAccessor.HttpContext != null)
            {
                var authResult = await _httpContextAccessor.HttpContext.AuthenticateAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                if (authResult.Succeeded)
                {
                    var properties = authResult.Properties;
                    properties.ExpiresUtc = DateTime.UtcNow.AddMinutes(LoginTimeoutInMinutes);

                    await _httpContextAccessor.HttpContext.SignInAsync(
                        CookieAuthenticationDefaults.AuthenticationScheme,
                        authResult.Principal,
                        properties);
                }
            }
        }
    }

    public static class SessionExtensions
    {
        public static void Set<T>(this ISession session, string key, T? value)
        {
            session.SetString(key, JsonSerializer.Serialize(value));
        }

        public static T? Get<T>(this ISession session, string key)
        {
            var value = session.GetString(key);
            return value == null ? default : JsonSerializer.Deserialize<T>(value);
        }

        public static CultureInfo GetCurrentCulture(this SessionManager sessionManager)
        {
            var cultureName = sessionManager.GetString(SessionKey.Culture)
                ?? GlobalLanguage.GetLanguageCode(GlobalLanguage.List.English);

            return new CultureInfo(cultureName);
        }
    }
}
