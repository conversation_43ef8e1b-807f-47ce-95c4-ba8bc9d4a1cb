﻿namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.PoQuote
{
    [SectionAuthorize(SecurityFunction.OrdersSection_View)]
    public class IndexModel : BasePageModel
    {
        private readonly SessionManager _sessionManager;

        public bool CanAdd { get; set; } = true;
        public int CurrentTab { get; set; }
        public bool CanViewMy { get; set; } = true;
        public bool CanViewTeam { get; set; } = true;
        public bool CanViewDivision { get; set; } = true;
        public bool CanViewCompany { get; set; } = true;

        public IndexModel(SecurityManager securityManager, IMediator mediator, SessionManager sessionManager) : base(securityManager)
        {
            _sessionManager = sessionManager;
            AddBreadCrumbs();
            SiteSection = SiteSection.Orders;
            PageType = SitePage.Orders_POQuoteBrowse;
        }

        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.PriceRequest);
        }

        public IActionResult OnGet()
        {

            if(!_sessionManager.IsPOHub)
            {
                return NotFound();
            }

            //Get visible tab list
            var listTabs = Enum.GetValues<ViewLevelList>();

            //set tab from preferences if it's not been set specifically
            var defaultPage = _sessionManager.DefaultListPageView;
            CurrentTab = (int)listTabs.First(x => x == defaultPage);

            return Page();
        }

    }
}