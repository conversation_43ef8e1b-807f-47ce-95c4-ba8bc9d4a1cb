using System.Globalization;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.Orders.Requirements;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.AdvisoryNote.AdvisoryNotes.Queries;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetDetailsForLineCalculations;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetAllSoLinesBySoId;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetClosedSoLinesBySoId;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetOpenSoLinesBySoId;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSoLineListBySoId
{
    /// <summary>
    /// usp_selectAll_SalesOrderLine_for_SalesOrder
    /// </summary>
    public class GetSoLineListBySoIdHandler(
    IBaseRepository<IsIpoExistReadModel> _isIpoExistRepository, ISender _sender)
    : IRequestHandler<GetSoLineListBySoIdQuery, BaseResponse<GetSoLineListDto>>
    {
        public async Task<BaseResponse<GetSoLineListDto>> Handle(GetSoLineListBySoIdQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<GetSoLineListDto> { Data = new GetSoLineListDto() };

            var salesOrderLineList = await GetSoLineListAsync(request, cancellationToken);

            response.Data.Count = salesOrderLineList.Count;

            var soDetails = await _sender.Send(new GetDetailsForLineCalculationsQuery(request.SalesOrderId), cancellationToken);
            var listMfrNotes = await GetManufacturerNotes(salesOrderLineList, request.ClientId, cancellationToken);

            await ProcessSoLineListResponse(salesOrderLineList, soDetails, listMfrNotes, response.Data);
            response.Success = true;
            return response;
        }

        private async Task<IReadOnlyList<AllOpenClosedSalesOrderLineDetailsDto>> GetSoLineListAsync(GetSoLineListBySoIdQuery request, CancellationToken cancellationToken)
        {
            var soLineListResponse = request.type switch
            {
                GetSoLineType.All => await _sender.Send(new GetAllSoLinesBySoIdQuery(request.SalesOrderId, true), cancellationToken),
                GetSoLineType.Open => await _sender.Send(new GetOpenSoLinesBySoIdQuery(request.SalesOrderId), cancellationToken),
                GetSoLineType.Closed => await _sender.Send(new GetClosedSoLinesBySoIdQuery(request.SalesOrderId), cancellationToken),
                _ => throw new ArgumentException("Invalid Type of GetSoLine")
            };
            return soLineListResponse.Success && soLineListResponse.Data != null ? soLineListResponse.Data : new List<AllOpenClosedSalesOrderLineDetailsDto>();
        }

        private async Task<IList<AdvisoryNoteDto>> GetManufacturerNotes(IReadOnlyList<AllOpenClosedSalesOrderLineDetailsDto> salesOrderLineList, int clientId, CancellationToken cancellationToken)
        {
            var mfrIDs = salesOrderLineList
                .Where(x => x.ManufacturerNo.HasValue)
                .Select(x => x.ManufacturerNo.ToString())
                .ToList();

            var listMfrNotesResponse = await _sender.Send(new GetAdvisoryNotesQuery()
            {
                Ids = string.Join(",", mfrIDs),
                ClientId = clientId,
            }, cancellationToken);

            return listMfrNotesResponse.Success && listMfrNotesResponse.Data != null
                ? listMfrNotesResponse.Data
                : new List<AdvisoryNoteDto>();
        }

        private async Task<IsIpoExistReadModel> GetIsIpoExistReadModelAsync(int salesOrderLineId)
        {
            var isIPOExistParameters = new List<SqlParameter>()
                    {
                        new SqlParameter("@SoLineId", SqlDbType.Int){ Value = salesOrderLineId },
                        new SqlParameter("@IsIPOExist", SqlDbType.Int) { Direction = ParameterDirection.Output },
                    };

            return await _isIpoExistRepository.SqlQueryRawReturnValueAsync(
                sql: $"{StoredProcedures.IsIPOAlreadyCreated} @SoLineId, @IsIPOExist Output",
                parameters: isIPOExistParameters.ToArray());
        }

        private async Task ProcessSoLineListResponse(IReadOnlyList<AllOpenClosedSalesOrderLineDetailsDto> salesOrderLineList,
            SalesOrderDetailsForLineCalculationsDto soDetails,
            IList<AdvisoryNoteDto> listMfrNotes,
            GetSoLineListDto responseData)
        {
            bool isConfirmedAll = true;
            double subTotal = 0;
            double subTotalRaw = 0;
            double tax = 0;
            bool isAnyLinePosted = false;
            foreach (var soLine in salesOrderLineList)
            {
                double lineTotal = soLine.Quantity * soLine.Price;
                double lineTax = soLine.IsLineTaxable ? lineTotal * (soDetails.TaxRate / 100) : 0;

                (var soLineDto, isConfirmedAll) = await CreateSoLineDto(soLine, soDetails, listMfrNotes, lineTotal, lineTax, isConfirmedAll);
                responseData.Items.Add(soLineDto);

                subTotalRaw += lineTotal + lineTax;
                if (soLine.Posted)
                {
                    subTotal += lineTotal;
                    tax += lineTax;
                    isAnyLinePosted = true;
                }
            }
            tax += soDetails.Freight * soDetails.TaxRate / 100;
            responseData.CanBePosted = soDetails.CreditLimit >= soDetails.Balance + subTotalRaw || soDetails.Paid;
            responseData.SubTotal = Functions.FormatCurrency(subTotal, CultureInfo.CurrentCulture, soDetails.CurrencyCode, 2, true);
            responseData.Freight = Functions.FormatCurrency(soDetails.Freight, CultureInfo.CurrentCulture, soDetails.CurrencyCode, 2, true);
            responseData.Tax = Functions.FormatCurrency(tax, CultureInfo.CurrentCulture, soDetails.CurrencyCode, 2, true);
            responseData.TotalVal = subTotal + tax + soDetails.Freight;
            responseData.Total = Functions.FormatCurrency(responseData.TotalVal, CultureInfo.CurrentCulture, soDetails.CurrencyCode, 2, true);
            responseData.AnyLinePosted = isAnyLinePosted;
            responseData.TotalFreight = soDetails.Freight + soDetails.Freight * soDetails.TaxRate / 100;
            responseData.TaxRate = soDetails.TaxRate / 100;
            responseData.IsConfirmedAll = isConfirmedAll;
        }

        private async Task<(SoLineDto dto, bool isConfirmedAll)> CreateSoLineDto(AllOpenClosedSalesOrderLineDetailsDto soLine,
        SalesOrderDetailsForLineCalculationsDto soDetails,
        IList<AdvisoryNoteDto> listMfrNotes,
        double lineTotal,
        double lineTax,
        bool isConfirmedAll)
        {
            var soLineDto = new SoLineDto();

            SetBasicProperties(soLineDto, soLine, soDetails, lineTotal, lineTax);
            SetManufacturerInfo(soLineDto, soLine, listMfrNotes);
            SetShippingInfo(soLineDto, soLine);
            await SetIPOInfo(soLineDto, soLine);
            SetPostingInfo(soLineDto, soLine, soDetails, lineTotal, lineTax);

            bool updatedIsConfirmedAll = UpdateConfirmationStatus(isConfirmedAll, soLine);
            return (soLineDto, updatedIsConfirmedAll);
        }

        private static void SetBasicProperties(SoLineDto soLineDto, AllOpenClosedSalesOrderLineDetailsDto soLine,
            SalesOrderDetailsForLineCalculationsDto soDetails, double lineTotal, double lineTax)
        {
            soLineDto.LineId = soLine.SalesOrderLineId;
            soLineDto.ROHS = soLine.ROHS;
            soLineDto.CustomerPart = soLine.CustomerPart;
            soLineDto.Part = soLine.Part;
            soLineDto.DC = soLine.DateCode;
            soLineDto.Product = soLine.ProductDescription;
            soLineDto.DutyCodeAndRate = $"{soLine.DutyCode} ({"0.00000"})";
            soLineDto.Package = soLine.PackageName;
            soLineDto.Quantity = Functions.FormatNumeric(soLine.Quantity, CultureInfo.CurrentCulture);
            soLineDto.ECCNCode = soLine.ECCNCode;
            soLineDto.ECCNCodeNo = soLine.ECCNCodeNo;
            soLineDto.ServiceNo = soLine.ServiceNo;
            soLineDto.IsIPO = soLine.IsIPO;
            soLineDto.IsChecked = soLine.IsChecked;
            soLineDto.Closed = soLine.Closed;
            soLineDto.IsPosted = soLine.Posted;
            soLineDto.IsAllocated = soLine.IsAllocated;
            soLineDto.IsShipped = soLine.IsShipped;
            soLineDto.Inactive = soLine.Inactive;
            soLineDto.Price = Functions.FormatCurrency(soLine.Price, CultureInfo.CurrentCulture, soDetails.CurrencyCode, 5, false);
            soLineDto.Total = Functions.FormatCurrency(lineTotal, CultureInfo.CurrentCulture, soDetails.CurrencyCode, 2, true);
            soLineDto.TotalRaw = lineTotal;
            soLineDto.Tax = Functions.FormatCurrency(lineTax, CultureInfo.CurrentCulture, soDetails.CurrencyCode, 2, true);
            soLineDto.TaxRaw = lineTax;
            soLineDto.CanBePosted = soDetails.Paid || soDetails.CreditLimit >= soDetails.Balance + lineTotal + lineTax;
            soLineDto.LineNo = Functions.FormatNumeric(soLine.SOSerialNo ?? 0, CultureInfo.CurrentCulture);
            soLineDto.SourcingResultNo = soLine.SourcingResultNo;
            soLineDto.IsFromIPO = soLineDto.SourcingResultNo > 0;
            soLineDto.IsIPOHeaderCreated = Convert.ToBoolean(soLine.IsIPOHeaderCreated);
            soLineDto.InternalPurchaseOrderNumber = soLine.InternalPurchaseOrderNumber;
            soLineDto.IsClone = soLine.ClonedId.HasValue;
            soLineDto.IsConfirmed = soLine.IsConfirmed;
            soLineDto.DateConfirmed = Functions.FormatDate(soLine.DateConfirmed, false, false, CultureInfo.CurrentCulture);
            soLineDto.PromiseReasonNo = soLine.PromiseReasonNo;
            soLineDto.PromiseReason = soLine.PromiseReason;
            soLineDto.WarehouseNo = soLine.WarehouseNo;
            soLineDto.WarehouseName = soLine.WarehouseName;
            soLineDto.AS6081 = soLine.AS6081;
        }

        private static void SetManufacturerInfo(SoLineDto soLineDto, AllOpenClosedSalesOrderLineDetailsDto soLine,
            IList<AdvisoryNoteDto> listMfrNotes)
        {
            string manufactureCode = GetManufacturerCode(soLine);
            string mfrAdvisoryNotes = GetManufacturerAdvisoryNotes(soLine, listMfrNotes);

            soLineDto.Mfr = manufactureCode;
            soLineDto.MfrNo = soLine.ManufacturerNo;
            soLineDto.MfrAdvisoryNotes = Functions.ReplaceLineBreaks(mfrAdvisoryNotes);
        }

        private static string GetManufacturerCode(AllOpenClosedSalesOrderLineDetailsDto soLine)
        {
            return soLine.RestrictedMfrNo > 0 && !Convert.ToBoolean(soLine.RestrictedMfrInactive)
                ? $"<span style=\"color: red !important;\">{soLine.ManufacturerCode ?? string.Empty}</span>"
                : soLine.ManufacturerCode ?? string.Empty;
        }

        private static string GetManufacturerAdvisoryNotes(AllOpenClosedSalesOrderLineDetailsDto soLine,
            IList<AdvisoryNoteDto> listMfrNotes)
        {
            return soLine.ManufacturerNo == null ? string.Empty
                : listMfrNotes.ToList().Find(mfrNote => mfrNote.ManufacturerId == soLine.ManufacturerNo)?.AdvisoryNotes ?? string.Empty;
        }

        private static void SetShippingInfo(SoLineDto soLineDto, AllOpenClosedSalesOrderLineDetailsDto soLine)
        {
            if (soLine.IsService)
            {
                soLineDto.Shipped = soLine.ServiceShipped ? Functions.FormatNumeric(soLine.Quantity, CultureInfo.CurrentCulture) : "0";
                soLineDto.Allocated = Functions.FormatNumeric(soLine.Quantity, CultureInfo.CurrentCulture);
                soLineDto.BackOrder = "0";
            }
            else
            {
                soLineDto.Shipped = Functions.FormatNumeric(soLine.QuantityShipped, CultureInfo.CurrentCulture);
                soLineDto.Allocated = Functions.FormatNumeric(soLine.QuantityAllocated, CultureInfo.CurrentCulture);
                soLineDto.BackOrder = Functions.FormatNumeric(soLine.BackOrderQuantity, CultureInfo.CurrentCulture);
            }

            soLineDto.PartialAllocated = soLine.QuantityAllocated < soLine.Quantity && soLine.QuantityAllocated > 0;
        }

        private async Task SetIPOInfo(SoLineDto soLineDto, AllOpenClosedSalesOrderLineDetailsDto soLine)
        {
            if (soLine.IsIPO != true) return;

            var IsIpoExistReadModel = await GetIsIpoExistReadModelAsync(soLine.SalesOrderLineId);

            if (IsIpoExistReadModel.IsIPOExist == 0)
            {
                soLineDto.IsSourcingResultExist = 1;
                soLineDto.IsIPOCreatedForCurrentLine = false;
            }
            else
            {
                soLineDto.IsSourcingResultExist = 0;
                soLineDto.IsIPOCreatedForCurrentLine = true;
            }
        }

        private static void SetPostingInfo(SoLineDto soLineDto, AllOpenClosedSalesOrderLineDetailsDto soLine,
            SalesOrderDetailsForLineCalculationsDto soDetails, double lineTotal, double lineTax)
        {
            bool canBePosted = soDetails.Paid || soDetails.CreditLimit >= soDetails.Balance + lineTotal + lineTax;

            if (!canBePosted)
            {
                soLine.PostDisableReason = string.IsNullOrEmpty(soLine.PostDisableReason)
                    ? " Not paid or low balance"
                    : soLine.PostDisableReason + ",Not paid or low balance";
            }

            soLineDto.PostDisableReason = soLine.PostDisableReason;
        }

        private static bool UpdateConfirmationStatus(bool isConfirmedAll, AllOpenClosedSalesOrderLineDetailsDto soLine)
        {
            return isConfirmedAll && (soLine.IsConfirmed ?? false);
        }
    }
}