namespace GlobalTrader2.Orders.UserCases.Orders.BOM.UpdateCustRequirementByBomID.Commands;

public class UpdateCustRequirementByBomIDValidator : AbstractValidator<UpdateCustRequirementByBomIDCommand>
{
    public UpdateCustRequirementByBomIDValidator()
    {
        RuleFor(x => x.BomId)
            .GreaterThan(0);

        RuleFor(x => x.UpdatedBy)
            .GreaterThan(0)
            .When(x => x.UpdatedBy.HasValue);

        RuleFor(x => x.ClientNo)
            .GreaterThan(0)
            .When(x => x.ClientNo.HasValue);

        RuleFor(x => x.ReqIds)
            .NotEmpty()
            .When(x => x.ReqIds != null);

        RuleFor(x => x.BOMStatus)
            .GreaterThanOrEqualTo(0)
            .When(x => x.BOMStatus.HasValue);
    }
}