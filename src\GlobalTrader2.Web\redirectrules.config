﻿<rules>
	<!--Redirect from V1 to V2-->
	<rule name="Setup Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Setup\.aspx)" />
		<action type="Rewrite" url="/Settings" />
	</rule>
	<rule name="Security Users Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_Sec_Users\.aspx)" />
		<action type="Rewrite" url="/Settings/SecuritySettings/SecurityUsers" />
	</rule>
	<rule name="Security Groups Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_Sec_Groups\.aspx)" />
		<action type="Rewrite" url="/Settings/SecuritySettings/SecurityGroups" />
	</rule>
	<rule name="Global Security Groups Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_Global_Sec_Groups\.aspx)" />
		<action type="Rewrite" url="/Settings/SecuritySettings/GlobalSecurityGroups" />
	</rule>
	<rule name="Application Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_AppSettings\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/ApplicationSettings" />
	</rule>
	<rule name="Terms Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Terms\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/Terms" />
	</rule>
	<rule name="Teams Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Team\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/Teams" />
	</rule>
	<rule name="Warehouses Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Warehouse\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/Warehouses" />
	</rule>
	<rule name="Local Currency Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_LocalCurrency\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/LocalCurrency" />
	</rule>
	<rule name="Printer Setting Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Printer\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/Printer" />
	</rule>
	<rule name="Sequence Numbers Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_SeqNumber\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/SequenceNumbers" />
	</rule>
	<rule name="CommunicationLogTypes Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_CommunicationLogType\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/CommunicationLogType" />
	</rule>
	<rule name="Master Application Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_AppSettings\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/MasterAppSettings" />
	</rule>
	<rule name="Master Country List Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_MstCountryList\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/MasterCountry" />
	</rule>
	<rule name="CompanyTypes Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_CompanyType\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/CompanyType" />
	</rule>
	<rule name="IndustryType Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_IndustryType\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/IndustryType" />
	</rule>
	<rule name="Bulk Invoice Email Composer Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_InvoiceEmailComposer\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/BulkInvoiceEmailComposer" />
	</rule>
	<rule name="Counting Methods Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_CountingMethod\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/CountingMethod" />
	</rule>
	<rule name="Close Reasons Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_Reason\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/CloseReason" />
	</rule>
	<rule name="Document File Size Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_PDFDocumentFileSize\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/DocumentSize" />
	</rule>
	<rule name="Master Taxes Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Global_Tax\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/MasterTax" />
	</rule>
	<rule name="Incoterm Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_Incoterm\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/Incoterm" />
	</rule>
	<rule name="Package Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_Package\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/Package" />
	</rule>
	<rule name="AS6081 Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_AS6081\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/AS6081" />
	</rule>
	<rule name="Certificate Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_Certificate\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/Certificate" />
	</rule>
	<rule name="Root Cause Code Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_EightDCode\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/RootCauseCode" />
	</rule>
	<rule name="Master Status Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_SetupList\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/MasterStatus" />
	</rule>
	<rule name="Warnings Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Warnings\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/Warnings" />
	</rule>
	<rule name="PPV/ BOM Qualification Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_PPVBOMQualification\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/PpvBomQualification" />
	</rule>
	<rule name="OGEL Licenses Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_OGELLicenses\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/OGELLicenses" />
	</rule>
	<rule name="Restricted Manufacture Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_RestrictedManufacture\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/RestrictedManufacturer" />
	</rule>
	<rule name="Profile Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Profile\.aspx)" />
		<action type="Rewrite" url="/Profile" />
	</rule>
	<rule name="Edit Profile Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Prf_ProfileEdit\.aspx)" />
		<action type="Rewrite" url="/Profile/EditMyProfile" />
	</rule>
	<rule name="Client Invoice Header Setting Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_ClientInvoiceHeader\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/ClientInvoiceHeader" />
	</rule>
	<rule name="Divisions Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Division\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/Divisions" />
	</rule>
	<rule name="Countries Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Country\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/Countries" />
	</rule>
	<rule name="ToDoListType Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_ToDoListType\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/ToDoListType" />
	</rule>
	<rule name="Mail Groups Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_MailGroups\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/MailGroups" />
	</rule>
	<rule name="Client Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_Client\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/Client" />
	</rule>
	<rule name="Global Product Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_Products\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/GlobalProduct" />
	</rule>
	<rule name="To Do List Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Prf_ToDo\.aspx)" />
		<action type="Rewrite" url="/Profile/ToDo" />
	</rule>
	<rule name="Mail Messages Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Prf_MailMessages\.aspx)" />
		<action type="Rewrite" url="/Profile/MailMessages" />
	</rule>
	<rule name="Master Login Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_MasterLogin\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/MasterLogin" />
	</rule>
	<rule name="Stock Log Reason Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_StockLogReason\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/StockLogReason" />
	</rule>
	<rule name="Products Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Product\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/Products" />
	</rule>
	<rule name="Sourcing Links Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_SourcingLinks\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/SourcingLinks" />
	</rule>
	<rule name="Master Currency List Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_MstCurrencyList\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/MasterCurrency" />
	</rule>
	<rule name="ECCN Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_ECCN\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/ECCN" />
	</rule>
	<rule name="Shipping Methods Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_ShipMethod\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/ShippingMethods" />
	</rule>
	<rule name="Taxes Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Tax\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/Taxes" />
	</rule>
	<rule name="Currencies Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_Currency\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/Currencies" />
	</rule>
	<rule name="Star Rating Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_StarRating\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/StarRating" />
	</rule>
	<rule name="Printed Documents Settings Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_CD_PrintedDocuments\.aspx)" />
		<action type="Rewrite" url="/Settings/CompanySettings/PrintedDocuments" />
	</rule>
	<rule name="Entertaiment Type Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Set_GS_EntertainmentType\.aspx)" />
		<action type="Rewrite" url="/Settings/GlobalSettings/EntertainmentType" />
	</rule>
	<rule name="Orders Sourcing Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_Sourcing\.aspx)" />
		<action type="Rewrite" url="/Orders/Sourcing" />
	</rule>
	<rule name="Contact Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Contact\.aspx)" />
		<action type="Rewrite" url="/Contact" appendQueryString="true" />
	</rule>
	<rule name="Contacts Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Con_ContactBrowse\.aspx)" />
		<action type="Rewrite" url="/Contact/Contacts" appendQueryString="true" />
	</rule>
	<rule name="Contact Details Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Con_ContactDetail\.aspx)" />
		<action type="Rewrite" url="/Contact/Contacts/Details" appendQueryString="true" />
	</rule>
	<rule name="Manufacturers Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Con_ManufacturerBrowse\.aspx)" />
		<action type="Rewrite" url="/Contact/Manufacturers" appendQueryString="true" />
	</rule>
	<rule name="Manufacturer Details Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Con_ManufacturerDetail\.aspx)" />
		<action type="Rewrite" url="/Contact/Manufacturers/Details" appendQueryString="true" />
	</rule>
	<rule name="Add Manufacturer Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Con_ManufacturerAdd\.aspx)" />
		<action type="Rewrite" url="/Contact/Manufacturers/AddNewManufacturer" appendQueryString="true" />
	</rule>
	<rule name="Add new Requirement Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_CusReqAdd\.aspx)" />
		<action type="Rewrite" url="/Orders/CustomerRequirement/AddNewRequirement" />
	</rule>
	<rule name="Requirement Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_CusReqBrowse\.aspx)" />
		<action type="Rewrite" url="/Orders/CustomerRequirement" />
	</rule>
	<rule name="Requirement Detail Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_CusReqDetail\.aspx)" />
		<action type="Rewrite" url="/Orders/CustomerRequirement/Details" />
	</rule>
	<rule name="Add in Manufacturer Group Code Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Con_ManufacturerGroup\.aspx)" />
		<action type="Rewrite" url="/Contact/Manufacturers/AddInGroupCode" />
	</rule>
	<rule name="All Companies Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Con_CompanyBrowse\.aspx)" />
		<action type="Rewrite" url="/Contact/AllCompanies" appendQueryString="true"/>
	</rule>

	<rule name="Company Details Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Con_CompanyDetail\.aspx)" />
		<action type="Rewrite" url="/Contact/AllCompanies/Details" appendQueryString="true" />
	</rule>

	<rule name="Add New Company Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Con_CompanyAdd\.aspx)" />
		<action type="Rewrite" url="/Contact/AllCompanies/AddCompany" appendQueryString="true" />
	</rule>
	
	<!--<rule name="Browse Sales Orders Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_SOBrowse\.aspx)" />
		<action type="Rewrite" url="/orders/salesorders" appendQueryString="true" />
	</rule>-->

	<!--<rule name="Sales Order Details Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_SODetail\.aspx)" />
		<action type="Rewrite" url="/orders/salesorders/details" appendQueryString="true" />
	</rule>-->

	<!--<rule name="Add New Sales Orders Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_SOAdd\.aspx)" />
		<action type="Rewrite" url="/Orders/SalesOrders/Add" appendQueryString="true" />
	</rule>-->

	<!--<rule name="Browse Orders Purchase Requisition Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_PurReqBrowse\.aspx)" />
		<action type="Rewrite" url="/orders/purchaserequisition" appendQueryString="true" />
	</rule>-->

	<!--<rule name="Orders Purchase Requisition Details Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_PurReqDetail\.aspx)" />
		<action type="Rewrite" url="/Orders/PurchaseRequisition/Details" appendQueryString="true" />
	</rule>-->

	<!--<rule name="HUBRFQs Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_BOMBrowse\.aspx)" />
		<action type="Rewrite" url="/Orders/HUBRFQ" appendQueryString="true" />
	</rule>-->

	<rule name="HUBRFQs Detail Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_BOMDetail\.aspx)" />
		<action type="Rewrite" url="/Orders/HUBRFQ/Details" appendQueryString="true" />
	</rule>

	<rule name="Add New HUBRFQ Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_BOMAdd\.aspx)" />
		<action type="Rewrite" url="/Orders/HUBRFQ/AddNewHUBRFQ" appendQueryString="true" />
	</rule>

	<rule name="Purchase Orders Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_POBrowse\.aspx)" />
		<action type="Rewrite" url="/Orders/PurchaseOrder" appendQueryString="true" />
	</rule>

	<rule name="Purchase Orders Details Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_PODetail\.aspx)" />
		<action type="Rewrite" url="/Orders/PurchaseOrder/Details" appendQueryString="true" />
	</rule>

	<rule name="Internal Purchase Orders Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_InternalPOBrowse\.aspx)" />
		<action type="Rewrite" url="/Orders/InternalPurchaseOrder" appendQueryString="true" />
	</rule>
	<rule name="Internal Purchase Orders Details Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_InternalPODetail\.aspx)" />
		<action type="Rewrite" url="/Orders/InternalPurchaseOrder/Details" appendQueryString="true" />
	</rule>
	<rule name="Price Request Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_POQuoteBrowse\.aspx)" />
		<action type="Rewrite" url="/Orders/PoQuote" appendQueryString="true" />
	</rule>
	<rule name="Orders HubRFQ Home (Ord_BOMBrowse.aspx) Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_BOMBrowse\.aspx)" />
		<action type="Rewrite" url="/Orders/HUBRFQ" appendQueryString="true" />
	</rule>
	<rule name="New Orders Price Request (Ord_POQuoteAdd.aspx) Page Rewrite" stopProcessing="true">
		<match url=".*?([^/]*Ord_POQuoteAdd\.aspx)" />
		<action type="Rewrite" url="/Orders/POQuote/Add" appendQueryString="true" />
	</rule>
	<!--Redirect from V2 to V1-->
	<rule name="Redirect to Root" stopProcessing="true">
		<match url="^(?:.*/)?(.*?\.aspx.*)$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/{R:1}?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="ASPX Domain Redirect" stopProcessing="true">
		<match url="(.*?\.aspx.*)$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/{R:0}?redirect_from=v2" appendQueryString="true" redirectType="Temporary"/>
	</rule>
	<rule name="Redirect Index Home Page" stopProcessing="true">
		<match url="^Index$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Default.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary"/>
	</rule>
	<rule name="Redirect Root Home Page" stopProcessing="true">
		<match url="^$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Default.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary"/>
	</rule>
	<!--<rule name="Redirect Contact Page" stopProcessing="true">
					<match url="^contact$" />
					<action type="Redirect" url="#{GTv1SiteUrl}#/Contact.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
				</rule>-->
	<!--<rule name="Redirect Contact Companies Page" stopProcessing="true">
					<match url="^Contact/AllCompanies$" />
					<action type="Redirect" url="#{GTv1SiteUrl}#/Con_CompanyBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
				</rule>-->
	<!--<rule name="Redirect Contact Company Detail Page" stopProcessing="true">
					<match url="^contact/AllCompanies/detail$" />
					<action type="Redirect" url="#{GTv1SiteUrl}#/Con_CompanyDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
				</rule>-->
	<!--<rule name="Redirect Contact Manufacturers Page" stopProcessing="true">
					<match url="^contact/manufacturers$" />
					<action type="Redirect" url="#{GTv1SiteUrl}#/Con_ManufacturerBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
				</rule>-->
	<!--<rule name="Redirect Contact Contacts Page" stopProcessing="true">
					<match url="^contact/contacts$" />
					<action type="Redirect" url="#{GTv1SiteUrl}#/Con_ContactBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
				</rule>-->

	<rule name="Redirect Orders Page" stopProcessing="true">
		<match url="^orders$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Orders.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<!--<rule name="Redirect Orders Customer Requirement Page" stopProcessing="true">
					<match url="^orders/customerrequirement$" />
					<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_CusReqBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
				</rule>-->
	<rule name="Redirect Orders Quote Page" stopProcessing="true">
		<match url="^orders/quotes$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_QuoteBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Quote Detail Page" stopProcessing="true">
		<match url="^orders/quotes/details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_QuoteDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect All Document Information Page" stopProcessing="true">
		<match url="^alldocument$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/AllDocumentInformation.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Quote Add Page" stopProcessing="true">
		<match url="^orders/quotes/add$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_QuoteAdd.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Print Page" stopProcessing="true">
		<match url="^orders/CusReqPrint$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_CustReqPrint.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Invoice Page" stopProcessing="true">
		<match url="^orders/invoice$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_InvoiceBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Invoice Add Page" stopProcessing="true">
		<match url="^Orders/Invoice/Add$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_InvoiceAdd.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<!--<rule name="Redirect Orders Purchase Order Page" stopProcessing="true">
		<match url="^orders/purchaseorder$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_POBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>-->
	<rule name="Redirect Orders Purchase Order Add Page" stopProcessing="true">
		<match url="^orders/purchaseorder/add$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_POAdd.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Customer RMA Page" stopProcessing="true">
		<match url="^orders/customerrma$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_CRMABrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Customer RMA Add Page" stopProcessing="true">
		<match url="^orders/customerrma/add$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_CRMAAdd.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Supplier RMA Page" stopProcessing="true">
		<match url="^orders/supplierrma$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_SRMABrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Supplier RMA Add Page" stopProcessing="true">
		<match url="^orders/supplierrma/add$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_SRMAAdd.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Credit Note Page" stopProcessing="true">
		<match url="^orders/creditnote$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_CreditNoteBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Credit Note Add Page" stopProcessing="true">
		<match url="^orders/creditnote/add$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_CreditNoteAdd.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Debit Note Page" stopProcessing="true">
		<match url="^orders/debitnote$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_DebitNoteBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Debit Note Add Page" stopProcessing="true">
		<match url="^orders/debitnote/add$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_DebitNoteAdd.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<!--<rule name="Redirect Orders Internal PO Page" stopProcessing="true">
		<match url="^orders/InternalPurchaseOrder" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_InternalPOBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>-->
	<rule name="Redirect Orders Client Invoice Page" stopProcessing="true">
		<match url="^orders/clientinvoice$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_ClientInvoiceBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Division Page" stopProcessing="true">
		<match url="^orders/division$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/KPI_DivisionDetails.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Team Page" stopProcessing="true">
		<match url="^orders/team$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/KPI_TeamDetails.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Sale Detail Page" stopProcessing="true">
		<match url="^orders/saledetail$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/KPI_SalesDetails.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Bom Search Page" stopProcessing="true">
		<match url="^orders/bomsearch$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/BOMSearch.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Orders Prospectivecs Page" stopProcessing="true">
		<match url="^orders/prospectivecs$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_ProspectiveCSBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Page" stopProcessing="true">
		<match url="^warehouse$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Warehouse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Receive PO Page" stopProcessing="true">
		<match url="^warehouse/receivepo$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_ReceivePOBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Ship SO Page" stopProcessing="true">
		<match url="^warehouse/shipso$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_ShipSOBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Receive CRMA Page" stopProcessing="true">
		<match url="^warehouse/receivecrma$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_ReceiveCRMABrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Ship SRMA Page" stopProcessing="true">
		<match url="^warehouse/shipsrma$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_ShipSRMABrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Stock Page" stopProcessing="true">
		<match url="^warehouse/stock$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_StockBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Services Page" stopProcessing="true">
		<match url="^warehouse/services$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_ServiceBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Services Details Page" stopProcessing="true">
		<match url="^warehouse/services/details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_ServiceDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Lots Page" stopProcessing="true">
		<match url="^warehouse/lots$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_LotBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Lots Details Page" stopProcessing="true">
		<match url="^warehouse/lots/details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_LotDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Supplier Invoice Page" stopProcessing="true">
		<match url="^warehouse/supplierinvoice$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_SupplierInvoiceBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Supplier Invoice Add Page" stopProcessing="true">
		<match url="^warehouse/supplierinvoice/add$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_SupplierInvoiceAdd.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Npr Page" stopProcessing="true">
		<match url="^warehouse/npr$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_NPRBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Ihs Catalogue Page" stopProcessing="true">
		<match url="^warehouse/ihscatalogue$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_IHSBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Short Shipment Page" stopProcessing="true">
		<match url="^warehouse/shortshipment$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_ShortShipmentBrowse.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Short Shipment Details Page" stopProcessing="true">
		<match url="^warehouse/shortshipment/details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_ShortShipmentDetails.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Ogel Line Page" stopProcessing="true">
		<match url="^warehouse/ogelline$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_OGELLinesExport.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Warehouse Stock Details Page" stopProcessing="true">
		<match url="^Warehouse/Stock/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_StockDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Reports Page" stopProcessing="true">
		<match url="^reports$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Reports.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Bom Import Page" stopProcessing="true">
		<match url="^utility/BOMImport$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_BOMImport.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Offer Import Page" stopProcessing="true">
		<match url="^utility/OfferImport$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_OfferImport.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Xmatch Page" stopProcessing="true">
		<match url="^utility/Xmatch$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_XMatch.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Hub Offer Import Page" stopProcessing="true">
		<match url="^utility/HUBOfferImport$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_HUBOfferImport.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Stock Import Page" stopProcessing="true">
		<match url="^utility/StockImport$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_StockImport.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Log Import Page" stopProcessing="true">
		<match url="^utility/LogImport$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_LogImport.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Reverse Logistics Import Page" stopProcessing="true">
		<match url="^utility/ReverseLogisticsImport$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_ReverseLogisticsImport.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Bom Manager Import Page" stopProcessing="true">
		<match url="^utility/BOMManagerImport$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_BOMManagerImport.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Price Quote Import Page" stopProcessing="true">
		<match url="^utility/PriceQuoteImport$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_PriceQuoteImport.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Alternative Import Page" stopProcessing="true">
		<match url="^utility/AlternativeImport$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_AlternativeImport.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Hub Offer Import Large Page" stopProcessing="true">
		<match url="^utility/HUBOfferImportLarge$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_HUBOfferImportLarge.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Utility Email Campaign Page" stopProcessing="true">
		<match url="^utility/EmailCampaign" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Utility_EmailCampaign.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<!--<rule name="Redirect Customer Requirement Details Page" stopProcessing="true">
		<match url="^Orders/CustomerRequirement/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_CusReqDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>-->
	<rule name="Redirect Quote Details Page" stopProcessing="true">
		<match url="^orders/quotes/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_QuoteDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Invoice Details Page" stopProcessing="true">
		<match url="^Orders/Invoice/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_InvoiceDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Client Invoive Details Page" stopProcessing="true">
		<match url="^Orders/ClientInvoice/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_ClientInvoiceDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<!--<rule name="Redirect Purchase Order Details Page" stopProcessing="true">
		<match url="^Orders/PurchaseOrder/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_PODetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>-->
	<rule name="Redirect Customer RMA Details Page" stopProcessing="true">
		<match url="^Orders/CustomerRMA/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_CRMADetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Supplier RMA Details Page" stopProcessing="true">
		<match url="^Orders/SupplierRMA/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_SRMADetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Credit Note Details Page" stopProcessing="true">
		<match url="^Orders/CreditNote/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_CreditNoteDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Debit Note Details Page" stopProcessing="true">
		<match url="^Orders/DebitNote/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_DebitNoteDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<!--<rule name="Redirect Internal Purchase Order Details Page" stopProcessing="true">
		<match url="^Orders/InternalPurchaseOrder/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_InternalPODetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>-->
	<rule name="Redirect Goods In Details Page" stopProcessing="true">
		<match url="^Warehouses/GoodsIn/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_GIDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Redirect Supplier Invoice Details Page" stopProcessing="true">
		<match url="^Warehouses/SupplierInvoice/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Whs_SupplierInvoiceDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
  <rule name="Goods In Page Rewrite" stopProcessing="true">
    <match url=".*?([^/]*Whs_GIBrowse\.aspx)" />
    <action type="Rewrite" url="/Warehouse/GoodsIn" />
  </rule>
	<!--Enable for release July-->
	<!--<rule name="Hub RFQ Details Page" stopProcessing="true">
		<match url="^Orders/HUBRFQ/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_BOMDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>-->
	<!--<rule name="Redirect Orders HUBRFQ Add New Page" stopProcessing="true">
		<match url="^Orders/HUBRFQ/AddNewHUBRFQ$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_BOMAdd.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>-->
	<rule name="Orders Quote Details Page" stopProcessing="true">
		<match url="^Orders/Quote/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_QuoteDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="Orders Price Request Details Page" stopProcessing="true">
		<match url="^Orders/PriceRequest/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_POQuoteDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
	<rule name="BOM Details Page" stopProcessing="true">
		<match url="^Orders/BomSearch/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/ord_BOMManagerDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />	  
	</rule>
	<rule name="Orders Purchase Quote Details Page" stopProcessing="true">
		<match url="^Orders/PoQuote/Details$" />
		<action type="Redirect" url="#{GTv1SiteUrl}#/Ord_POQuoteDetail.aspx?redirect_from=v2" appendQueryString="true" redirectType="Temporary" />
	</rule>
</rules>