﻿using AutoMapper;
using GlobalTrader2.Aggregator.UseCases.DataList.AutoSearchForPOApproveSupplier.SearchForPOApproveSupplier.Queries;
using GlobalTrader2.Aggregator.UseCases.DataList.AutoSearchLyticaManufacturers.Queries;
using GlobalTrader2.Aggregator.UseCases.DataList.BuyForClientAndGlobal.Queries;
using GlobalTrader2.Aggregator.UseCases.DataList.CertificateCategoryDropdown;
using GlobalTrader2.Aggregator.UseCases.DataList.DropDownProductSource;
using GlobalTrader2.Aggregator.UseCases.DataList.DropDownStatusReason;
using GlobalTrader2.Aggregator.UseCases.DataList.GetActiveCommunicationLogType;
using GlobalTrader2.Aggregator.UseCases.DataList.GetAddressesDropDown;
using GlobalTrader2.Aggregator.UseCases.DataList.GetBOMList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetBuyCurrencyForClientList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetBuyCurrencyList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetBuyTermsForCompany;
using GlobalTrader2.Aggregator.UseCases.DataList.GetContactList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetCountryForClient;
using GlobalTrader2.Aggregator.UseCases.DataList.GetDivisionsList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetEmployeeList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetIhsPartData;
using GlobalTrader2.Aggregator.UseCases.DataList.GetIncotermsList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetMSLList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetOGELForClientList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetPurchaseHubEmployeeList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetRegionList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetRequirementData;
using GlobalTrader2.Aggregator.UseCases.DataList.GetRohsStatusList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetSalesOrderStatus;
using GlobalTrader2.Aggregator.UseCases.DataList.GetSellCurrencyForClientList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetSellCurrencyList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetSellIncotermsForClientList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetShipViaForClientList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetTaxesList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetUsageList;
using GlobalTrader2.Aggregator.UseCases.DataList.GetViewLevelByPageId;
using GlobalTrader2.Aggregator.UseCases.DataList.GetWarehouseForClientList;
using GlobalTrader2.Aggregator.UseCases.DataList.Reason.Reason.Queries;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.SaleCompany.Queries;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetDropDownForCRMProspects;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetManufacturerGroupBySupplier;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.GetContactGroups;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.Base;
using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Dto.CommunicationLogType;
using GlobalTrader2.Dto.Contacts;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.IhsPartDetail;
using GlobalTrader2.Dto.Manufacturers;
using GlobalTrader2.Dto.OGEL;
using GlobalTrader2.Dto.Warehouses;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQClient.Queries;
using GlobalTrader2.Settings.UseCases.Countries.Queries;
using GlobalTrader2.Settings.UseCases.GlobalSettings.CompanyType.Queries;
using GlobalTrader2.Settings.UseCases.SecuritySettings.SecurityUsers.Queries.GetActiveSecurityUsers;
using GlobalTrader2.SharedUI.Bases;
using GlobalTrader2.SharedUI.Constants;
using GlobalTrader2.SharedUI.Models;
using GlobalTrader2.SharedUI.Services;
using GlobalTrader2.SharedUI.ViewModels;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using GlobalTrader2.Core.Constants;

namespace GlobalTrader2.SharedUI.Areas.Common.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/lists")]
    public class DataListController : ApiBaseController
    {
        private readonly ISender _sender;
        private readonly IStringLocalizer<Misc> _miscLocalizer;
        private readonly IMapper _mapper;
        private readonly SessionManager _sessionManager;
        public DataListController(ISender sender, IStringLocalizer<Misc> miscLocalizer, IMapper mapper, SessionManager sessionManager)
        {
            _sender = sender;
            _miscLocalizer = miscLocalizer;
            _mapper = mapper;
            _sessionManager = sessionManager;
        }

        [HttpGet("divisions")]
        public async Task<IActionResult> GetDivisionsAsync([FromQuery] int? globalLoginClientNo, [FromQuery] bool refreshData = false)
        {
            var query = new GetDivisionsListQuery(ClientId);
            query.ResetCache = refreshData;
            if (globalLoginClientNo.HasValue)
            {
                query.ClientNo = globalLoginClientNo > 0 ? globalLoginClientNo.Value :  ClientId;
            }

            var divisions = await _sender.Send(query);

            return Ok(divisions);
        }

        [HttpGet("divisions/company-details")]
        public async Task<IActionResult> GetDivisionsForCompanyDetailsAsync(int? selectedClientNo, int? globalLoginClientNo, bool refreshData = false)
        {
            int clientNo;
            if (globalLoginClientNo > 0)
                clientNo = globalLoginClientNo.Value;
            else if (selectedClientNo > 0)
                clientNo = selectedClientNo.Value;
            else
                clientNo = ClientId;

            var query = new GetDivisionsListQuery(clientNo);
            query.ResetCache = refreshData;

            var divisions = await _sender.Send(query);

            return Ok(divisions);
        }

        [HttpGet("taxes")]
        public async Task<IActionResult> GetTaxesAsync([FromQuery] int? globalLoginClientNo, [FromQuery] bool refreshData = false)
        {
            var query = new GetTaxesListQuery(ClientId);
            query.ResetCache = refreshData;
            if (globalLoginClientNo.HasValue)
            {
                query.ClientNo = globalLoginClientNo > 0 ? globalLoginClientNo.Value : ClientId;
            }

            var taxes = await _sender.Send(query);

            return Ok(taxes);
        }

        [HttpGet("incoterms")]
        public async Task<IActionResult> GetIncontermsAsync([FromQuery] bool refreshData = false)
        {
            var query = new GetIncotermsListQuery();
            query.ResetCache = refreshData;

            var divisions = await _sender.Send(query);

            return Ok(divisions);
        }

        [HttpGet("sell-incoterms-for-client")]
        public async Task<IActionResult> GetSellIncontermsForClientAsync([FromQuery] int? globalLoginClientNo, bool refreshData = false)
        {
            var clientNo = globalLoginClientNo ?? ClientId;
            var query = new GetSellIncotermsForClientListQuery(clientNo);
            query.ResetCache = refreshData;
            var result = await _sender.Send(query);
            return Ok(result);
        }

        [HttpGet("reqData")]
        public async Task<IActionResult> GetRequirementDropDownDataAsync([FromQuery] Stype stype, bool refreshData = false)
        {
            var query = new GetRequirementDataQuery(stype);
            query.ResetCache = refreshData;

            var reqData = await _sender.Send(query);

            return Ok(reqData);
        }

        [HttpGet("usage")]
        public async Task<IActionResult> GetUsageAsync([FromQuery] bool refreshData = false)
        {
            var query = new GetUsageListQuery();
            query.ResetCache = refreshData;

            var usage = await _sender.Send(query);

            return Ok(usage);
        }

        [HttpGet("contact/{companyId}")]
        public async Task<IActionResult> GetContactAsync(int companyId, [FromQuery] bool refreshData = false)
        {
            var query = new GetContactListQuery(companyId);
            query.ResetCache = refreshData;

            var contact = await _sender.Send(query);

            return Ok(contact);
        }

        [HttpGet("employee")]
        public async Task<IActionResult> GetEmployeeAsync([FromQuery] GetClientDropDownRequest? request, [FromQuery] bool refreshData = false)
        {
            var query = new GetEmployeeListQuery(ClientId, 0, 0, 0);
            query.ResetCache = refreshData;
            if (request != null)
            {
                query.ClientNo = request.GlobalLoginClientNo.HasValue && request.GlobalLoginClientNo > 0 ? request.GlobalLoginClientNo.Value : ClientId;
                query.TeamNo = request.LimitToCurrentUsersTeam ? LoginTeamID : 0;
                query.DivisionNo = request.LimitToCurrentUsersDivision ? LoginDivisionID : 0;
                query.ExcludeLoginNo = request.ExcludeCurrentUser ? UserId : 0;
            }

            var employee = await _sender.Send(query);

            return Ok(employee);
        }

        [HttpGet("employee/purchase-hub/{clientId}")]
        public async Task<IActionResult> GetPurchaseHubEmployeeAsync([FromRoute] int clientId, [FromQuery] GetClientDropDownRequest? request, [FromQuery] bool refreshData = false)
        {
            var query = new GetPurchaseHubEmployeeListQuery(clientId, 0, 0, 0);
            query.ResetCache = refreshData;
            if (request != null)
            {
                query.TeamNo = request.LimitToCurrentUsersTeam ? LoginTeamID : 0;
                query.DivisionNo = request.LimitToCurrentUsersDivision ? LoginDivisionID : 0;
                query.ExcludeLoginNo = request.ExcludeCurrentUser ? UserId : 0;
            }

            var employee = await _sender.Send(query);
            return Ok(employee);
        }

        [HttpGet("communicationType")]
        public async Task<IActionResult> GetActiveCommunicationType([FromQuery] bool includeNewSystemDocuments = false, [FromQuery] bool refreshData = false)
        {
            var query = new GetActiveCommunicationLogTypeQuery();
            query.ResetCache = refreshData;

            var communicationType = await _sender.Send(query);
            if (includeNewSystemDocuments)
            {
                var systemDocuments = Enum.GetValues(typeof(SystemDocument.ListForSequencer))
                                .Cast<SystemDocument.ListForSequencer>();
                foreach (var doc in systemDocuments)
                {
                    communicationType.Data = communicationType.Data?.Append(new CommunicationLogTypeDropdownDto
                    {
                        CommunicationLogTypeId = (int)doc,
                        Name = _miscLocalizer.GetString($"New{doc.ToString()}")
                    });
                }
            }
            return Ok(communicationType);
        }

        [HttpGet("sell-currencies/{companyId}")]
        public async Task<IActionResult> GetSellCurrenciesAsync(int companyId, [FromQuery] bool refreshData = false)
        {
            var query = new GetSellCurrencyListQuery(ClientId, companyId)
            {
                ResetCache = refreshData
            };

            var currencies = await _sender.Send(query);

            return Ok(currencies);
        }

        [HttpGet("sell-currencies-for-client")]
        public async Task<IActionResult> GetSellCurrenciesForClientAsync(int? globalClientLoginNo, bool refreshData = false)
        {
            var clientNo = globalClientLoginNo ?? ClientId;
            var query = new GetSellCurrencyForClientListQuery(clientNo)
            {
                ResetCache = refreshData
            };

            var currencies = await _sender.Send(query);

            return Ok(currencies);
        }

        [HttpGet("rohs-statuses")]
        public async Task<IActionResult> GetROHSStatusesAsync([FromQuery] bool refreshData = false)
        {
            var query = new GetRohsStatusListQuery()
            {
                ResetCache = refreshData
            };

            var rohsStatuses = await _sender.Send(query);

            return Ok(rohsStatuses);
        }

        //usp_dropdown_MSLLevel
        [HttpGet("msls")]
        public async Task<IActionResult> GetMSLLevelAsync([FromQuery] bool refreshData = false)
        {
            var query = new GetMSLListQuery();
            query.ResetCache = refreshData;

            var msls = await _sender.Send(query);

            return new JsonResult(_mapper.Map<BaseResponse<IEnumerable<SelectItemListViewModel>>>(msls));
        }

        //usp_dropdown_BOM_for_Client
        [HttpGet("boms")]
        public async Task<IActionResult> GetBomAsync([FromQuery] int companyNo, bool refreshData = false)
        {
            var query = new GetBOMListQuery()
            {
                ClientNo = ClientId,
                CompanyNo = companyNo
            };
            query.ResetCache = refreshData;

            var boms = await _sender.Send(query);

            return new JsonResult(_mapper.Map<BaseResponse<IEnumerable<SelectItemListViewModel>>>(boms));
        }

        [HttpGet("address-drop-down/{companyId}")]
        public async Task<IActionResult> GetAddressesDropDown(int companyId, [FromQuery] bool refreshData = false)
        {
            var response = await _sender.Send(new GetAddressesDropDownQuery(companyId)
            {
                ResetCache = refreshData
            });
            return Ok(response);
        }


        [HttpPost("part-detail")]
        public async Task<IActionResult> GetPartDetail([FromBody] IhsPartCreateRequest request)
        {
            var command = new SaveIhsPartCommand()
            {
                ClientId = ClientId,
                IhsResults = request.IhsResults,
                UpdatedBy = UserId,
            };

            var ihsPart = await _sender.Send(command);

            return Ok(ihsPart);
        }

        [HttpPost("search-ihs-part")]
        public async Task<IActionResult> GetIhsPartList([FromBody] IhsPartSearchRequest request)
        {
            var command = new InsertIhsPartListCommand()
            {
                ClientId = ClientId,
                UpdatedBy = UserId,
                PartSearch = request.PartSearch,
                SearchType = request.SearchType,
            };

            await _sender.Send(command);

            var query = new SearchIhsListQuery()
            {
                ClientId = ClientId,
                PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
                SearchType = request.SearchType,
                OrderBy = request.OrderBy ?? 1,
                SortDir = request.SortDir ?? 1,
                Size = (request.Size == 0 ? 10 : request.Size),
                Index = request.Index
            };

            var result = await _sender.Send(query);

            var totalRecords = result?.Data?.FirstOrDefault()?.RowCnt;

            var response = new DatatableResponse<IEnumerable<IhsPartDetailDto>>()
            {
                Success = result!.Success,
                Data = result.Data,
                RecordsTotal = totalRecords ?? 0,
                RecordsFiltered = totalRecords ?? 0,
                Draw = request.Draw
            };

            return Ok(response);
        }

        [HttpPost("search-ihs-part-detail")]
        public async Task<IActionResult> GetIhsPartDetail([FromBody] IhsPartDetailSearchRequest request)
        {
            var query = new SearchIhsDetailQuery()
            {
                ClientId = ClientId,
                IHSPartsId = request.IHSPartsId,
                MSLName = request.MSLName,
                MSLNo = request.MSLNo,
                Manufacturer = request.Manufacturer,
                IHSProdDesc = request.IHSProdDesc,
                Packaging = request.Packaging,
                HTSCode = request.HTSCode,
                IHSDutyCode = request.IHSDutyCode,
                CountryOfOrigin = request.CountryOfOrigin,
                CountryOfOriginNo = request.CountryOfOriginNo,
                PackagingSize = request.PackagingSize
            };

            var result = await _sender.Send(query);

            return Ok(result);
        }

        [HttpGet("buy-currencies/{companyId}")]
        public async Task<IActionResult> GetBuyCurrenciesAsync(int companyId, [FromQuery] bool refreshData = false)
        {
            var query = new GetBuyCurrencyListQuery(ClientId, companyId)
            {
                ResetCache = refreshData
            };

            var currencies = await _sender.Send(query);

            return Ok(currencies);
        }

        [HttpGet("company-or-client-buy-currencies/{companyId}")]
        public async Task<IActionResult> GetCompanyOrClientBuyCurrenciesAsync(int companyId, [FromQuery] bool refreshData = false)
        {
            var query = new GetBuyCurrencyListQuery(ClientId, companyId)
            {
                ResetCache = refreshData
            };

            var result = await _sender.Send(query);

            if (result.Data != null && result.Data.Selected == null)
            {
                result.Data.Data = (await _sender.Send(new GetBuyCurrencyForClientListQuery(ClientId)
                {
                    ResetCache = refreshData
                })).Data ?? [];
            }

            return Ok(result);
        }

        [HttpGet("buy-currencies-for-client")]
        public async Task<IActionResult> GetBuyCurrenciesForClientAsync([FromQuery] bool refreshData = false)
        {
            var query = new GetBuyCurrencyForClientListQuery(ClientId)
            {
                ResetCache = refreshData
            };

            var currencies = await _sender.Send(query);

            return Ok(currencies);
        }

        [HttpGet("buy-currencies")]
        public async Task<IActionResult> GetBuyCurrenciesForClientAsync([FromQuery] int? globalLoginClientNo, [FromQuery] bool refreshData = false)
        {
            var query = new GetBuyCurrencyForClientListQuery(globalLoginClientNo.HasValue ? globalLoginClientNo.Value : ClientId)
            {
                ResetCache = refreshData
            };

            var currencies = await _sender.Send(query);

            return Ok(currencies);
        }

        [HttpGet("regions")]
        public async Task<IActionResult> GetRegionsAsync([FromQuery] bool refreshData = false)
        {
            var query = new GetRegionListQuery()
            {
                ResetCache = refreshData
            };

            var result = await _sender.Send(query);

            return Ok(result);
        }

        [HttpGet("company-status")]
        public IActionResult GetCompanyStatusAsync([FromQuery] bool refreshData = false)
        {
            var result = new BaseResponse<List<DropDownDto>>
            {
                Success = true,
                Data = new List<DropDownDto>()
               {
                   new DropDownDto { Id =  -1, Name = "All"},
                   new DropDownDto { Id =  2, Name = "Active-Only"},
                   new DropDownDto { Id =  1, Name = "Inactive Only"}
               }
            };
            return Ok(result);
        }

        [HttpGet("company-sub-type")]
        public IActionResult GetCompanySubtype([FromQuery] bool refreshData = false)
        {
            var result = new BaseResponse<List<DropDownDto>>
            {
                Success = true,
                Data = new List<DropDownDto>()
                {
                    new DropDownDto
                    {
                        Id = 1,
                        Name = "All"
                    },
                    new DropDownDto
                    {
                        Id = 2,
                        Name = "Supplier"
                    },
                    new DropDownDto
                    {
                        Id = 3,
                        Name = "Customer"
                    }
                }
            };
            return Ok(result);
        }

        [HttpGet("certificate-Category")]
        public async Task<IActionResult> GetCertificateCategoryAsync([FromQuery] bool refreshData = false)
        {
            var query = new GetCertificateCategoryDropdownQuery()
            {
                ResetCache = refreshData
            };

            var result = await _sender.Send(query);

            return Ok(result);
        }

        [HttpGet("reason")]
        public async Task<IActionResult> GetDropDownDataAsync([FromQuery] Stype stype, bool refreshData = false)
        {
            var query = new GetAllReasonQuery();
            query.ResetCache = refreshData;

            var reqData = await _sender.Send(query);

            return Ok(reqData);
        }

        [HttpGet("supplier/{supplierId}/manufacturer-group")]
        public async Task<IActionResult> GetManufacturerGroupBySupplier(int supplierId)
        {
            var query = new GetManufacturerGroupBySupplierQuery(supplierId);
            var result = await _sender.Send(query);
            return Ok(result);
        }

        [HttpGet("company-prospects-ddl")]
        public async Task<IActionResult> GetDropDownForCRMProspects([FromQuery] string type)
        {
            var query = new GetDropDownForCRMProspectsQuery()
            {
                ClientId = ClientId,
                CompanyProspectsDDLType = type
            };

            var result = await _sender.Send(query);
            return Ok(result);

        }

        [HttpGet("buy-currency-by-global-no")]
        public async Task<IActionResult> GetBuyCurrencyByGlobalNo(int globalNo, int? globalLoginClientNo, bool buy)
        {
            var query = new GetAllBuyForClientAndGlobalQuery()
            {
                ClientId = (globalLoginClientNo.HasValue && globalLoginClientNo.Value > 0) ? globalLoginClientNo.Value : ClientId,
                GlobalCurrencyNo = globalNo,
                Buy = buy
            };
            var result = await _sender.Send(query);
            return Ok(result);
        }

        [HttpGet("active-users")]
        public async Task<IActionResult> GetActiveSecurityUsersByClientIdAsync([FromQuery] int clientId)
        {
            var securityUsersQuery = new GetActiveSecurityUsersQuery
            {
                ClientNo = clientId
            };
            var securityUsersResponse = await _sender.Send(securityUsersQuery);

            return Ok(securityUsersResponse);
        }

        [HttpGet("contact-groups")]
        public async Task<IActionResult> GetContactGroups([FromQuery] string type, [FromQuery] bool isForDropdown)
        {
            var result = await _sender.Send(new GetContactGroupsQuery()
            {
                ContactGroupType = type,
                IsForDropdown = isForDropdown
            });

            return Ok(result);
        }

        [HttpGet("sell-ship-via-for-client")]
        public async Task<IActionResult> GetSellShipViaForClientAsync([FromQuery] int? globalLoginClientNo, bool refreshData = false)
        {
            var clientNo = globalLoginClientNo ?? ClientId;
            var query = new GetShipViaForClientListQuery(clientNo)
            {
                ResetCache = refreshData
            };
            var result = await _sender.Send(query);
            return Ok(result);
        }

        [HttpGet("warehouse-for-client")]
        public async Task<IActionResult> GetWarehouseForClientAsync([FromQuery] WarehouseDropdownGetRequest? request, bool refreshData = false)
        {
            var query = new GetWarehouseForClientListQuery()
            {
                ResetCache = refreshData,
                ClientId = ClientId
            };
            if (request != null)
            {
                query.ClientId = request.GlobalLoginClientNo ?? ClientId;
                query.IncludeVirtual = request.IncludeVirtual;
                query.IncludeAll = request.IncludeAll;
            }

            var result = await _sender.Send(query);
            return Ok(result);
        }


        [HttpGet("buy-terms")]
        public async Task<IActionResult> GetBuyTermsAsync([FromQuery] int? globalLoginClientNo, [FromQuery] bool refreshData = false)
        {
            var query = new GetBuyTermsForCompanyQuery()
            {
                ResetCache = refreshData,
                ClientId = globalLoginClientNo.HasValue ? globalLoginClientNo.Value : ClientId
            };

            var terms = await _sender.Send(query);

            return Ok(terms);
        }

        [HttpGet("countries")]
        public async Task<IActionResult> GetCountriesAsync([FromQuery] int? globalLoginClientNo, [FromQuery] bool refreshData = false)
        {
            var query = new GetCountriesQuery(globalLoginClientNo.HasValue ? globalLoginClientNo.Value : ClientId)
            {
                ResetCache = refreshData,
            };

            var countries = await _sender.Send(query);

            return Ok(countries);
        }

        [HttpGet("view-levels/{enmPageId}")]
        public async Task<IActionResult> GetViewLevels([FromRoute] int enmPageId, [FromQuery] bool refreshData = false)
        {
            var response = await _sender.Send(new GetViewLevelByPageIdQuery
            {
                ResetCache = refreshData,
                EnmPageId = enmPageId,
                IsGlobalUser = _sessionManager.IsGlobalUser,
                LoginNo = HttpContext.Session.GetInt32(SessionKey.LoginID).GetValueOrDefault()
            });

            return Ok(response);
        }

        [HttpPost("search-for-po-approve-suppliers")]
        public async Task<IActionResult> AutoSearchForPOApproveSuppliers(AutoSearchForPOApproveSuppliersRequest request, [FromQuery] bool refreshData = false)
        {
            var query = new GetAllSearchForPOApproveSupplierQuery()
            {
                ClientId = ClientId,
                NameSearch = request.NameSearch,
                ResetCache = refreshData
            };

            var result = await _sender.Send(query);

            return Ok(result);
        }

        [HttpPost("search-for-sale-companies")]
        public async Task<IActionResult> GetAutosearchSaleCompanies([FromBody] GetAutosearchSaleCompaniesRequest request)
        {
            var query = new GetAllSaleCompanyQuery
            {
                ClientId = ClientId,
                NameSearch = request.NameSearch
            };

            var result = await _sender.Send(query);
            return Ok(result);
        }

        [HttpGet("sales-orders-status")]
        public async Task<IActionResult> GetSalesOrderStatusAsync([FromQuery] bool refreshData = false)
        {
            var query = new SalesOrderStatusQuery()
            {
                ResetCache = refreshData,
            };

            var result = await _sender.Send(query);

            if (result.Success && result.Data != null)
            {
                foreach(var item in result.Data)
                {
                    item.Name = _miscLocalizer[item.Name];
                }
            }

            return Ok(result);
        }

        [HttpGet("so-sent-to-customer")]
        public IActionResult GetSOsSentToCustomer([FromQuery] bool refreshData = false)
        {
            var result = new BaseResponse<List<DropDownDto>>
            {
                Success = true,
                Data = new List<DropDownDto>()
               {
                   new DropDownDto { Id =  0, Name = "All"},
                   new DropDownDto { Id =  1, Name = "Sent to Customer Only"},
                   new DropDownDto { Id =  2, Name = "Not Sent"}
               }
            };

            return Ok(result);
        }

        [HttpGet("counterfeit-electronic-parts")]
        public IActionResult GetCounterfeitElectronicParts([FromQuery] bool refreshData = false)
        {
            var result = new BaseResponse<List<DropDownDto>>
            {
                Success = true,
                Data = new List<DropDownDto>()
                {
                    new DropDownDto { Id =  2, Name = "No"},
                    new DropDownDto { Id =  1, Name = "Yes"}
                }
            };

            return Ok(result);
        }

        [HttpGet("so-checked-status")]
        public IActionResult GetSOCheckedStatus([FromQuery] bool refreshData = false)
        {
            var result = new BaseResponse<List<DropDownDto>>
            {
                Success = true,
                Data = new List<DropDownDto>()
                {
                   new DropDownDto { Id =  1, Name = "Checked"},
                   new DropDownDto { Id =  2, Name = "Unchecked"},
                   new DropDownDto { Id =  3, Name = "Both options"}
                }
            };

            return Ok(result);
        }

        [HttpGet("client-countries")]
        public async Task<IActionResult> GetClientCountries([FromQuery] int? soClientNo, [FromQuery] bool refreshData = false) {
            var result = await _sender.Send(new GetCountryForClientQuery() {
                ClientId = soClientNo ?? ClientId,
                ResetCache = refreshData
            });
            return Ok(result);
        }

        [HttpGet("military-uses")]
        public IActionResult GetMilitaryUses()
        {
            var result = new BaseResponse<IEnumerable<DropDownDto>>()
            {
                Success = true,
                Data = new List<DropDownDto>
                {
                    new DropDownDto { Id = 1, Name = _miscLocalizer.GetString("Yes")},
                    new DropDownDto { Id = 2, Name = _miscLocalizer.GetString("No") },
                    new DropDownDto { Id = 3, Name = _miscLocalizer.GetString("Dual")},
                    new DropDownDto { Id = 4, Name = _miscLocalizer.GetString("TBC")}
                }
            };
            return Ok(result);
        }

        [HttpGet("bom-status")]
        public IActionResult GetBOMStatus()
        {
            var result = new BaseResponse<List<BOMStatusDto>>
            {
                Success = true,
                Data =
                [
                    new () { ID =  (int)BOMStatus.New, Name = _miscLocalizer.GetString("New")},
                    new () { ID =  (int)BOMStatus.Open, Name =  _miscLocalizer.GetString("Open")},
                    new () { ID =  (int)BOMStatus.RPQ, Name = _miscLocalizer.GetString("RPQ")},
                    new () { ID =  (int)BOMStatus.PartialReleased, Name = _miscLocalizer.GetString("PartialReleased")},
                    new () { ID =  (int)BOMStatus.Released, Name = _miscLocalizer.GetString("Released")},
                    new () { ID =  (int)BOMStatus.CustomerQuoted, Name = _miscLocalizer.GetString("CustomerQuoted")},
                    new () { ID =  (int)BOMStatus.OrderGenerated, Name = _miscLocalizer.GetString("OrderGenerated")},
                    new () { ID =  (int)BOMStatus.Closed, Name = _miscLocalizer.GetString("Closed")}
                ]
            };

            return Ok(result);
        }

        [HttpGet("active-hubrfq-clients")]
        public async Task<IActionResult> GetActiveHUBRFQClients([FromQuery] bool refreshData = false)
        {
            var query = new GetActiveHUBRFQClientsQuery
            {
                ClientId = (_sessionManager.IsPOHub || _sessionManager.IsGlobalUser) ? Core.Constants.ClientId.DMCC : ClientId,
                LoginId = UserId,
                ResetCache = refreshData    
            };

            var result = await _sender.Send(query);

            return Ok(result);
        }

        [HttpGet("company-types")]
        public async Task<IActionResult> GetCompanyTypes([FromQuery] bool refreshData = false)
        {
            var query = new GetListActiveCompanyTypesQuery
            {
                ResetCache = refreshData
            };

            var result = await _sender.Send(query);

            return Ok(result);
        }

        [HttpGet("ogel-lisences")]
        public async Task<IActionResult> GetClientOgelisence([FromQuery] int? clientId, [FromQuery] bool refreshData = false) {
            var result = await _sender.Send(new GetOgelForClientListQuery
            {
                ClientId = clientId ?? ClientId,
                ResetCache = refreshData
            });
            return Ok(result);
        }

        [HttpGet("ogel-required-dropdown")]
        public IActionResult GetOgelRequiredDropDownAsync()
        {
            var result = new BaseResponse<IEnumerable<DropDownDto>>()
            {
                Success = true,
                Data = new List<DropDownDto>
                {
                    new DropDownDto { Id = 0, Name = _miscLocalizer.GetString("No") },
                    new DropDownDto { Id = 1, Name = _miscLocalizer.GetString("Yes")}
                }
            };
            return Ok(result);
        }

        [HttpPost("lytica-manufacturers")]
        public async Task<IActionResult> AutoSearchLyticaManufacturers([FromBody] LyticaManufacturersSearchRequest request)
        {
            var query = new AutoSearchLyticaManufacturersQuery(request.LyticaPartNo, request.Search, request.ShowInactive);
            var result = await _sender.Send(query);
            return Ok(result);
        }

        [HttpGet("promise-reasons")]
        public async Task<IActionResult> GetPromiseReasonsAsync([FromQuery] string section, [FromQuery] bool refreshData = false)
        {
            var response = await _sender.Send(new DropDownStatusReasonQuery(section) { ResetCache = refreshData });
            return Ok(response);
        }

        [HttpGet("product-sources")]
        public async Task<IActionResult> GetProductSourcesAsync([FromQuery] bool refreshData = false)
        {
            var response = await _sender.Send(new DropDownProductSourceQuery { ResetCache = refreshData });
            return Ok(response);
        }
    }
}
