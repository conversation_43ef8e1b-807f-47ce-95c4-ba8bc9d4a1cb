using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetGSAInfo;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Dto.Contact;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetSaleOrder;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetSalesOrderForPage;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.DocumentsSection;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders.Details
{
    [SectionLevelAuthorize(SecurityFunction.OrdersSection_View)]
    public class Index : BasePageModel
    {
        private readonly SessionManager _sessionManager;
        private readonly IMediator _mediator;
        private readonly IStringLocalizer<SharedUI.Status> _statusLocalizer;
        private readonly SettingManager _settingManager;
        private readonly IStringLocalizer<Misc> _miscLocalizer;

        public LinesSectionViewModel LinesSectionViewModel { get; set; } = new();
        [FromQuery(Name = "so")]
        public int? SalesOrderId { get; set; }
        public bool IsDifferentClient { get; set; }
        public bool IsGSAEditPermission { get; set; }
        public bool IsGlobalSalesAccessMember { get; set; }
        public bool CanAddSalesOrder { get; set; }
        public ExportApprovalStatusLineViewModel ExportApprovalStatuslineViewModel { get; set; } = new();
        public SalesOrderMainInfoViewModel SalesOrderMainInfoViewModel { get; set; } = new();
        public SalesOrderGeneralInfo SalesOrderGeneralInfo { get; set; } = new SalesOrderGeneralInfo();
        public DocumentsSectionViewModel SORDocumentsViewModel { get; set; } = new();
        public DocumentsSectionViewModel PdfDocumentsViewModel { get; set; } = new();
        public DocumentsSectionViewModel CustomerPoOnlyPdfViewModel { get; set; } = new();
        public DocumentsSectionViewModel SOPaymentFilesViewModel { get; set; } = new();
        public DocumentsSectionViewModel ExcelDocumentsViewModel { get; set; } = new();
        public bool CanPayByCreditCard { get; set; }
        public AuthorisationInfoViewModel AuthorisationInfoViewModel { get; set; } = new();
        public Index(SessionManager sessionManager, IMediator mediator, SecurityManager securityManager, IStringLocalizer<SharedUI.Status> statusLocalizer, SettingManager settingManager, IStringLocalizer<Misc> miscLocalizer) : base(securityManager)
        {
            _sessionManager = sessionManager;
            _mediator = mediator;
            _statusLocalizer = statusLocalizer;
            SiteSection = SiteSection.Orders;
            PageType = SitePage.Orders_SalesOrderDetail;
            _settingManager = settingManager;
            _miscLocalizer = miscLocalizer;
        }

        public async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken)
        {
            if (SalesOrderGeneralInfo.SalesOrderId <= 0) return RedirectToPage(V2Paths.NotFound);
            if (!await IsUserCanViewPageAsync()) return RedirectToPage(V2Paths.NotFound);
            AddBreadCrumbs();
            SetupPermission();
            await _settingManager.UpdateRecentlyView(BreadCrumb, Request.QueryString.Value ?? string.Empty);
            LinesSectionViewModel.CompanyName = SalesOrderGeneralInfo.CompanyName;
            LinesSectionViewModel.SalesOrderNumber = SalesOrderGeneralInfo.SalesOrderNo;
            var isGSAViewingOtherClient = SalesOrderGeneralInfo.IsGSA && IsDifferentClient;
            await SetUpAuthorisationPermission();
            await SetUpLineSectionPermissions(isGSAViewingOtherClient);
            SetUpExportApprovalStatuslinePermission();
            await SetupCustomerPoOnlyPdfSectionBox();
            SetupPdfDocumentsSectionBox();
            SetupSOPaymentFilesSectionBox();
            SetupExcelDocumentsSectionBox();
            SetUpMainInfoSection();
            SetupSORDocumentsSectionBox();
            return Page();
        }

        private void SetupPermission()
        {
            if (_securityManager is null) return;
            var isGSA = !_sessionManager.IsGlobalUser && _sessionManager.IsGSA;
            CanPayByCreditCard = (!IsDifferentClient || !isGSA || (isGSA && IsGSAEditPermission))
                && _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_MainInfo_Can_Pay_By_CreditCard);
            CanAddSalesOrder = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_Add);
        }

        private async Task SetUpSalesOrderDetailGeneralInfo()
        {
            var getSalesOrderForPage = await _mediator.Send(new GetSalesOrderForPageQuery { SalesOrderId = SalesOrderId.GetValueOrDefault() });
            if (getSalesOrderForPage.Success && getSalesOrderForPage.Data != null)
            {
                var getCompanyAdvisoryNote = await _mediator.Send(new GetCompanyAdvisoryNoteQuery { Id = getSalesOrderForPage.Data.CompanyNo.GetValueOrDefault() }, CancellationToken.None);

                SalesOrderGeneralInfo.SOClientId = getSalesOrderForPage.Data.ClientNo.GetValueOrDefault();
                SalesOrderGeneralInfo.IsGSA = _sessionManager.IsGSA; //temp
                SalesOrderGeneralInfo.SalesOrderId = getSalesOrderForPage.Data.SalesOrderId.GetValueOrDefault();
                SalesOrderGeneralInfo.SOClientName = getSalesOrderForPage.Data.ClientName ?? string.Empty;
                SalesOrderGeneralInfo.SalesOrderNo = getSalesOrderForPage.Data.SalesOrderNumber.GetValueOrDefault();
                SalesOrderGeneralInfo.CompanyName = getSalesOrderForPage.Data.CompanyName ?? string.Empty;
                SalesOrderGeneralInfo.CompanyNo = getSalesOrderForPage.Data.CompanyNo;
                SalesOrderGeneralInfo.SalesOrderCompanyId = getSalesOrderForPage.Data.CompanyNo.GetValueOrDefault();
                SalesOrderGeneralInfo.Status = _statusLocalizer[((Core.Enums.SalesOrderStatus)getSalesOrderForPage.Data.StatusNo.GetValueOrDefault()).ToString()];
                SalesOrderGeneralInfo.CurrentPrintSetTo = getSalesOrderForPage.Data.ConsolidateStatus;
                SalesOrderGeneralInfo.SOTeamNo = getSalesOrderForPage.Data.TeamNo.GetValueOrDefault();
                SalesOrderGeneralInfo.SODivisionNo = getSalesOrderForPage.Data.DivisionNo.GetValueOrDefault();
                SalesOrderGeneralInfo.SOSalesman = getSalesOrderForPage.Data.Salesman.GetValueOrDefault();
                SalesOrderGeneralInfo.IsFromIPO = getSalesOrderForPage.Data.IsFromIPO;
                if (SalesOrderGeneralInfo.SOClientId != _sessionManager.ClientID)
                {
                    var getGSAInfo = await _mediator.Send(new GetGsaInfoQuery(_sessionManager.ClientID.GetValueOrDefault(), _sessionManager.LoginID.GetValueOrDefault(), SalesOrderGeneralInfo.SalesOrderCompanyId));
                    if (getCompanyAdvisoryNote.Success && getCompanyAdvisoryNote.Data != null)
                    {
                        SalesOrderGeneralInfo.AdvisoryNotes = getCompanyAdvisoryNote.Data;
                    }
                    if (getGSAInfo.Success && getGSAInfo.Data != null)
                    {
                        IsGlobalSalesAccessMember = getGSAInfo.Data.IsGsa;
                    }
                }
            }
            string? authorizeSO = await _settingManager.GetSettingItem(ApplicationSettingItem.AutoApproveSO);
            SalesOrderGeneralInfo.AutoApproveSO = authorizeSO?.ToLower() == "true";
        }

        private async Task SetUpLineSectionPermissions(bool isOtherGSA)
        {
            if (isOtherGSA)
            {
                LinesSectionViewModel.IsReadOnly = true;
                return;
            }
            if (_securityManager != null)
                {
                    LinesSectionViewModel.CanAdd = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Lines_Add);
                    LinesSectionViewModel.CanEdit = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Lines_Edit);
                    LinesSectionViewModel.CanPost = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Lines_Post);
                    LinesSectionViewModel.CanUnpost = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Lines_Unpost);
                    LinesSectionViewModel.CanDelete = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Lines_Delete);
                    LinesSectionViewModel.CanAllocate = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Lines_Allocate);
                    LinesSectionViewModel.CanClose = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Lines_Close);
                    LinesSectionViewModel.CanCreateIpo = true;
                    LinesSectionViewModel.CanCloneThisLine = true;
                    LinesSectionViewModel.CanConfirm = true;
                    LinesSectionViewModel.CanConfirmAll = true;
                    LinesSectionViewModel.CanEditAll = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Lines_EditAll);
                    LinesSectionViewModel.CanEccnLog = true;
                    LinesSectionViewModel.CanEditDateRequired = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Lines_Edit_DateRequired);
                    LinesSectionViewModel.CanEditPromiseDateAfterCheck = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Lines_EditDatePromisedAfterChecked);
                    var autoApproveSOStringValue = await _settingManager.GetSettingItem(ApplicationSettingItem.AutoApproveSO);
                    bool.TryParse(autoApproveSOStringValue, out bool isAutoApproveSO);
                    LinesSectionViewModel.IsAutoAuthorizeSo = isAutoApproveSO;
                }
        }

        private void SetUpExportApprovalStatuslinePermission()
        {
            if (_securityManager == null) return;
            ExportApprovalStatuslineViewModel.CanEdit = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_ExportApproval_EditApproval);
            ExportApprovalStatuslineViewModel.CanRequestApproval = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_ExportApproval_RequestApproval);
            ExportApprovalStatuslineViewModel.CanSendApproval = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_ExportApproval_SendApproval);
            ExportApprovalStatuslineViewModel.CanSelect = true;
            ExportApprovalStatuslineViewModel.CanViewPdfWarning = true;
            if (!HttpContext.Session.Get<bool>(SessionKey.IsGlobalUser) && SalesOrderGeneralInfo.SOClientId != _sessionManager.ClientID && _sessionManager.IsGSA)
            {
                ExportApprovalStatuslineViewModel.CanEdit = false;
                ExportApprovalStatuslineViewModel.CanSendApproval = false;
                ExportApprovalStatuslineViewModel.CanRequestApproval = false;
                ExportApprovalStatuslineViewModel.CanSelect = false;
                ExportApprovalStatuslineViewModel.CanViewPdfWarning = false;
            }
        }
        private async Task SetUpAuthorisationPermission()
        {
            AuthorisationInfoViewModel.SalesOrderCompanyId = SalesOrderGeneralInfo.SalesOrderCompanyId;
            if (_securityManager == null) return;
            var isEnablePowerAppSetting = await _settingManager.GetSettingItem(ApplicationSettingItem.EnablePowerApp);
            AuthorisationInfoViewModel.DisplayRequestApproval = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Authorise_RequestApproval)
                                                                && !string.IsNullOrEmpty(isEnablePowerAppSetting) && isEnablePowerAppSetting.ToLower().Trim() == "true";
            AuthorisationInfoViewModel.AllowSOAuthorisationOnCompanyStop = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_AllowCheckedCompanyOnStop);
            AuthorisationInfoViewModel.DisplayReadyToShip = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_SOAuth_AllowReadyToShip);
            if (SalesOrderGeneralInfo.SOClientId != _sessionManager.ClientID && _sessionManager.IsGSA && _sessionManager.IsGlobalUser)
            {

                AuthorisationInfoViewModel.DisplayRequestApproval = false;
                AuthorisationInfoViewModel.DisplayReadyToShip = false;

            }


        }
        
        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.SalesOrders);
            BreadCrumb.Add(Navigations.SalesOrderDetail(SalesOrderGeneralInfo.SalesOrderNo.ToString()));
        }
        public override async Task OnPageHandlerExecutionAsync(PageHandlerExecutingContext context, PageHandlerExecutionDelegate next)
        {
            await SetUpSalesOrderDetailGeneralInfo();
            if (SalesOrderGeneralInfo.SOClientId != _sessionManager.ClientID)
            {
                IsDataHasOtherClient = _sessionManager.IsGSA;
            }
            await base.OnPageHandlerExecutionAsync(context, next);
        }

        private async Task<bool> IsUserCanViewPageAsync()
        {
            if (_securityManager == null) return false;

            if (SalesOrderGeneralInfo.SOClientId != _sessionManager.ClientID)
            {
                IsDifferentClient = true;
                if (IsGlobalSalesAccessMember)
                {
                    IsGSAEditPermission = !_sessionManager.IsGSAViewPermission;
                    return true;
                }
            }

            if (!_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_View)) return false;
            if (_sessionManager.IsGlobalUser) return true;

            var tabPermission = await GetTabSecurityFunctionPermission(SecurityFunction.Orders_SalesOrders_View);
            if (!tabPermission[ViewLevelList.My])
            {
                return false;
            }

            var isTabAllow = CheckTabSecurityInDetailPage(new TabSecurityInDetailPageRequest
            {
                ListTabs = tabPermission.Where(x => x.Value).Select(x => (int)x.Key).ToList(),
                TeamID = SalesOrderGeneralInfo.SOTeamNo,
                LoginTeamID = _sessionManager.LoginTeamID.GetValueOrDefault(),
                DivisionID = SalesOrderGeneralInfo.SODivisionNo,
                LoginDivisionID = _sessionManager.LoginDivisionID.GetValueOrDefault(),
                ClientID = SalesOrderGeneralInfo.SOClientId,
                LoginClientID = _sessionManager.ClientID.GetValueOrDefault(),
                LoginID = SalesOrderGeneralInfo.SOSalesman,
                LoginUserID = _sessionManager.LoginID.GetValueOrDefault(),
            });

            if (!isTabAllow)
            {
                return false;
            }

            return true;
        }

        private async Task SetupCustomerPoOnlyPdfSectionBox()
        {
            var isGSA = !_sessionManager.IsGlobalUser && _sessionManager.IsGSA;
            if (_securityManager == null) return;
            CustomerPoOnlyPdfViewModel = new()
            {
                SectionId = "customer-po-only-pdf",
                DocumentType = DocumentSizeByType.PDFDocument,
                SectionName = _miscLocalizer["SORPDFDocuments", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                CanAdd = !IsDifferentClient || !isGSA || (isGSA && IsGSAEditPermission),
                //_securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_PDFDocument_Add) //not used
                CanDelete = !isGSA || !IsDifferentClient
                    && (_securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_PDFDocument_Delete)
                    && (_securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_PDFDocument_DeleteAfterAuthorise) || !await CheckIsAuthorizedAsync())),
                NoDataMessage = "This Sales Order has no PDF document",
                SectionBoxTitle = "Customer PO Only",
                UploadDialog = new()
                {
                    DialogId = "customer-po-only-pdf-upload-dialog",
                    FormId = "customer-po-only-pdf-upload-form",
                    SectionName = _miscLocalizer["SORPDFDocuments", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                    DialogTitle = "PDF Documents",
                    DialogName = "Add New Customer PO",
                    DialogDescription = "Select a new PDF file for the Customer PO and press Save",
                    AllowedFileExtensions = ".pdf"
                },
                RemoveFileDialog = new()
                {
                    DialogId = "customer-po-only-pdf-remove-dialog",
                    SectionName = _miscLocalizer["SORPDFDocuments", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                    DialogTitle = "Delete PDF",
                    DialogDescription = "Are you sure you would like to delete this PDF?",
                }
            };
        }
        private void SetupSORDocumentsSectionBox()
        {
            var isGSA = !_sessionManager.IsGlobalUser && _sessionManager.IsGSA;
            if (_securityManager == null) return;

            SORDocumentsViewModel = new()
            {
                SectionId = "sor-pdf",
                DocumentType = DocumentSizeByType.PDFDocument,
                SectionName = _miscLocalizer["SORPDFDocumentsNew", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                CanAdd = false,
                CanDelete = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_SORPDFDocument_Delete)
                            && (!IsDifferentClient || !isGSA),
                NoDataMessage = "This Sales Order has no SOR",
                SectionBoxTitle = "SOR",
                RemoveFileDialog = new()
                {
                    DialogId = "sor-pdf-remove-dialog",
                    SectionName = _miscLocalizer["SORPDFDocumentsNew", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                    DialogTitle = "Delete PDF",
                    DialogDescription = "Are you sure you would like to delete this PDF?",
                }
            };
        }
        private void SetupSOPaymentFilesSectionBox()
        {
            if (_securityManager == null) return;
            SOPaymentFilesViewModel = new()
            {
                SectionId = "so-payment-files",
                DocumentType = DocumentSizeByType.PDFDocument,
                SectionName = $"{SectionNameConstant.SOPaymentFiles}_{SalesOrderGeneralInfo.SalesOrderNo}_{SalesOrderId}",
                IsReadOnly = true,
                NoDataMessage = "The receipt has no PDF file attached",
                SectionBoxTitle = "SO Payment Files",
            };
        }
        private async Task<bool> CheckIsAuthorizedAsync()
        {
            return (await _mediator.Send(new GetSaleOrderQuery(SalesOrderId.GetValueOrDefault(), 0, 0))).Data?.AuthorisedBy > 0;
        }

        private void SetupPdfDocumentsSectionBox()
        {
            var isGSA = !_sessionManager.IsGlobalUser && _sessionManager.IsGSA;
            if (_securityManager == null) return;
            PdfDocumentsViewModel = new()
            {
                SectionId = "pdf-documents-PDF",
                DocumentType = DocumentSizeByType.PDFDocument,
                SectionName = _miscLocalizer["SOPDFDocuments", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                CanAdd = !IsDifferentClient || !isGSA || (isGSA && IsGSAEditPermission),
                //_securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_PDFDocument_Add) //not used
                CanDelete = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_PDFDocument_Delete)
                            && (!IsDifferentClient || !isGSA),
                NoDataMessage = "This Sales Order has no PDF document",
                SectionBoxTitle = "PDF Documents",
                UploadDialog = new()
                {
                    DialogId = "pdf-documents-PDF-upload-dialog",
                    FormId = "pdf-documents-PDF-upload-form",
                    SectionName = _miscLocalizer["SORPDFDocuments", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                    DialogTitle = "PDF Documents",
                    DialogName = "Add New PDF Document",
                    DialogDescription = "Select a new PDF file for the PDF Documents and press Save",
                    AllowedFileExtensions = ".pdf"
                },
                RemoveFileDialog = new()
                {
                    DialogId = "pdf-documents-PDF-remove-dialog",
                    SectionName = _miscLocalizer["SORPDFDocuments", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                    DialogTitle = "Delete PDF",
                    DialogDescription = "Are you sure you would like to delete this PDF?",
                }
            };
        }

        private void SetupExcelDocumentsSectionBox()
        {
            if (_securityManager == null) return;
            var isGSA = !_sessionManager.IsGlobalUser && _sessionManager.IsGSA;
            ExcelDocumentsViewModel = new()
            {
                SectionId = "documents-excel",
                DocumentType = DocumentSizeByType.ExcelDocument,
                SectionName = _miscLocalizer["SOExcelDocDocuments", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                CanAdd = !IsDifferentClient || !isGSA,
                //_securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_SOExcelDocDocument_Add) //not used
                CanDelete = !IsDifferentClient || !isGSA,
                //_securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_SOExcelDocDocument_Delete) //not found,
                NoDataMessage = "This Sales Order has no Excel or doc file",
                SectionBoxTitle = "Uploaded Documents",
                UploadDialog = new()
                {
                    DialogId = "documents-excel-upload-dialog",
                    FormId = "documents-excel-upload-form",
                    SectionName = _miscLocalizer["SOExcelDocDocuments", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                    DialogTitle = "Uploaded Documents",
                    DialogName = "Add New Excel or Doc Document",
                    DialogDescription = "Select a new Excel or Doc file and press Save",
                    AllowedFileExtensions = ".xls, .xlsx, .csv, .doc, .docx"
                },
                RemoveFileDialog = new()
                {
                    DialogId = "documents-excel-remove-dialog",
                    SectionName = _miscLocalizer["SOExcelDocDocuments", SalesOrderGeneralInfo.SalesOrderNo, SalesOrderId.ToString() ?? ""],
                    DialogTitle = "Delete Document",
                    DialogDescription = "Are you sure you would like to delete this Document?",
                }
            };
        }
        private void SetUpMainInfoSection()
        {
            if (_securityManager != null)
            {
                SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanEdit = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_MainInfo_Edit);
                SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanEditCurrencyAndTerms = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_MainInfo_EditCurrencyAndTerms);
                SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanEditTax = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_MainInfo_EditTax);
                SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanEditPaid = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_MainInfo_Paid_Edit);
                SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanEditFieldsAfterAuthorisation = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_MainInfo_EditFieldsAfterAuthorisation);
                SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanEditShippingCosts = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_MainInfo_EditShippingCosts);
                SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanEditAS9120 = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_QuoteAndSO_Edit_AS9120);
                SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanCloseSalesOrder = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_SalesOrder_MainInfo_Close);
                SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanConfirmSOSent = true;
                SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanNotify = true;

                if (!HttpContext.Session.Get<bool>(SessionKey.IsGlobalUser) && SalesOrderGeneralInfo.SOClientId != _sessionManager.ClientID && _sessionManager.IsGSA)
                {
                    SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanCloseSalesOrder = false;
                    SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanConfirmSOSent = false;
                    SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission.CanNotify = false;
                }
            }
            SalesOrderMainInfoViewModel.SOGeneralInfo = SalesOrderGeneralInfo;
        }
    }

    public class SalesOrderGeneralInfo
    {
        public int SOClientId { get; set; }
        public string SOClientName = string.Empty;
        public bool IsGSA { get; set; }
        public int SalesOrderNo { get; set; }
        public int SalesOrderId { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public int? CompanyNo { get; set; }
        public int SalesOrderCompanyId { get; set; }
        public string Status { get; set; } = string.Empty;
        public string AdvisoryNotes { get; set; } = string.Empty;
        public string? CurrentPrintSetTo { get; set; }
        public int SOTeamNo { get; set; }
        public int SODivisionNo { get; set; }
        public int SOSalesman { get; set; }
        public bool AutoApproveSO { get; set; }
        public bool IsFromIPO { get; set; }
    }

    public class SalesOrderMainInfoViewModel
    {
        public SalesOrderGeneralInfo SOGeneralInfo { get; set; } = new SalesOrderGeneralInfo();
        public SalesOrderMainInfoPermission SalesOrderMainInfoPermission = new SalesOrderMainInfoPermission();
    }

    public class SalesOrderMainInfoPermission
    {
        public bool CanEditTax { get; set; }
        public bool CanEditCurrencyAndTerms { get; set; }
        public bool CanEdit { get; set; }
        public bool CanEditPaid { get; set; }
        public bool CanEditShippingCosts { get; set; }
        public bool CanEditAS9120 { get; set; }
        public bool CanEditFieldsAfterAuthorisation { get; set; }
        public bool CanCloseSalesOrder { get; set; }
        public bool CanConfirmSOSent { get; set; }
        public bool CanNotify { get; set; }
    }
}
