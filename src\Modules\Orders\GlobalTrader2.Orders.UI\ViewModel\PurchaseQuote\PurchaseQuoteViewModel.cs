﻿using GlobalTrader2.Dto.Converters.DateTime;
using System.Text.Json.Serialization;

namespace GlobalTrader2.Orders.UI.ViewModel.PurchaseQuote
{
    public class PurchaseQuoteViewModel
    {
        public int PurchaseRequestLineId { get; set; }
        public int PurchaseRequestId { get; set; }
        public int PurchaseRequestNumber { get; set; }
        public string Part { get; set; } = string.Empty;
        public int Quantity { get; set; }
        [JsonConverter(typeof(ShortDateWithCultureFormatterConverter))]
        public DateTime DateRequested { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string BOMName { get; set; } = string.Empty;
        public int BOMNo { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
    }
}
