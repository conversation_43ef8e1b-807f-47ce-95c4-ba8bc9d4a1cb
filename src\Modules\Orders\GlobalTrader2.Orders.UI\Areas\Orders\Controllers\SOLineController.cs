﻿using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.Orders.Requirements;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Queries;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.CloseSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.ConfirmSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.DeleteSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.PostUnpostSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetItemsearchSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetOrCreateSOIHSEccnDetail;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetPdfForEuuSoLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetPromiseLog;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSoLineDetails;
using System.Globalization;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetPdfForEuuSoLine;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.PostUnpostSalesOrderLine;
using System.Globalization;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine;
using System.ComponentModel.DataAnnotations;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyEccnSales;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;

[ApiController]
[Authorize]
[Route("api/orders/sales-order/so-lines")]
public class SOLineController : ApiBaseController
{
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    public SOLineController(IMediator mediator, IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    [HttpPost("get-so-ihs-eccn-detail")]
    public async Task<IActionResult> GetSOIHSEccnDetail([FromBody] SoihsEccnRequest request)
    {
        var response = await _mediator.Send(new GetOrCreateSoihsEccnDetailQuery
        {
            ClientNo = ClientId,
            PartNo = request.PartNo.Trim() ?? string.Empty,
            LoginNo = UserId
        });

        return new JsonResult(response);
    }

    [HttpGet("{salesOrderLineId}")]
    public async Task<IActionResult> GetSoLineDetailsAsync(int salesOrderLineId)
    {
        var response = await _mediator.Send(new GetSoLineDetailsQuery(salesOrderLineId, ClientId, IsPOHub, ClientCurrencyCode));
        return Ok(response);
    }

    [HttpGet("{salesOrderLineId}/promise-reason-logs")]
    public async Task<IActionResult> GetPromiseLogsBySoLineIdAsync(int salesOrderLineId)
    {
        var response = await _mediator.Send(new GetPromiseLogQuery(salesOrderLineId));
        return Ok(response);
    }


    [HttpPut("close-line")]
    [ApiAuthorize(false, SecurityFunction.Orders_SalesOrder_Lines_Close)]
    public async Task<IActionResult> CloseSoLineAsync([FromBody] CloseSalesOrderLineRequest request)
    {
        var response = await _mediator.Send(new CloseSalesOrderLineCommand
        {
            SalesOrderLineId = request.SalesOrderLineId,
            ResetQuantity = request.ResetQuantity,
            UpdatedBy = UserId,
        });
        return Ok(response);
    }

    [HttpPut("confirm-lines")]
    public async Task<IActionResult> ConfirmSoLineAsync([FromBody] ConfirmSalesOrderLineRequest request)
    {
        var response = await _mediator.Send(new ConfirmSalesOrderLineCommand
        {
            SalesOrderLineId = request.SalesOrderLineId,
            SalesOrderNo = request.SalesOrderNo,
            ConfirmAll = request.ConfirmAll
        });
        return Ok(response);
    }

    [HttpPut("post-unpost")]
    [ApiAuthorize(false, SecurityFunction.Orders_SalesOrder_Lines_Post)]
    public async Task<IActionResult> PostUnpostSoLineAsync([FromBody] PostUnpostSalesOrderLineRequest request)
    {
        var response = await _mediator.Send(new PostUnpostSOLineCommand
        {
            SalesOrderLineId = request.SalesOrderLineId,
            Posted = request.Posted,
            UpdatedBy = UserId,
        });
        return Ok(response);
    }

    [HttpDelete("{salesOrderLineId}/delete-line")]
    [ApiAuthorize(false, SecurityFunction.Orders_SalesOrder_Lines_Delete)]
    public async Task<IActionResult> DeleteSoLineAsync(int salesOrderLineId)
    {
        var response = await _mediator.Send(new DeleteSalesOrderLineCommand
        {
            SalesOrderLineId = salesOrderLineId
        });
        return Ok(response);
    }

    [HttpGet("{salesOrderLineId}/euu-pdfs")]
    public async Task<IActionResult> GetEuuSoLinePdfAsync(int salesOrderLineId)
    {
        var response = await _mediator.Send(new GetPdfForEuuSoLineQuery
        {
            SalesOrderLineId = salesOrderLineId,
            FileType = "EUUPDF",
            Culture = Culture
        });
        return Ok(response);
    }

    [HttpPost("search-requirements")]
    public async Task<IActionResult> SearchRequirementsAsync([FromBody] CustomerRequirementsMainInfoRequest request, CancellationToken cancellation)
    {
        var query = new GetCustomerRequirementsMainInfoQuery()
        {
            ClientId = ClientId,
            OrderBy = request.OrderBy,
            SortDir = request.SortDir,
            PageIndex = request.Index / request.Size,
            PageSize = request.Size,
            PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
            CompanySearch = request.CompanySearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.CompanySearch) : null,
            CustomerRequirementNoHi = request.CustomerRequirementNoHi,
            CustomerRequirementNoLo = request.CustomerRequirementNoLo,
            IncludeClosed = request.IncludeClosed,
            ReceivedDateFrom = request.ReceivedDateFrom,
            ReceivedDateTo = request.ReceivedDateTo,
        };

        var result = await _mediator.Send(query, cancellation);

        var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

        var cultureInfo = new CultureInfo(Culture);

        foreach (var item in result.Data ?? Enumerable.Empty<CustomerRequirementMainInfoDto>())
        {
            item.FormatedPrice = Core.Helpers.Functions.FormatCurrency(item.Price ?? 0, cultureInfo, item.CurrencyCode ?? string.Empty, 5, false);
        }

        var response = new DatatableResponse<IEnumerable<CustomerRequirementMainInfoDto>>()
        {
            Success = result.Success,
            Data = result.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpPost("item-search")]
    public async Task<IActionResult> GetSalesOrderLineItemSearchAsync([FromBody] GetItemsearchSalesOrderLineRequest request, CancellationToken cancellation)
    {
        var query = new GetItemsearchSalesOrderLineQuery()
        {
            ClientId = ClientId,
            OrderBy = request.OrderBy,
            SortDir = request.SortDir,
            PageIndex = request.Index / request.Size,
            PageSize = request.Size,
            PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
            CompanySearch = request.CompanySearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.CompanySearch) : null,
            SoNoLo = request.SoNoLo,
            SoNoHi = request.SoNoHi,
            IncludeClosed = request.IncludeClosed,
            DateOrderedFrom = request.DateOrderedFrom,
            DateOrderedTo = request.DateOrderedTo,
            DatePromisedFrom = request.DatePromisedFrom,
            DatePromisedTo = request.DatePromisedTo,
            OnlyFromIPO = request.OnlyFromIPO,
            Salesperson = request.Salesperson
        };

        var result = await _mediator.Send(query, cancellation);

        var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

        var cultureInfo = new CultureInfo(Culture);

        foreach (var item in result.Data ?? Enumerable.Empty<GetItemsearchSalesOrderLineDto>())
        {
            item.FormatedPrice = Functions.FormatCurrency(item.Price ?? 0, cultureInfo, item.CurrencyCode ?? string.Empty, 5, false);
        }

        var response = new DatatableResponse<IEnumerable<GetItemsearchSalesOrderLineDto>>()
        {
            Success = result.Success,
            Data = result.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }
        
    [HttpPut("{salesOrderLineId}")]
    [ApiAuthorize(false, SecurityFunction.Orders_SalesOrder_Lines_Edit)]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditSoLineAsync([Range(1, int.MaxValue)] int salesOrderLineId, [FromBody] EditSalesOrderLineRequest request)
    {
        var command = _mapper.Map<EditSalesOrderLineCommand>(request);
        command.SalesOrderLineId = salesOrderLineId;
        command.UpdatedBy = UserId;
        command.ClientId = ClientId;
        command.LoginEmail = LoginEmail;
        command.LoginFullName = LoginFullName;

        var response = await _mediator.Send(command);
        return Ok(response);
    }

    [HttpPost("eccn-sales-notification")]
    public async Task<IActionResult> NotifyEccnSalesAsync([FromBody] NotifyEccnSalesRequest request)
    {
        var command = new NotifyEccnSalesCommand(request.SaleOrderId, request.SaleOrderLineId, UserId, ClientId, LoginEmail, LoginFullName);

        var response = await _mediator.Send(command);
        return Ok(response);
    }
}

public record SoihsEccnRequest(string PartNo);
