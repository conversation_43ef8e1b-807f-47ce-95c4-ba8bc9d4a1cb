using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyECCNMessageSO;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetMailGroupNoByName;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Dto.MailMessages;

namespace GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyEccnSales
{
    public class NotifyEccnSalesHandler(ISender _sender, IBaseRepository<SalesOrder> _salesOrderRepository) : IRequestHandler<NotifyEccnSalesCommand, BaseResponse<bool>>
    {
        public async Task<BaseResponse<bool>> Handle(NotifyEccnSalesCommand request, CancellationToken cancellationToken)
        {
            var eccnRestrictedMailGroupNo = await _sender.Send(new GetMailGroupNoByNameQuery(request.ClientId, Core.Constants.MailGroup.ECCNRestricted), cancellationToken);
            var recipients = new List<RecipientRequest>
            {
                new RecipientRequest(eccnRestrictedMailGroupNo, (int)MailMessageAddressType.Group)

            };

            int? supportTeamMemberNo = await GetSupportTeamMemberNoAsync(request.SalesOrderId, request.ClientId);
            if (supportTeamMemberNo.HasValue)
            {
                recipients.Add(new RecipientRequest(supportTeamMemberNo.Value, (int)MailMessageAddressType.Individual));
            }

            recipients.Add(new RecipientRequest(request.LoginId, (int)MailMessageAddressType.Individual));
            var notifyEccnCommand = new NotifyECCNMessageSOCommand
            {
                SalesOrderLineId = request.SalesOrderLineId,
                Recipient = recipients,
                LoginId = request.LoginId,
                SenderEmail = request.LoginEmail,
                SenderName = request.LoginName

            };
            await _sender.Send(notifyEccnCommand, cancellationToken);
            return new BaseResponse<bool>
            {
                Data = true,
                Success = true
            };
        }
        
        private async Task<int?> GetSupportTeamMemberNoAsync(int salesOrderId, int clientId)
        {
            var saleOrder = await _salesOrderRepository.ListAsync(
                filter: s => s.ClientNo == clientId && s.SalesOrderId == salesOrderId);

            return saleOrder.Count > 0 ? saleOrder[0].SupportTeamMemberNo : null;
        }
    }
}