﻿import { LiteDatatable } from "../../../components/base/lite-datatable.component.js?v=#{BuildVersion}#"
import { FilterTableSectionBox } from "../../../components/base/filter-table-section-box.component.js?v=#{BuildVersion}#"
import { SectionBox } from "../../../components/base/section-box.component.js?v=#{BuildVersion}#"
import { inputFilterDefinition } from "./input-filter-definition.js?v=#{BuildVersion}#"
import { HUBRFQTalbeFilterComponent } from "./hurfq-table-filter.js?v=#{BuildVersion}#"
import { PartTableButton } from "../../../components/button/part-table-button.js?v=#{BuildVersion}#";
import { LiteFormDialog } from "../../../components/base/lite-form-dialog.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../helper/rohs-helper.js?v=#{BuildVersion}#";

$(async () => {
    
    let custReqAlternative = Object.freeze({
        1: hubrfqLocalizedTitles.Alternative,
        2: hubrfqLocalizedTitles.PossibleAlternative,
        3: hubrfqLocalizedTitles.FirmAlternative
    });
    const state = {
        tabId: 0, // default currentTab
        tabDic: {
            0: "my-tab",
            1: "team-tab",
            2: "division-tab",
            3: "company-tab",
        },
        filterStateType: 33,//BOMLines
        requirementsTable: null,
        filter: null,
        sectionBox: null,
        filterSectionBox: null,
        addRequirementDialogInited: false,
        lastFilter: null, // state already made a call
        isSearchRequirement: false,
    }
    const preferences = await getPreferences();
    state.preferences = preferences;

    function registerTabEvents() {
        $(document).on('click', '#hurfq-nav-tabs-wrapper button', async (e, data) => {
            const ignoreSave = data?.notResetPageRequired
            setTimeout(function () {
                openningTab($(e.target).attr("tabId"), ignoreSave, !data?.notResetPageRequired);
            }, 100);
        })
    }

    function setHeaderOrDetailDefault() {
        state.filter.getInputElementByName('HeaderOrDetail').clear();        
    }

    async function initFilterTableSectionBoxAsync() {
        state.requirementsTable = new LiteDatatable('#hurfqTbl', {
            serverSide: true,
            ajax: {
                url: '/api/orders/bom/list',
                type: 'POST',
            },
            ordering: true,
            disableSelect: true,
            pageConfig: {
                pageSize: state.preferences.defaultListPageSize
            },
            columns: [ 
                {
                    data: 'bomId',
                    name: 'bomId',
                    visible: false
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${hubrfqLocalizedTitles.HUBRFQName}</div>${hubrfqLocalizedTitles.ReqNo}`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'contactName',
                    width: "10%",
                    render: function (data, type, row) {
                        let customerReqHtml = '<br>';
                        
                        if (row.customerRequirementNumber) {
                            if (row.isPOHub) {
                                customerReqHtml = $('<span>').text(row.customerRequirementNumber).prop('outerHTML') + '<br>';
                            } else {
                                customerReqHtml = $('<a>')
                                    .attr('href', GlobalTrader.PageUrlHelper.GET_URL_CustomerRequirement(row.customerRequirementId))
                                    .addClass('dt-hyper-link')
                                    .text(row.customerRequirementNumber)
                                    .prop('outerHTML') + '<br>';
                            }
                        }

                        return `
                    ${row.bomName ? $('<a>').attr('href', GlobalTrader.PageUrlHelper.GET_URL_BOM(row.bomId)).addClass('dt-hyper-link').text(row.bomName).prop('outerHTML') + '<br>' : '<br>'}
                    ${customerReqHtml}
                `;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${hubrfqLocalizedTitles.AssignedUser}</div>${hubrfqLocalizedTitles.Quantity}`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'contactName',
                    width: "10%",
                    render: function (data, type, row) {
                        let assignedUserHtml = '';
                        if (state.isSearchRequirement) {
                            assignedUserHtml = row.assignedTo ? $('<a>').text(row.assignedTo).prop('outerHTML') + '<br>' : '<br>';
                        } else {
                            assignedUserHtml = row.assignedUser ? $('<a>').text(row.assignedUser).prop('outerHTML') + '<br>' : '<br>';
                        }

                        const quantityHtml = row.quantity ? $('<a>').text(row.quantity).prop('outerHTML') + '<br>' : '<br>';

                        return `${assignedUserHtml}${quantityHtml}`;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${hubrfqLocalizedTitles.PartNo}</div>${hubrfqLocalizedTitles.Manufacturer}`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'contactName',
                    width: "10%",
                    render: function (data, type, row) {
                        let part = renderPartAlternate(row.part, row.alternateStatus, row.alternate);
                        return `${ROHSHelper.writePartNo(part, row.rohs)} <br>
                    ${row.manufacturerCode ? $('<a>').attr('href', `/Contact/Manufacturers/Details?mfr=${row.manufacturerNo}`).addClass('dt-hyper-link').text(row.manufacturerCode).prop('outerHTML') + '<br>' : '<br>'}
                `;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${hubrfqLocalizedTitles.HUBRFQCode}</div><div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${hubrfqLocalizedTitles.Salesperson}</div>${hubrfqLocalizedTitles.SupportTeamMember}`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'contactName',
                    width: "10%",
                    render: function (data, type, row) {
                        return `
                    ${row.bomCode ? $('<a>').text(row.bomCode).prop('outerHTML') + '<br>' : '<br>'}
                    ${row.salesmanName ? $('<a>').text(row.salesmanName).prop('outerHTML') + '<br>' : '<br>'}
                    ${row.supportTeamMemberName ? $('<a>').css('background-color', 'yellow').text(row.supportTeamMemberName).prop('outerHTML') + '<br>' : '<br>'}
                `;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${hubrfqLocalizedTitles.Company}</div>${hubrfqLocalizedTitles.Contact}`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'contactName',
                    width: "10%",
                    render: function (data, type, row) {
                        function handleCompanyNameLink(row) {
                            if (!row.companyName) return '<br>';
                            const companyText = state.isSearchRequirement
                                ? row.companyName
                                : `${row.companyName} (${row.companyType})`;

                            const $a = $('<a>')
                                .attr('href', GlobalTrader.PageUrlHelper.Get_URL_Company(row.companyNo))
                                .addClass('dt-hyper-link')
                                .text(companyText);

                            if (row.blnMakeYellow) {
                                $a.css('background-color', 'yellow');
                            }
                            return $a.prop('outerHTML') + '<br>';
                        }
                        return `
                    ${handleCompanyNameLink(row)}
                    ${row.contactName ? $('<a>').attr('href', GlobalTrader.PageUrlHelper.Get_URL_Contact(row.contactNo)).addClass('dt-hyper-link').text(row.contactName).prop('outerHTML') + '<br>' : '<br>'}
                `;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${hubrfqLocalizedTitles.Status}</div>${hubrfqLocalizedTitles.Division}`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'contactName',
                    width: "10%",
                    render: function (data, type, row) {
                        return `
                    ${row.status ? $('<a>').text(row.status).prop('outerHTML') + '<br>' : '<br>'}
                    ${row.divisionName ? $('<a>').text(row.divisionName).prop('outerHTML') + '<br>' : '<br>'}
                `;
                    }
                },
                {
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${hubrfqLocalizedTitles.ReceivedDate}</div>${hubrfqLocalizedTitles.Promised}`,
                    className: 'text-wrap text-break header-custom align-top',
                    orderSequence: ["asc", "desc"],
                    data: 'contactName',
                    width: "10%",
                    render: function (data, type, row) {
                        return `
                    ${row.dlup ? $('<a>').text(row.dlup).prop('outerHTML') + '<br>' : '<br>'}
                `;
                    }
                },
                {
                    title: `<div style="border: none !important; margin-bottom: 20px; padding-bottom: 8px;">${hubrfqLocalizedTitles.HUBRFQRequiredDate}</div>`,
                    className: 'text-wrap text-break header-custom align-top',
                    data: 'contactName',
                    width: "10%",
                    orderSequence: ["asc", "desc"],
                    render: function (data, type, row) {
                        const linkHtml = row.requiredDate
                            ? $('<a>')
                                .text(row.requiredDate)
                                .prop('outerHTML')
                            : '';

                        const spanHtml = `<span style="background-color: ${getColorRequiredDateStatus(row.requiredDateStatus)} !important; float: right; margin-top: -17px; height: 20px; width: 20px;"></span>`;

                        return `${linkHtml}<br>${spanHtml}`;
                    }
                },
                {
                    title: `<div style="border: none !important; margin-bottom: 20px; padding-bottom: 8px;">${hubrfqLocalizedTitles.TotalValue}</div>`,
                    className: 'text-wrap text-break header-custom align-top',
                    data: 'contactName',
                    width: "10%",
                    orderSequence: ["asc", "desc"],
                    render: function (data, type, row) {
                        return `
                    ${row.totalValue ? $('<a>').text(row.totalValue).prop('outerHTML') + '<br>' : '<br>'}
                    `;
                    }
                },
            ],
            rowId: 'bomId'
        })

        let inputs = inputFilterDefinition;

        const clientInput = inputs.find(x => x.name == "Client");

        clientInput.options.onSelected = (data, selectedClient) => {
            if (!data) {
                return;
            };

            let divisonEndpoint = `/setup/company-settings/divisions/dropdown-divisions`;
            if (selectedClient) {
                divisonEndpoint += `?clientNo=${selectedClient}`;
            }

            const divisionFilter = state.filter.getInputElementByName('Division');

            const currentDivisionValue = $(divisionFilter.element).dropdown("selectedValue");

            $(divisionFilter.element).dropdown("reset");

            $(divisionFilter.element).dropdown("option", "onSuccessLoaded", function (data, currentSelected) {
                if (currentDivisionValue && data?.some(item => item.divisionId == currentDivisionValue)) {
                    $(divisionFilter.element).dropdown("select", currentDivisionValue);
                }
            });

            $(divisionFilter.element).dropdown("reload", divisonEndpoint);

            let salespersonEndpoint = `/lists/employee`;
            if (selectedClient) {
                salespersonEndpoint += `?globalLoginClientNo=${selectedClient}`;
            } 

            const salespersonFilter = state.filter.getInputElementByName("Salesperson");

            const currentSalespersonValue = $(salespersonFilter.element).dropdown("selectedValue");

            $(salespersonFilter.element).dropdown("reset");

            $(salespersonFilter.element).dropdown("option", "onSuccessLoaded", function (data, currentSelected) {
                if (currentSalespersonValue && data?.some(item => item.loginId == currentSalespersonValue)) {
                    $(salespersonFilter.element).dropdown("select", currentSalespersonValue);
                }
            });

            $(salespersonFilter.element).dropdown("reload", salespersonEndpoint);
        }

        state.filter = new HUBRFQTalbeFilterComponent("#hubrfq-box-content #filter-section-wrapper", "Filter Results", {
            inputConfigs: inputs
            //templateId:'my-template' // template insert input to filter
        });
        state.sectionBox = new SectionBox('#hubrfq', {
            loadingContentId: state.requirementsTable.getContainerId() // only table should be hidden while processing api requests
        }, {
            enableFilterButton: false
        });

        state.filterSectionBox = new FilterTableSectionBox(state.requirementsTable, state.filter, state.sectionBox, {
            prepareDataBeforeReload: prepareDataBeforeReload
        });

        state.filterSectionBox.registerHiddenButtonFilterEvents();

        state.sectionBox.setLock(state.preferences.saveDLNState);
        state.filterSectionBox.setFilterStateType(state.filterStateType)
        await state.filterSectionBox.initAsync();
        $(`${state.requirementsTable.state.selector} thead th .dt-column-order`).addClass('dt-column-order-custom');;
        const appliedCount = state.filterSectionBox.countAppliedFilters(state.tabId);
        updateFilterAppliedMessage(appliedCount);
        activeTab(currentTab);
    }

    async function getPreferences() {
        const response = await GlobalTrader.ApiClient.getAsync(`/user-account/profile/preferences`, {}, {});
        // handle error?
        return response?.data;
    }

    function setClientSelectState() {
        const clientInput = state.filter.getInputElementByName('Client');
        if(priceRequestPageState.IsPoHub || priceRequestPageState.IsGlobalUser || priceRequestPageState.IsGSA) {
            clientInput.wrapper.show();    
        } else {
            clientInput.wrapper.hide();    
        }
    }

    function updateFilterAppliedMessage(appliedCount) {
        let message;
        if (appliedCount === 0) {
            message = `${hubrfqLocalizedTitles.No} ${hubrfqLocalizedTitles.Filter} ${hubrfqLocalizedTitles.Applied}`;
        } else {
            const filterLabel = appliedCount === 1 ? (hubrfqLocalizedTitles.Filter) : (hubrfqLocalizedTitles.Filters);
            message = `${appliedCount} ${filterLabel} ${hubrfqLocalizedTitles.Applied}`;
        }
        state.filter.container.find('p[name="count-filter-applied"]').text(message);
    }

    function countFilterApplied(triggeredEvent) {
        const inputs = state.filter.getInputs();

        const appliedCount = (triggeredEvent === "off.mtf")
            ? 0
            : Object.values(inputs).reduce((count, input) => {
                const isApplied = Boolean(input.inputValue) && input.instance?.isChecked?.();
                return count + (isApplied ? 1 : 0);
            }, 0);

        return appliedCount;
    }

    function setupFilterCountEvents() {
        const events = ["applied.mtf", "off.mtf"];
        let requestFilterApplied = null;

        events.forEach(eventName => {
            state.filter.on(eventName, () => {
                requestFilterApplied = {
                    count: countFilterApplied(eventName)
                }
            });
        });

        state.filter.on('cancel.mtf', () => {
            requestFilterApplied = null;
        });

        state.filterSectionBox.on('processed.mftsb', () => {
            setTimeout(() => {
                if (requestFilterApplied) {
                    const latestAppliedCount = state.filterSectionBox.countAppliedFilters(state.tabId);
                    updateFilterAppliedMessage(latestAppliedCount);
                }
            }, 0);
        });
    }

    function getColorRequiredDateStatus(requiredDateStatus) {
        const statusColors = {
            Green: 'green',
            Yellow: '#FFBF00',
            Red: 'red',
            White: 'white'
        };

        return statusColors[requiredDateStatus] || '';
    }

    function renderPartAlternate(part, alternateStatus, atl) {
        if (part && alternateStatus > 0 && atl) {
            return `${part} (${custReqAlternative[alternateStatus]})`
        }
        return part;
    }

    function activeTab(tabId) {
        state.tabId = tabId;
        let tab = state.tabDic[tabId];

        $(`#${tab}`).trigger('click', {
            notResetPageRequired: true
        });
    }

    function prepareDataBeforeReload(filterData) {
        state.isSearchRequirement = filterData.HeaderOrDetail?.value == 'detail';
        let headerOrDetailValue = filterData.HeaderOrDetail?.value == 'detail' ? 'detail' : 'header';
        let requestData = {
            viewLevelList: state.tabId,
            headerOrDetail: headerOrDetailValue,
            status: filterData.Status?.value || null,
            assignedUser: filterData.AssignedUser?.value || null,
            manufacturer: !shouldInclude(filterData.Manufacturer) ? null : filterData.Manufacturer?.value,
            part: !shouldInclude(filterData.Part) ? null : filterData.Part?.value,
            startDate: !shouldInclude(filterData.StartDate) ? null : filterData.StartDate?.value,
            endDate: !shouldInclude(filterData.EndDate) ? null : filterData.EndDate?.value,
            division: filterData.Division?.value || null,
            salesPersonId: filterData.Salesperson?.value || null,
            companyTypeId: filterData.CompanyType?.value || null,
            as6081Required: filterData.AS6081Required?.value || null,
            selectedClientId: filterData.Client?.value || null,
            requiredEndDate: !shouldInclude(filterData.HUBRFQRequiredEndDate) ? null : filterData.HUBRFQRequiredEndDate?.value,
            requiredStartDate: !shouldInclude(filterData.HUBRFQRequiredStartDate) ? null : filterData.HUBRFQRequiredStartDate?.value,
            name: !shouldInclude(filterData.Name) ? null : filterData.Name?.value,
            code: !shouldInclude(filterData.Code) ? null : filterData.Code?.value
        }
        function shouldInclude(input) {
            return input?.isOn && input.isShown;
        }
        return requestData;
    }
    async function openningTab(tabId, ignoreSave, resetPageRequired) {
        // this section for example & testing
        //params for testing
        state.tabId = tabId;
        const data = state.filter.getAppliedValues();
        const convertedData = prepareDataBeforeReload(data)
        if (resetPageRequired) {
            state.requirementsTable.resetPage();
        }
        convertedData._ignoredSave = ignoreSave;
        await state.requirementsTable.reloadAsync(convertedData);
    }


    async function initAsync() {
        registerTabEvents();
        await initFilterTableSectionBoxAsync();
        registerButtonEvents();
        setHeaderOrDetailDefault();
        setClientSelectState();
        setupFilterCountEvents();
    }

    function registerButtonEvents() {
        state.addForm = new LiteFormDialog("#add-new-hub-form", {
            width: '400px',
            closeWhenSuccess: true,
            url: "/api/orders/bom/add-new",
            method: "POST",
            buttons: [
                { name: "save", icon: "check", alt: "yes", display: 'Yes' },
                { name: "cancel", icon: "xmark", alt: "no", display: 'No' }
            ],
            body: function () {
                //get information in bomdetails and bind on this
                return {
                    Name: "Name",
                    Company: "31100",
                    Contact: "128733",
                    CurrencyNo: "1",
                    QuoteRequired: "2025-11-11",
                    AS9120: true,
                    Contact2: "7292",
                    GeneratedBomID: "bom-1749540298431-583",
                    CompanyName: "Company of Electronic und Prod. GmbH",
                }
            }
        })

        state.addNewHub = new PartTableButton("#add-new-hub", () => {
            state.addForm.open();
        });
    }

    await initAsync();
})