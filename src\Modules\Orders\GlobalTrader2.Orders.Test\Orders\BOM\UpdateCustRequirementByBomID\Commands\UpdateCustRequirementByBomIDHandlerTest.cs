using GlobalTrader2.Core.StoreName;
using GlobalTrader2.Orders.UserCases.Orders.BOM.UpdateCustRequirementByBomID.Commands;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.BOM.UpdateCustRequirementByBomID.Commands;

public class UpdateCustRequirementByBomIDHandlerTest
{
    private readonly Mock<IBaseRepository<object>> _repositoryMock;
    private readonly UpdateCustRequirementByBomIDHandler _handler;
    private readonly IFixture _fixture;

    public UpdateCustRequirementByBomIDHandlerTest()
    {
        _fixture = new Fixture();
        _repositoryMock = new Mock<IBaseRepository<object>>();
        _handler = new UpdateCustRequirementByBomIDHandler(_repositoryMock.Object);
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldReturnSuccessResponse()
    {
        // Arrange
        var command = _fixture.Create<UpdateCustRequirementByBomIDCommand>();

        _repositoryMock
            .Setup(repo => repo.ExecuteSqlRawAsync(
                It.IsAny<string>(),
                It.IsAny<object[]>()))
            .Callback<string, object[]>((sql, parameters) =>
            {
                // Simulate setting the output parameter
                var sqlParams = parameters.Cast<SqlParameter>().ToArray();
                var outputParam = sqlParams.FirstOrDefault(p => p.ParameterName == "@RowsAffected");
                if (outputParam != null)
                {
                    outputParam.Value = 1;
                }
            })
            .ReturnsAsync(1);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.True(result.Data);
    }

    [Fact]
    public async Task Handle_WithNoRowsAffected_ShouldReturnFailureResponse()
    {
        // Arrange
        var command = _fixture.Create<UpdateCustRequirementByBomIDCommand>();

        _repositoryMock
            .Setup(repo => repo.ExecuteSqlRawAsync(
                It.IsAny<string>(),
                It.IsAny<object[]>()))
            .Callback<string, object[]>((sql, parameters) =>
            {
                // Simulate setting the output parameter to 0
                var sqlParams = parameters.Cast<SqlParameter>().ToArray();
                var outputParam = sqlParams.FirstOrDefault(p => p.ParameterName == "@RowsAffected");
                if (outputParam != null)
                {
                    outputParam.Value = 0;
                }
            })
            .ReturnsAsync(0);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.False(result.Data);
    }

    [Fact]
    public async Task Handle_ShouldUseCorrectStoredProcedureConstant()
    {
        // Arrange
        var command = _fixture.Create<UpdateCustRequirementByBomIDCommand>();

        _repositoryMock
            .Setup(repo => repo.ExecuteSqlRawAsync(
                It.IsAny<string>(),
                It.IsAny<object[]>()))
            .ReturnsAsync(1);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _repositoryMock.Verify(repo => repo.ExecuteSqlRawAsync(
            $"{StoredProcedures.Update_CustRequirementByBomID} @BomId, @UpdatedBy, @ClientNo, @ReqIds, @BOMStatus, @RowsAffected OUTPUT",
            It.IsAny<object[]>()),
            Times.Once);
    }

    [Fact]
    public async Task Handle_WithNullRequest_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _handler.Handle(null, CancellationToken.None));
    }
}

