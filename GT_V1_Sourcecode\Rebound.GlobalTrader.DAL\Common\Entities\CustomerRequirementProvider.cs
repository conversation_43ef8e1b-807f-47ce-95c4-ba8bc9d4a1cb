﻿/*

Marker     changed by      date         Remarks

[001]      Abhinav       31/05/2012     ESMS Ref:92 - Requirement Error - Urgent
[002]      <PERSON><PERSON><PERSON>   15-Oct-2018    Export all result instead of single page on HUBRFQ.
[003]      <PERSON><PERSON><PERSON>   27-Nov-2018    Show customer requirement all info in tree view.
[003]      <PERSON><PERSON> 17-Dec-2018    Customer Requirement Import functionality.
[004]      <PERSON><PERSON> 07-Jan-2019    Client BOM Items Details functionality.
[005]      <PERSON><PERSON> 10-Jan-2019    Save Client BOM Items  functionality.
[006]      <PERSON><PERSON> 11-Jan-2019    Update ClientBOMId Items   functionality.
[007]      Abhinav <PERSON>xena 14-Jul-2021   Add new function for partwatch matching.
[008]      Tanbirakhtar    13/06/2023    RP-37  Filter Added for industry type and Also added industry type on grid of UI
*/
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{

    public abstract class CustomerRequirementProvider : DataAccess
    {
        static private CustomerRequirementProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public CustomerRequirementProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (CustomerRequirementProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.CustomerRequirements.ProviderType));
                return _instance;
            }
        }
        public CustomerRequirementProvider()
        {
            this.ConnectionString = Globals.Settings.CustomerRequirements.ConnectionString;
            this.GTConnectionString = Globals.Settings.CustomerRequirements.GTConnectionString;
        }

        #region Method Registrations

        /// <summary>
        /// CountForClient
        /// Calls [usp_count_CustomerRequirement_for_Client]
        /// </summary>
        public abstract Int32 CountForClient(System.Int32? clientId);
        /// <summary>
        /// CountForCompany
        /// Calls [usp_count_CustomerRequirement_for_Company]
        /// </summary>
        public abstract Int32 CountForCompany(System.Int32? companyId, System.Boolean? includeClosed);
        /// <summary>
        /// CountOpenForCompany
        /// Calls [usp_count_CustomerRequirement_open_for_Company]
        /// </summary>
        public abstract Int32 CountOpenForCompany(System.Int32? companyId);
        /// <summary>
        /// DataListNugget
        /// Calls [usp_datalistnugget_CustomerRequirement]
        /// </summary>
        /// Code start[008]
        public abstract List<CustomerRequirementDetails> DataListNugget(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String contactSearch, System.String cmSearch, System.Int32? salesmanNo, System.Boolean? recentOnly, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.DateTime? datePromisedFrom, System.DateTime? datePromisedTo, System.Boolean? partWatch, System.String bomNameSearch, System.String bomCodeSearch, System.Double? totalLo, System.Double? totalHi, System.Int32? REQStatus, System.Int32? IndustryType, System.Boolean? AS6081, System.Int32? SelectedClientNo = null, System.Int32? SelectedLoginNo = null);
        /// Code End[008]
        /// <summary>
        /// Delete
        /// Calls [usp_delete_CustomerRequirement]
        /// </summary>
        public abstract bool Delete(System.Int32? customerRequirementId);
        /// <summary>
        /// Insert
        /// Calls [usp_insert_CustomerRequirement]
        /// </summary>
        public abstract Int32 Insert(System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packing, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode,System.Int32? ECCNNo, System.Boolean? AS6081);

        /// <summary>
		/// Insert
        /// Calls [usp_insert_APITokenNumber]
		/// </summary>
        public abstract Int32 InsertTokenNumberFromAPI(System.String TokenNumberInsert);

        /// <summary>
        /// Insert
        /// Calls [usp_insert_InsertFromIHSApi]
        /// </summary>
        public abstract Int32 InsertFromIHSApi(System.Int32? clientNo, System.Int64? PartID, System.String prtNbr, System.String mfrName, System.String prtStatus, System.String prtDesc, System.String mfrFullName, System.String coo, System.String htsCd, System.String msl, System.String pckMethod, System.String pckCd, System.String source, System.Double price, System.String currency, System.String telephone, System.String email, System.String priceAvailabilityLink);

        /// <summary>
        /// Insert
        /// Calls [usp_insert_IHSApiXML]
        /// </summary>
        public abstract Int32 InsertIHSApiXML(System.Int32? clientNo, System.Int32? updatedBy, System.String strXMLData);

        public abstract List<PartDetails> InsertIHSApiXML_BOMManager(System.Int32? clientNo, System.Int32? updatedBy, System.String strXMLData);

        /// <summary>
        /// InsertAsAlternate
        /// Calls [usp_insert_CustomerRequirement_as_Alternate]
        /// </summary>
        //public abstract Int32 InsertAsAlternate(System.String customerRequirementName, System.Int32? customerRequirementNumber, System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName,
        //    System.Int32? salesmanno, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Boolean? FactorySealed, System.String MSL, System.Int32? BOMNo, System.Int32? RequirementforTraceability, System.String EAU);
        /// <summary>
        /// InsertAsAllAlternate
        /// Calls [usp_insert_CustomerRequirement_as_AllAlternate]
        /// </summary>
        public abstract Int32 InsertAsAllAlternate(System.Int32? clientNo, System.String part, System.Int32? CustRequirementId);

        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_CustomerRequirement]
        /// </summary>
        public abstract List<CustomerRequirementDetails> ItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo);
        /// <summary>
        /// Get
        /// Calls [usp_select_CustomerRequirement]
        /// </summary>
        public abstract CustomerRequirementDetails Get(System.Int32? customerRequirementId);
        /// <summary>
        /// Get
        /// Calls [usp_select_CustomerRequirementBOM]
        /// </summary>
        public abstract CustomerRequirementDetails GetReqBOM(System.Int32? customerRequirementId, System.Int32? clientNo);
        /// <summary>
		/// GetByNumber
		/// Calls [usp_select_CustomerRequirement_by_Number]
		/// </summary>
		public abstract CustomerRequirementDetails GetByNumber(System.Int32? customerRequirementNumber, System.Int32? clientNo);
        /// <summary>
        /// GetForPage
        /// Calls [usp_select_CustomerRequirement_for_Page]
        /// </summary>
        public abstract CustomerRequirementDetails GetForPage(System.Int32? customerRequirementId);
        /// <summary>
        /// GetIdByNumber
        /// Calls [usp_select_CustomerRequirement_Id_by_Number]
        /// </summary>
        public abstract CustomerRequirementDetails GetIdByNumber(System.Int32? customerRequirementNumber, System.Int32? clientNo);
        /// <summary>
        /// GetNextNumber
        /// Calls [usp_select_CustomerRequirement_NextNumber]
        /// </summary>
        public abstract CustomerRequirementDetails GetNextNumber(System.Int32? clientNo, System.Int32? updatedBy);
        /// <summary>
        /// GetNumberById
        /// Calls [usp_select_CustomerRequirement_Number_by_Id]
        /// </summary>
        public abstract CustomerRequirementDetails GetNumberById(System.Int32? customerRequirementId);
        /// <summary>
        /// GetListForCompany
        /// Calls [usp_selectAll_CustomerRequirement_for_Company]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetListForCompany(System.Int32? companyId, System.Boolean? includeClosed);
        /// <summary>
        /// GetListForCustomerRequirementNumber
        /// Calls [usp_selectAll_CustomerRequirement_for_CustomerRequirementNumber]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetListForCustomerRequirementNumber(System.Int32? customerRequirementNumber, System.Int32? clientID);


        // [001] code start
        /// <summary>
        /// GetListForCustomerRequirement
        /// Calls [usp_selectAll_CustomerRequirement_for_CustomerRequirement]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetListForCustomerRequirement(System.Int32? customerRequirementNo, System.Int32? clientID, System.Boolean? IsGSA = null);
        // [001] code end

        /// <summary>
        /// GetListOpenForCompany
        /// Calls [usp_selectAll_CustomerRequirement_open_for_Company]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetListOpenForCompany(System.Int32? companyId);
        /// <summary>
        /// GetListOpenForLogin
        /// Calls [usp_selectAll_CustomerRequirement_open_for_Login]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetListOpenForLogin(System.Int32? loginId, System.Int32? topToSelect);
        /// <summary>
        /// GetListOverdueForLogin
        /// Calls [usp_selectAll_CustomerRequirement_overdue_for_Login]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetListOverdueForLogin(System.Int32? loginId, System.Int32? topToSelect);
        /// <summary>
        /// Source
        /// Calls [usp_source_CustomerRequirement]
        /// </summary>
        public abstract List<CustomerRequirementDetails> Source(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal);

        //code added by anand for customer requierment
        /// <summary>
        /// Source
        /// Calls [usp_CrossMatch_CustomerRequirement]
        /// </summary>
        public abstract List<CustomerRequirementDetails> SourceCustomerRequirement(System.Int32? clientId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.String sortDir, System.String partSearch, System.String PartMatch, System.String months, System.Int32? monthTime, System.Int32? vendorNo, System.Int32? currencyNo, System.Boolean? isManufaurer, System.Int32? NoOfTopRecord, bool IsServerLocal, System.Boolean? isPohub, System.Int32? BomID, System.Boolean? IncludeAltPart, System.Int32? ReqId);
        //code end
        /// <summary>
        /// 
        /// </summary>
        /// <param name="HUBRFQId"></param>
        /// <param name="expediteNotes"></param>
        /// <param name="UpdatedBy"></param>
        /// <param name="ReqIds"></param>
        /// <returns></returns>
        public abstract int InsertExpedite(int? HUBRFQId, string expediteNotes, int? UpdatedBy, string ReqIds, System.Int32? emailSendTo, System.String CCUserID, System.String SendToGroup);

        public abstract int InsertBOMExpediteNote(int? HUBRFQId, string expediteNotes, int? UpdatedBy, string ReqIds, System.Int32? emailSendTo, System.String CCUserID, System.String SendToGroup);

        public abstract int InsertHUBRFQExpedite(int? HUBRFQId, string expediteNotes, int? UpdatedBy, System.Int32? emailSendTo);


        /// <summary>
        /// Update
        /// Calls [usp_update_CustomerRequirement]
        /// </summary>
        //        public abstract bool Update(System.Int32? customerRequirementId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? bomNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packaging, System.String PackagingSize);
        public abstract bool Update(System.Int32? customerRequirementId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? bomNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packaging, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo, System.Boolean? AS6081);

        /// <summary>
        /// Update
        /// Calls [usp_update_CustRequirementByBomID]
        /// </summary>
        public abstract bool Update(System.Int32? bomID, System.Int32? updatedBy, System.Int32? clientNo, System.String ReqsId, System.Int32? BOMStatus);

        /// <summary>
		/// UpdateClose
		/// Calls [usp_update_CustomerRequirement_Close]
		/// </summary>
		public abstract bool UpdateClose(System.Int32? customerRequirementId, System.Boolean? includeAllRelatedAlternates, System.Int32? reasonNo, System.Int32? updatedBy);

        /// <summary>
        /// Delete
        /// Calls [[usp_delete_CustomerReq_AlternatePart]]
        /// </summary>
        public abstract bool DeleteAlternateParts(System.Int32? AlternatePartid);

        /// <summary>
        /// DataListNuggetPrint
        /// Calls [usp_datalistnugget_CustomerRequirementPrint]
        /// </summary>
        public abstract List<CustomerRequirementDetails> DataListNuggetPrint(System.Int32? clientId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String cmSearch, System.Int32? salesmanNo, System.Int32? companyNo, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.DateTime? datePromisedFrom, System.DateTime? datePromisedTo, System.Int32? contactNo);

        /// <summary>
        /// DataListNuggetImport
        /// Calls [usp_datalistnugget_CustomerRequirementImport]
        /// </summary>
        public abstract List<CustomerRequirementDetails> DataListNuggetImport(System.Int32? clientId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String clientBOMName, System.Int32? salesmanNo, System.String companyNo, System.DateTime? importDateFrom, System.DateTime? importDateTo, System.Int32? status);

        /// <summary>
        /// Print Requirement
        /// Calls [usp_Print_CustomerRequirement_Enquiry_Form]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetForPrint(System.String xmlReqNo);
        /// <summary>
        /// Release
        /// Calls [usp_update_CustomerRequirement_Release]
        /// </summary>
        public abstract bool ReleaseRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy, System.Int32? bomID);

        /// <summary>
        /// Release
        /// Calls [usp_update_BOM_Release]
        /// </summary>
        public abstract bool BOMReleaseRequirement(System.Int32? bomID, System.Int32? updatedBy);
        /// <summary>
        ///Recall NoBid
        /// Calls [usp_update_CustomerRequirement_RecallNoBid]
        /// </summary>
        public abstract bool RecallNoBidRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy);

        /// <summary>
        /// NoBid
        /// Calls [usp_update_CustomerRequirement_NoBid]
        /// </summary>
        public abstract bool NoBidRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy, System.Int32? bomID, string Notes);

        /// <summary>
        /// NoBid
        /// Calls [usp_update_BOM_NoBid]
        /// </summary>
        public abstract bool BOMNoBidRequirement(System.Int32? bomID, System.Int32? updatedBy, string Notes);
        #endregion

        /// <summary>
        /// Returns a new CustomerRequirementDetails instance filled with the DataReader's current record data
        /// </summary>        
        protected virtual CustomerRequirementDetails GetCustomerRequirementFromReader(DbDataReader reader)
        {
            CustomerRequirementDetails customerRequirement = new CustomerRequirementDetails();
            if (reader.HasRows)
            {
                customerRequirement.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0); //From: [Table]
                customerRequirement.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0); //From: [Table]
                customerRequirement.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0); //From: [Table]
                customerRequirement.FullPart = GetReaderValue_String(reader, "FullPart", ""); //From: [Table]
                customerRequirement.Part = GetReaderValue_String(reader, "Part", ""); //From: [Table]
                customerRequirement.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null); //From: [Table]
                customerRequirement.DateCode = GetReaderValue_String(reader, "DateCode", ""); //From: [Table]
                customerRequirement.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null); //From: [Table]
                customerRequirement.Quantity = GetReaderValue_Int32(reader, "Quantity", 0); //From: [Table]
                customerRequirement.Price = GetReaderValue_Double(reader, "Price", 0); //From: [Table]
                customerRequirement.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null); //From: [Table]
                customerRequirement.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue); //From: [Table]
                customerRequirement.Salesman = GetReaderValue_Int32(reader, "Salesman", 0); //From: [Table]
                customerRequirement.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue); //From: [Table]
                customerRequirement.Notes = GetReaderValue_String(reader, "Notes", ""); //From: [Table]
                customerRequirement.Instructions = GetReaderValue_String(reader, "Instructions", ""); //From: [Table]
                customerRequirement.Shortage = GetReaderValue_Boolean(reader, "Shortage", false); //From: [Table]
                customerRequirement.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0); //From: [Table]
                customerRequirement.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0); //From: [Table]
                customerRequirement.Alternate = GetReaderValue_Boolean(reader, "Alternate", false); //From: [Table]
                customerRequirement.OriginalCustomerRequirementNo = GetReaderValue_NullableInt32(reader, "OriginalCustomerRequirementNo", null); //From: [Table]
                customerRequirement.ReasonNo = GetReaderValue_NullableInt32(reader, "ReasonNo", null); //From: [Table]
                customerRequirement.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null); //From: [Table]
                customerRequirement.CustomerPart = GetReaderValue_String(reader, "CustomerPart", ""); //From: [Table]
                customerRequirement.Closed = GetReaderValue_Boolean(reader, "Closed", false); //From: [Table]
                customerRequirement.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0); //From: [Table]
                customerRequirement.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null); //From: [Table]
                customerRequirement.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue); //From: [Table]
                customerRequirement.UsageNo = GetReaderValue_NullableInt32(reader, "UsageNo", null); //From: [Table]
                customerRequirement.FullCustomerPart = GetReaderValue_String(reader, "FullCustomerPart", ""); //From: [Table]
                customerRequirement.BOM = GetReaderValue_NullableBoolean(reader, "BOM", null); //From: [Table]
                customerRequirement.BOMName = GetReaderValue_String(reader, "BOMName", ""); //From: [Table]
                customerRequirement.PartWatch = GetReaderValue_NullableBoolean(reader, "PartWatch", null); //From: [Table]
                customerRequirement.SalesmanName = GetReaderValue_String(reader, "SalesmanName", ""); //From: [usp_select_Credit]
                customerRequirement.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", ""); //From: [usp_datalistnugget_CustomerRequirement]
                customerRequirement.CompanyName = GetReaderValue_String(reader, "CompanyName", ""); //From: [usp_select_Credit]
                customerRequirement.ContactName = GetReaderValue_String(reader, "ContactName", ""); //From: [usp_select_Credit]
                customerRequirement.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null); //From: [usp_datalistnugget_CustomerRequirement]
                customerRequirement.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", ""); //From: [usp_select_Credit]
                customerRequirement.DisplayStatus = GetReaderValue_String(reader, "DisplayStatus", ""); //From: [usp_select_CustomerRequirement]
                customerRequirement.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null); //From: [Table]
                customerRequirement.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null); //From: [usp_select_Credit]
                customerRequirement.CompanyOnStop = GetReaderValue_NullableBoolean(reader, "CompanyOnStop", null); //From: [usp_select_CustomerRequirement]
                customerRequirement.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", ""); //From: [usp_select_Credit]
                customerRequirement.ProductName = GetReaderValue_String(reader, "ProductName", ""); //From: [usp_select_CustomerRequirement]
                customerRequirement.ProductDescription = GetReaderValue_String(reader, "ProductDescription", ""); //From: [usp_select_CustomerRequirement]
                customerRequirement.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", ""); //From: [usp_select_CustomerRequirement]
                customerRequirement.PackageName = GetReaderValue_String(reader, "PackageName", ""); //From: [usp_select_CustomerRequirement]
                customerRequirement.PackageDescription = GetReaderValue_String(reader, "PackageDescription", ""); //From: [usp_select_CustomerRequirement]
                customerRequirement.UsageName = GetReaderValue_String(reader, "UsageName", ""); //From: [usp_select_CustomerRequirement]
                customerRequirement.CustomerRequirementValue = GetReaderValue_Double(reader, "CustomerRequirementValue", 0); //From: [usp_select_CustomerRequirement]
                customerRequirement.ClosedReason = GetReaderValue_String(reader, "ClosedReason", ""); //From: [usp_select_CustomerRequirement]
                customerRequirement.DivisionName = GetReaderValue_String(reader, "DivisionName", ""); //From: [usp_select_Credit]
                customerRequirement.Status = GetReaderValue_String(reader, "Status", ""); //From: [usp_selectAll_CustomerRequirement_open_for_Login]
                customerRequirement.CreditLimit = GetReaderValue_NullableDouble(reader, "CreditLimit", null); //From: [usp_selectAll_CustomerRequirement_open_for_Login]
                customerRequirement.Balance = GetReaderValue_NullableDouble(reader, "Balance", null); //From: [usp_selectAll_CustomerRequirement_open_for_Login]
                customerRequirement.DaysOverdue = GetReaderValue_NullableInt32(reader, "DaysOverdue", null); //From: [usp_selectAll_CustomerRequirement_open_for_Login]
                customerRequirement.ClientName = GetReaderValue_String(reader, "ClientName", ""); //From: [usp_source_CustomerRequirement]
            }
            return customerRequirement;
        }

        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_CustomerReqsWithBOM]
        /// </summary>
        public abstract List<CustomerRequirementDetails> ItemSearchWithBOM(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.Int32? client, System.String bomName);


        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_CustRequirementWithoutBOM]
        /// </summary>
        public abstract List<CustomerRequirementDetails> ItemSearchWithoutBOM(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.Int32? bomId, System.String BOMName);

        /// <summary>
        /// Returns a collection of CustomerRequirementDetails objects with the data read from the input DataReader
        /// </summary>                
        protected virtual List<CustomerRequirementDetails> GetCustomerRequirementCollectionFromReader(DbDataReader reader)
        {
            List<CustomerRequirementDetails> customerRequirements = new List<CustomerRequirementDetails>();
            while (reader.Read()) customerRequirements.Add(GetCustomerRequirementFromReader(reader));
            return customerRequirements;
        }
        // [001] code start
        /// <summary>
        /// GetBOMListForCustomerRequirement 
        /// Calls [usp_selectAll_CustomerRequirement_for_BOM]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetBOMListForCustomerRequirement(System.Int32? BOMNo, System.Int32? clientID, System.Boolean isPOHub);
        // [001] code end

        //by anand
        // [000] code start
        /// <summary>
        /// GetBOMListForCustomerRequirement Cross match
        /// Calls [usp_selectAll_CustomerRequirement_for_BOM_CorssMatchOffer]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetBOMListForCustomerRequirement_Offer(System.Int32? BOMNo, System.Int32 customerReqID, System.Int32? clientID);
        // [000] code end
        //end by anand


        public abstract List<CustomerRequirementDetails> GetHUBRFQReqNos(String ReqIds, System.Int32? clientID);
        /// <summary>
        /// This use for Mail Template of HURFQ
        /// </summary>
        /// <param name="BOMNo"></param>
        /// <param name="clientID"></param>
        /// Calls [usp_selectCusReqForMail]
        /// <returns></returns>
        public abstract List<CustomerRequirementDetails> GetHUBRFQForMail(System.Int32? BOMNo, System.Int32? clientID);

        /// <summary>
        /// GetBOMListForCRList
        /// Calls [usp_select_CustomerRequirements_for_BOM]
        /// </summary>
        public abstract List<List<object>> GetBOMListForCRList(System.Int32? BOMNo, System.Int32? clientID, System.Int32? CompanyNo);
        /// <summary>
        /// 
        protected virtual List<List<object>> GetCustReqDataFromReader(DbDataReader reader)
        {
            List<List<object>> lstReports = new List<List<object>>();
            while (reader.Read()) lstReports.Add(GetCustReqDataRowFromReader(reader));
            return lstReports;
        }

        protected virtual List<object> GetCustReqDataRowFromReader(DbDataReader reader)
        {
            List<object> lst = new List<object>(reader.FieldCount);
            for (int i = 0; i < reader.FieldCount; i++)
            {
                var val = reader.GetValue(i);
                lst.Add(val);
            }
            return lst;
        }


        /// <summary>
        /// delete 
        /// Calls [usp_delete_CustomerRequirement_Bom]
        /// </summary>
        public abstract bool DeleteBomItem(int? bomId, int? requirementId, System.Int32? LoginID, System.Int32? clientId);

        /// <summary>
        /// delete 
        /// Calls [usp_UnRelease_CustomerRequirement_Bom]
        /// </summary>
        public abstract bool UnReleaseBomItem(int? bomId, int? requirementId);

        /// <summary>
        /// DataListNugget for HURFQ
        /// Calls [usp_datalistnugget_CustomerRequirementForHUBRFQ]
        /// add start date and end date  for searching by umendra
        /// </summary>
        public abstract List<CustomerRequirementDetails> DataListNuggetHUBRFQ(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.DateTime? RequiredStartdate, System.DateTime? RequiredEndDate, System.Int32? salesPerson, System.Int32? AS6081Required, System.Int32? SelectedLoginId = null);
        /// <summary>
        /// usp_update_CustReqAlternate_Status
        /// </summary>
        /// <param name="customerRequirementId"></param>
        /// <param name="altStatus"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract bool ChangeAltStatus(System.Int32? customerRequirementId, System.Byte? altStatus, System.Int32? updatedBy);
        //[002] start
        public abstract DataTable DataListNuggetHUBRFQ_Export(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.Int32? salesPerson);
        //ihs export start
        public abstract DataTable DataListNuggetIHS_Export(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String MfrSearch, System.Int32? countryOforigin, System.String MSL, System.String HtcCode, System.String Description, System.Boolean? recentOnly, int? IsPoHub);
        //ihs export end
        //[002] end
        public abstract DataTable GetBOMDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int BomId);
        public abstract DataTable GetCustTableAllColumn();
        public abstract DataTable GetExcelHeader(System.Int32 clientBomId, System.Int32 userId);
        public abstract void SaveMappingDetail(int columnIndex, int columnId, int ClientBOMId, int userId);
        public abstract void AutoMapRemainingColumn(int ClientBOMId, int userId);
        public abstract bool ValidateBOMData(string RecordIds, int bomId, int userId);
        public abstract int SaveCompanyColumnMapping(int ClientBOMId);
        public abstract void SaveUpdateRecord(DataTable dt, int userid);
        public abstract void UpdateCell(DataTable dt, int userid);
        public abstract void DeleteTempMapping(int bomId, int userId);
        public abstract int DeleteRecord(int recordId);
        public abstract DataTable GetBOMInfo(int BOMId);
        public abstract DataTable GetCompanyAndOtherMasterData(int CompanyId);
        public abstract DataTable GetSalespersonList(int clientId);
        public abstract DataTable GetAllColumn(int bomId, int userId);
        public abstract DataTable GetMappedColumn(int clientBomId, int loginId);
        public abstract int ProcessRecord(int bomId, int userId, int currentIndex, int pageSize, string recordIds);
        public abstract int UpdateBOMStatus(int bomId, string status);
        public abstract void saveExcelData(DataTable dt, int bomId);
        public abstract DataTable GetRecordDetail(int bomId);
        public abstract DataTable GetValidationError(int bomId, int userId, string RecordIds);
        public abstract Int32 saveBomFileInfo(int bomId, string caption, string filename, int loginId);
        public abstract void SaveExcelHeader(string columnList, string insertColumnList, int bomId, int loginId);
        public abstract List<PDFDocumentDetails> GetBomUploadedFiles(int bomId);
        public abstract void ResetBomData(int bomId, int userId, int fileLogId);
        public abstract void RearrangeColumnData(int bomId, int userId, string insertColumnList, string selectColumnList);
        public abstract void ReArrangeCompanyMapping(int columnIndex, int columnId, int ClientBOMId, int userId);
        public abstract void UpdateCompanyMapping(int bomId, int userId);
        public abstract bool IsCompanyMappingExists(int bomId);
        public abstract DataTable GetCurrencyCode(string currencyCode);
        public abstract DataTable GetClientCurrencyCode(string currencyCode, int? ClientNo);

        // [004] code start
        /// <summary>
        /// GetClientBOMListForCustomerRequirement 
        /// Calls [usp_selectAll_CustomerRequirement_for_ClientBOM]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetClientBOMListForCustomerRequirement(System.Int32? BOMNo, System.Int32? clientID);
        // [004] code end

        // [006] code start
        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_CustRequirementWithoutClientBOM]
        /// </summary>
        public abstract List<CustomerRequirementDetails> ItemSearchWithoutClientBOM(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.Int32? bomId, System.String BOMName);
        public abstract List<CustomerRequirementDetails> DataListNuggetHUBRFQBOMManager(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.Int32? salesPerson, System.Int32? MailGroupId);
        /// <summary>
        /// Update
        /// Calls [usp_update_CustRequirementByClientBomID]
        /// </summary>
        public abstract bool ClientBOMItemsUpdate(System.Int32? bomID, System.Int32? updatedBy, System.Int32? clientNo, System.String ReqsId, System.Int32? BOMStatus);
        // [006] code end

        /// <summary>
        /// Update
        /// Calls [usp_update_CustRequirementByClientBomID]
        /// </summary>
        public abstract bool ClientBOMCustomerRequirementWithHUBRFQUpdate(System.Int32? bomID, System.Int32? CompanyNo, System.Int32? CustomerRequirementNumber, System.Int32? clientNo);
        // [006] code end
        // [003] code start
        /// <summary>
        /// usp_Get_CustReq_Info
        /// Calls [usp_Get_CustReq_Info]
        /// </summary>
        public abstract List<CustomerRequirementDetails> GetListCustomerAllInfo(System.String customerRequirementNo, System.String actionType);
        // [003] code end

        //start code by umendra
        public abstract void SaveUpdateTempCusReqData(int tempid, int bomid, int clientid, string manufacturename);
        public abstract void SaveUpdateTempSourcingData(int sourcingresultid, string suppliername, int clientid, string manufacturename);
        //end code by umendra
        /// <summary>
        /// Clone the existing customer requirement and also add existing HUBRFQ
        /// Calls [usp_CloneRequirementDataHUBRFQ]
        /// </summary>
        /// <param name="customerRequirementNumber"></param>
        /// <param name="hubrfqId"></param>
        /// <param name="clientNo"></param>
        /// <param name="loginId"></param>
        /// <returns></returns>
        //public abstract Int32 CloneRequirementDataHUBRFQ(System.Int32 customerRequirementNumber, System.Int32 hubrfqId, System.Int32? clientNo, System.Int32 loginId);
        /// <summary>
        /// Calls [usp_CloneRequirementDataHUB]
        /// </summary>
        /// <param name="customerRequirementNumber"></param>
        /// <param name="clientNo"></param>
        /// <param name="loginId"></param>
        /// <returns></returns>
        //public abstract Int32 CloneRequirementDataHUB(System.Int32 customerRequirementNumber, System.Int32? clientNo, System.Int32 loginId);


        /// <summary>
        /// Insert
        /// Calls [usp_insert_CloneRequirementDataHUBRFQ]
        /// </summary>
        public abstract Int32 CloneRequirementDataHUBRFQ(System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo,System.Int32? CustReqNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packaging, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo);

        /// <summary>
        /// Insert
        /// Calls [usp_insert_CloneRequirementDataHUB]
        /// </summary>
        public abstract Int32 CloneRequirementDataHUB(System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.Int32? CustReqNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packaging, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo);

        public abstract DataTable GetPartMachInfo(System.Int32? clientId, System.String partSearch, System.Int32? CustomerRequirementId);

        public abstract DataTable GetPartMachHUBIPOInfo(System.Int32? clientId, System.Int32? updatedBy, System.Int32? CustomerRequirementId);

        //public abstract void AddSourcingResultsforMatchedOffers(System.Int32? CustomerRequirementId, DataTable dtOffer);

        /// <summary>
        /// Calls [usp_insert_CustomerRequirement_as_Alternate] 
        /// </summary>
        /// <param name="customerRequirementId"></param>
        /// <param name="part"></param>
        /// <param name="manufacturerNo"></param>
        /// <param name="dateCode"></param>
        /// <param name="packageNo"></param>
        /// <param name="quantity"></param>
        /// <param name="price"></param>
        /// <param name="currencyNo"></param>
        /// <param name="receivedDate"></param>
        /// <param name="salesman"></param>
        /// <param name="datePromised"></param>
        /// <param name="notes"></param>
        /// <param name="instructions"></param>
        /// <param name="shortage"></param>
        /// <param name="companyNo"></param>
        /// <param name="contactNo"></param>
        /// <param name="usageNo"></param>
        /// <param name="alternate"></param>
        /// <param name="originalCustomerRequirementNo"></param>
        /// <param name="reasonNo"></param>
        /// <param name="productNo"></param>
        /// <param name="customerPart"></param>
        /// <param name="closed"></param>
        /// <param name="rohs"></param>
        /// <param name="updatedBy"></param>
        /// <param name="partWatch"></param>
        /// <param name="bom"></param>
        /// <param name="bomName"></param>
        /// <param name="bomNo"></param>
        /// <param name="FactorySealed"></param>
        /// <param name="MSL"></param>
        /// <param name="PQA"></param>
        /// <param name="ObsoleteChk"></param>
        /// <param name="LastTimeBuyChk"></param>
        /// <param name="RefirbsAcceptableChk"></param>
        /// <param name="TestingRequiredChk"></param>
        /// <param name="TargetSellPrice"></param>
        /// <param name="CompetitorBestOffer"></param>
        /// <param name="CustomerDecisionDate"></param>
        /// <param name="RFQClosingDate"></param>
        /// <param name="QuoteValidityRequired"></param>
        /// <param name="Type"></param>
        /// <param name="OrderToPlace"></param>
        /// <param name="RequirementforTraceability"></param>
        /// <param name="EAU"></param>
        /// <param name="AlternativesAccepted"></param>
        /// <param name="RepeatBusiness"></param>
        /// <param name="SupportTeamMemberNo"></param>
        /// <param name="CountryOfOrigin"></param>
        /// <param name="CountryOfOriginNo"></param>
        /// <param name="LifeCycleStage"></param>
        /// <param name="HTSCode"></param>
        /// <param name="AveragePrice"></param>
        /// <param name="Packaging"></param>
        /// <param name="PackagingSize"></param>
        /// <param name="Descriptions"></param>
        /// <param name="IHSPartsId"></param>
        /// <param name="IHSCurrencyCode"></param>
        /// <param name="IHSProduct"></param>
        /// <param name="ECCNCode"></param>
        /// /// <param name="ECCNNo"></param>
        /// <returns></returns>
        public abstract Int32 AddAlternateNew(System.Int32? customerRequirementId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? bomNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packaging, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo);

        ///// <summary>
        ///// Get
        ///// Calls [usp_select_PartFoundInReverseLogistics]
        ///// </summary>
        public abstract CustomerRequirementDetails GetRLPart(System.String PartNo);
        ///// <summary>
        ///// Get
        ///// Calls [usp_Notifiy_RLVendor]
        ///// </summary>
        public abstract string GetPartLinesRL(System.String strpartNo);
        ///// <summary>
        ///// Get
        ///// Calls [usp_UpsertLyticaAPI]
        ///// </summary>
        public abstract List<LyticaAPI> UpsertLyticaAPIData(string APIResponseJson, int? UpdatedBy);
        ///// <summary>
        ///// Get
        ///// Calls [usp_Get_LyticaAPIData_ByKey]
        ///// </summary>
        public abstract bool IsExistLyticaAPIDataOver3Days(string partNo, string mfrCode, int mfrNo);
        ///// <summary>
        ///// Get
        ///// Calls [usp_Get_LyticaAPIData_By_PartMfr]
        ///// </summary>
        public abstract LyticaAPI GetLyticaDataByPartMfr(System.String partNo, System.String manufacturerCode, System.String manufacturerName);
        ///// <summary>
        ///// Get
        ///// Calls [usp_Get_ManufacturerName_By_Code]
        ///// </summary>
        public abstract string GetManufacturerNameByCode(string manufacturerCode, string mfrNo);
        ///// <summary>
        ///// Get
        ///// Calls [usp_Get_List_CusReq_PowerApp_By_Ids]
        ///// </summary>
        public abstract List<ProspectiveOfferForPowerApp> ListEmailPowerApp(List<int> customerRequirementIds, string flowName);
        ///// <summary>
        ///// Get
        ///// Calls [usp_Get_List_CustomerRequirement_Details_By_Ids]
        ///// </summary>
        public abstract List<CustomerRequirementDetails> List(List<int> customerRequirementIds);
        ///// <summary>
        ///// Get
        ///// Calls [usp_Get_CusReq_Having_Available_RLStock]
        ///// </summary>
        public abstract List<CustomerRequirementDetails> GetHUBRFQHasRLStock(int? bomId);
        ///// <summary>
        ///// Get
        ///// Calls [usp_Get_CusReq_Having_Available_RLStock_By_Part]
        ///// </summary>
        public abstract List<CustomerRequirementDetails> GetHUBRFQHasRLStockByPart(string part);

        ///// <summary>
        ///// Get
        ///// Calls [usp_GetLyticaDataOnHUBRFQ_ByPartMfr]
        ///// </summary>
        public abstract LyticaAPI GetLyticaDataOnHUBRFQByPartMfr(System.Int32 cusReqNo, System.String mfrName);
    }
}
