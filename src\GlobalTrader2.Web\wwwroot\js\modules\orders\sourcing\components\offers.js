﻿$(() => {
    const isPoHub = $("#is-po-hub").val() === "true";

    initializeTable({
        tableId: "offers-table",
        legendId: "legend-Offers",
        rowId: "offerId",
        columns: [
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [offersLocalizedStrings.quantity, offersLocalizedStrings.status]),
                render: (row) => GlobalTrader.DataTablesHelper.createStackedCell([row?.quantity, row?.offerStatus ?? ""]),
                type: "string",
                className: "text-break"
            },
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [offersLocalizedStrings.partNo, offersLocalizedStrings.additionalNotesFromSupplier]),
                render: (row) => GlobalTrader.DataTablesHelper.createStackedCell([getPartNoAndRohsStatus(row.part, row.rohs), row.notes]),
                className: "text-break"
            },
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [offersLocalizedStrings.mfr, offersLocalizedStrings.dc]),
                render: function (row) {
                    let mfrDisplayText = getMfrContent(row.manufacturerNo, row.manufacturerCode ?? row.manufacturerName, row.isRestrictedManufacturer);
                    return GlobalTrader.DataTablesHelper.createStackedCell([mfrDisplayText, row.dateCode])
                },
                className: "text-break"
            },
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [offersLocalizedStrings.product, offersLocalizedStrings.package]),
                render: (row) => GlobalTrader.DataTablesHelper.createStackedCell([row.productName, row.packageName]),
                className: "text-break"
            },
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [offersLocalizedStrings.supplier, offersLocalizedStrings.type]),
                render: (row) => GlobalTrader.DataTablesHelper.createStackedCell([
                    getSuplierMessage(row, isPoHub),
                    row.supplierType]),
                className: "text-break"
            },
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [offersLocalizedStrings.offered, offersLocalizedStrings.by]),
                render: (row) => GlobalTrader.DataTablesHelper.createStackedCell(
                    [GlobalTrader.DatetimeHelper.formatDate(new Date(row.offerStatusChangeDate)),
                    row.salesmanName ?? row.offerStatusChangeEmployeeName]),
                className: "text-break"
            },
            {
                data: null, title: offersLocalizedStrings.unitPrice,
                render: (row) => formatCurrencyRate(row.price, row.currencyCode),
                type: "string",
                className: "text-break"
            }
        ],
        onSelectDeselect: (table) => {
            const selectedRows = table.rows({ selected: true }).data().toArray();
            const selectedRow = selectedRows.length === 1 ? selectedRows[0] : null;

            const canEditOffer = selectedRow?.currentClient && !selectedRow?.ishub;
            setButtonState("#edit-offer", !!canEditOffer);

            const hasUnrestricted = selectedRows.some(row => row.isRestrictedManufacturer === false);
            const hasNonIshub = selectedRows.some(row => row.ishub === false);
            const canAddToRequirement = (!isRequirementPage() || !stateValue?.selectedRequirementDetails?.closed) && selectedRows.length > 0 && hasUnrestricted && hasNonIshub;
            setButtonState("#offer-add-to-requirement", canAddToRequirement);

            const canCloneAndAddToRequirement = selectedRow && isHUBRFQDetails() && !stateValue?.selectedRequirementDetails?.closed && selectedRows.length && hasUnrestricted && hasNonIshub;
            setButtonState("#clone-offers-and-add-to-requirement", canCloneAndAddToRequirement);
        }
    });
});
