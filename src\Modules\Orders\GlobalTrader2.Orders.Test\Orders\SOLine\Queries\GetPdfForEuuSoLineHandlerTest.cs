﻿using AutoMapper;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Dto.Manufacturers.Document;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetPdfForEuuSoLine;
using Moq;
using System.Linq.Expressions;

namespace GlobalTrader2.Orders.Test.Orders.SOLine.Queries
{
    public class GetPdfForEuuSoLineHandlerTest
    {
        private readonly Mock<IBaseRepository<SoLineEuuPdf>> _soLineEuuPdfRepositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly GetPdfForEuuSoLineHandler _handler;

        public GetPdfForEuuSoLineHandlerTest()
        {
            _soLineEuuPdfRepositoryMock = new Mock<IBaseRepository<SoLineEuuPdf>>();
            _mapperMock = new Mock<IMapper>();
            _handler = new GetPdfForEuuSoLineHandler(_soLineEuuPdfRepositoryMock.Object, _mapperMock.Object);
        }

        [Fact]
        public async Task Handle_ReturnsPdfDocuments_WhenPdfsExist()
        {
            // Arrange  
            var request = new GetPdfForEuuSoLineQuery { SalesOrderLineId = 1, FileType = "EUU", Culture = "en-US" };
            var soLineEuuPdfs = new List<SoLineEuuPdf>
                {
                    new SoLineEuuPdf { SoLineEuuPdfId = 1, SoLineNo = 1, FileType = "EUU" }
                };
            var documentDtos = new List<DocumentDto>
                {
                    new DocumentDto { DocumentId = 1 }
                };

            _ = _soLineEuuPdfRepositoryMock.Setup(r => r.ListAsync(
                It.IsAny<Expression<Func<SoLineEuuPdf, bool>>>(),
                It.IsAny<Func<IQueryable<SoLineEuuPdf>, IOrderedQueryable<SoLineEuuPdf>>>(),
                It.IsAny<Expression<Func<SoLineEuuPdf, object?>>[]>()))
                .ReturnsAsync(soLineEuuPdfs);

            _mapperMock.Setup(m => m.Map<IEnumerable<DocumentDto>>(soLineEuuPdfs, It.IsAny<Action<IMappingOperationOptions<object, IEnumerable<DocumentDto>>>>()))
                .Returns(documentDtos);

            // Act  
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert  
            Assert.True(result.Success);
            Assert.Equal(documentDtos, result.Data);
            _soLineEuuPdfRepositoryMock.Verify(r => r.ListAsync(
                It.IsAny<Expression<Func<SoLineEuuPdf, bool>>>(),
                It.IsAny<Func<IQueryable<SoLineEuuPdf>, IOrderedQueryable<SoLineEuuPdf>>>(),
                x => x.Login), Times.Once);
        }

        [Fact]
        public async Task Handle_VerifiesOrderingAndCultureMapping_WhenCallingDependencies()
        {
            // Arrange
            var request = new GetPdfForEuuSoLineQuery { SalesOrderLineId = 123, FileType = "PDF", Culture = "fr-FR" };
            var soLineEuuPdfs = new List<SoLineEuuPdf>
            {
                new SoLineEuuPdf { SoLineEuuPdfId = 2, SoLineNo = 123, FileType = "PDF" },
                new SoLineEuuPdf { SoLineEuuPdfId = 1, SoLineNo = 123, FileType = "PDF" }
            };
                    var documentDtos = new List<DocumentDto>
            {
                new DocumentDto { DocumentId = 1 },
                new DocumentDto { DocumentId = 2 }
            };

            Func<IQueryable<SoLineEuuPdf>, IOrderedQueryable<SoLineEuuPdf>>? capturedOrderBy = null;
            Action<IMappingOperationOptions<object, IEnumerable<DocumentDto>>>? capturedMappingOptions = null;

            _soLineEuuPdfRepositoryMock.Setup(r => r.ListAsync(
                It.IsAny<Expression<Func<SoLineEuuPdf, bool>>>(),
                It.IsAny<Func<IQueryable<SoLineEuuPdf>, IOrderedQueryable<SoLineEuuPdf>>>(),
                It.IsAny<Expression<Func<SoLineEuuPdf, object?>>[]>()))
                .Callback<Expression<Func<SoLineEuuPdf, bool>>, Func<IQueryable<SoLineEuuPdf>, IOrderedQueryable<SoLineEuuPdf>>, Expression<Func<SoLineEuuPdf, object?>>[]>(
                    (filter, orderBy, includes) => capturedOrderBy = orderBy)
                .ReturnsAsync(soLineEuuPdfs);

            _mapperMock.Setup(m => m.Map<IEnumerable<DocumentDto>>(soLineEuuPdfs, It.IsAny<Action<IMappingOperationOptions<object, IEnumerable<DocumentDto>>>>()))
                .Callback<object, Action<IMappingOperationOptions<object, IEnumerable<DocumentDto>>>>((source, options) => capturedMappingOptions = options)
                .Returns(documentDtos);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(documentDtos, result.Data);

            // Verify orderBy functionality
            Assert.NotNull(capturedOrderBy);
            var testQueryable = soLineEuuPdfs.AsQueryable();
            var orderedResult = capturedOrderBy(testQueryable).ToList();
            Assert.Equal(1, orderedResult[0].SoLineEuuPdfId); // Should be ordered by SoLineEuuPdfId ascending
            Assert.Equal(2, orderedResult[1].SoLineEuuPdfId);

            // Verify culture mapping options
            Assert.NotNull(capturedMappingOptions);
            var actualItems = new Dictionary<string, object>();
            var mockOptions = new Mock<IMappingOperationOptions<object, IEnumerable<DocumentDto>>>();
            mockOptions.Setup(x => x.Items).Returns(actualItems);

            capturedMappingOptions(mockOptions.Object);

            // Verify that the culture was set in the Items dictionary
            Assert.True(actualItems.ContainsKey("culture"));
            Assert.Equal("fr-FR", actualItems["culture"]);
        }
    }
}