namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine
{
    public class EditSalesOrderLineCommand : IRequest<BaseResponse<bool>>
    {
        public int SalesOrderLineId { get; set; }
        public int SalesOrderId { get; set; }
        public int UpdatedBy { get; set; }
        public int ClientId { get; set; }
        public string? Part { get; set; }
        public int? ManufacturerNo { get; set; }
        public string? DateCode { get; set; }
        public int? PackageNo { get; set; }
        public int? Quantity { get; set; }
        public double? Price { get; set; }
        public DateTime? DatePromised { get; set; }
        public DateTime? RequiredDate { get; set; }
        public string? Instructions { get; set; }
        public int? ProductNo { get; set; }
        public string Taxable { get; set; } = string.Empty;
        public string? CustomerPart { get; set; }
        public bool? ShipASAP { get; set; }
        public bool Inactive { get; set; }
        public int? ROHS { get; set; }
        public string? Notes { get; set; }
        public int? ProductSource { get; set; }
        public DateTime? PODelDate { get; set; }
        public int? SOSerialNo { get; set; }
        public bool? PrintHazardous { get; set; }
        public string? MSLLevel { get; set; }
        public string? ContractNo { get; set; }
        public bool? IsFormChanged { get; set; }
        public bool? IsReasonChanged { get; set; }
        public int? PromiseReasonNo { get; set; }
        public string? ECCNCode { get; set; }
        public int? ECCNNo { get; set; }
        public double? ServiceCostRef { get; set; }
        public string? PreviousEccnCode { get; set; }
        public DateTime? PreviousPromiseDate { get; set; }
        public string? PromiseReasonString { get; set; }
        public int SalesOrderNumber { get; set; }
        public string? Email { get; set; }
        public string LoginEmail { get; set; } = string.Empty;
        public string LoginFullName { get; set; } = string.Empty;

    }

}