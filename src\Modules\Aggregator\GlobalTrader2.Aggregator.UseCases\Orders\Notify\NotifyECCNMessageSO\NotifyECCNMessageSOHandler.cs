﻿using GlobalTrader2.Aggregator.UseCases.Account.LoginPreference.LoginPreference.Queries;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.MailMessages;
using GlobalTrader2.Dto.Templates;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseRequisition.Queries;
using GlobalTrader2.Settings.UseCases.CompanySettings.MailGroupMembers.Queries;
using GlobalTrader2.UserAccount.UseCases.MailMessages.Commands.SendNewMessage;

namespace GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyECCNMessageSO
{
    public class NotifyECCNMessageSOHandler : IRequestHandler<NotifyECCNMessageSOCommand, BaseResponse<bool>>
    {
        private readonly ISender _sender;
        private readonly IRazorViewToStringService _razorViewToStringService;
        private readonly IEmailService _emailService;
        public NotifyECCNMessageSOHandler(ISender sender, IRazorViewToStringService razorViewToStringService, IEmailService emailService)
        {
            _sender = sender;
            _razorViewToStringService = razorViewToStringService;
            _emailService = emailService;
        }

        public async Task<BaseResponse<bool>> Handle(NotifyECCNMessageSOCommand request, CancellationToken cancellationToken)
        {
            var saleOrderLineDetailResult = await _sender.Send(new GetSalesOrderLineQuery()
            {
                SalesOrderLineId = request.SalesOrderLineId
            }, cancellationToken);
            if (!saleOrderLineDetailResult.Success
                || saleOrderLineDetailResult.Data == null
                || string.IsNullOrEmpty(saleOrderLineDetailResult.Data.ECCNCode)
                || !(saleOrderLineDetailResult.Data.ECCNClientNotify ?? false)) return new();

            string content = await _razorViewToStringService.RenderViewToStringAsync("Templates/_NotifySalesOrderECCNEmail",
                new NotifySalesOrderECCNTemplate
                {
                    SalesOrderNo = saleOrderLineDetailResult.Data.SalesOrderNo,
                    SalesOrderNumber = saleOrderLineDetailResult.Data.SalesOrderNumber.GetValueOrDefault(),
                    Part = saleOrderLineDetailResult.Data.Part!,
                    ECCNCode = saleOrderLineDetailResult.Data.ECCNCode,
                    WarningMessage = saleOrderLineDetailResult.Data.WarningMessage!,
                    ECCNMessage = saleOrderLineDetailResult.Data.ECCNMessage!,
                }
            );

            var allUniqueRecipients = await GetUniqueRecipientLoginNos(request.Recipient);
            var sendMessageCommand = new SendNewMessageCommand(saleOrderLineDetailResult.Data.ECCNSubject ?? "", content, allUniqueRecipients, request.LoginId, request.SenderName);

            await _sender.Send(sendMessageCommand, cancellationToken);
            await SendExternalEmailAsync(allUniqueRecipients, content, saleOrderLineDetailResult.Data.ECCNSubject ?? "", request.SenderEmail, cancellationToken);

            return new BaseResponse<bool>
            {
                Data = true,
                Success = true
            };
        }

        private async Task SendExternalEmailAsync(List<RecipientRequest> recipients, string content, string subject, string senderEmail, CancellationToken cancellationToken)
        {
            var usersForSendEmail = await _sender.Send(new GetAllLoginForSendEmailQuery()
            {
                LoginIds = [.. recipients.Select(x => x.Value)],
            }, cancellationToken);
            if (usersForSendEmail.Data == null) return;

            List<string> toEmails = [];
            foreach (var user in usersForSendEmail.Data.Where(x => x.SendEmail && !string.IsNullOrEmpty(x.Email)))
            {
                toEmails.Add(user.Email);
            }
            await _emailService.TrySendEmailAsync(senderEmail, toEmails, [], [], subject, content, [senderEmail], [], cancellationToken);
        }

        private async Task<List<RecipientRequest>> GetUniqueRecipientLoginNos(List<RecipientRequest> recipients)
        {
            var loginNos = new List<int>();
            foreach (var recipient in recipients)
            {
                var recipientLoginNos = recipient.Type == (int)MailMessageAddressType.Group
                    ? await GetMailGroupRecipients(recipient.Value)
                    : new List<int> { recipient.Value };

                loginNos = loginNos.Union(recipientLoginNos).ToList();
            }
            return loginNos.Select(loginNo => new RecipientRequest(loginNo , (int)MailMessageAddressType.Individual)).ToList();
        }

        private async Task<IEnumerable<int>> GetMailGroupRecipients(int mailGroupNo)
        {
            var response = await _sender.Send(new GetMailGroupMembersQuery()
            {
                MailGroupId = mailGroupNo
            });

            return response.Success && response.Data != null
                ? response.Data.Select(x => x.LoginNo)
                : [];
        }
    }
}
