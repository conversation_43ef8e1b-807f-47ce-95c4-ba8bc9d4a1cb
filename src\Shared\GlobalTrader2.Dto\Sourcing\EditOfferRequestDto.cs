﻿namespace GlobalTrader2.Dto.Sourcing;

public class EditOfferRequestDto
{
    public int OfferId { get; set; }
    public string? PartNo { get; set; }
    public int? ManufacturerNo { get; set; }
    public string? DateCode { get; set; }
    public int? ProductNo { get; set; }
    public int? PackageNo { get; set; }
    public int? Quantity { get; set; }
    public double? Price { get; set; }
    public int? CurrencyNo { get; set; }
    public int? Salesman { get; set; }
    public int? OfferStatusNo { get; set; }
    public int? CompanyNo { get; set; }
    public byte? Rohs { get; set; }
    public string? Notes { get; set; }
    public int? SupplierTotalQSA { get; set; }
    public int? SupplierMOQ { get; set; }
    public string? SupplierLTB { get; set; }
    public int? MslLevelNo { get; set; }
    public int? Spq { get; set; }
    public string? LeadTime { get; set; }
    public string? FactorySealed { get; set; }
    public string? RohsStatus { get; set; }
    public bool IsArchived { get; set; }
    public bool? IsHubrfqPage { get; set; }
    public int? CustReqNo { get; set; }
    public string? CloneSource { get; set; }
    public int? PageType { get; set; }
}
