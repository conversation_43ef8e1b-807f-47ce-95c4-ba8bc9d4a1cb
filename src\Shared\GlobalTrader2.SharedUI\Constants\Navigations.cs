﻿using GlobalTrader2.SharedUI.Models;
using Microsoft.Extensions.Configuration;

namespace GlobalTrader2.SharedUI.Constants
{
    public static class Navigations
    {
        public static void Initialize(IConfiguration configuration)
        {
            SalesPerformance = new NavigationItem()
            {
                ID = 1,
                Title = "Customer Reporting Hub",
                CtaUri = configuration["Navigations:Dashboard:SalesPerformance"],
                IconUri = "",
                PageTitle = "Customer Reporting Hub"
            };

            Sales = new NavigationItem()
            {
                ID = 2,
                Title = "Sales Reporting Hub",
                CtaUri = configuration["Navigations:Dashboard:Sales"],
                IconUri = "",
                PageTitle = "Sales Reporting Hub"
            };
        }


        private const string UserIcon = "~/img/icons/user.svg";
        private const string ShippingMethodIcon = "~/img/icons/shipping-box.svg";
        private const string ProductTitle = "Products";
        private const string SetupModulePath = "/Settings";
        private const string SetupPageTitle = "Setup";

        public static readonly NavigationItem Login = new NavigationItem { Title = "Login Page", CtaUri = "/login", IconUri = "" };
        public static readonly NavigationItem Home = new NavigationItem() { Title = "Home", CtaUri = "/", IconUri = "~/img/icons/home.svg", PageTitle = "Home" };

        #region Profile Section
        public static NavigationItem Profile(string name) => new NavigationItem() { Title = name, CtaUri = "/Profile", PageTitle = "Profile", IconUri = "~/img/icons/personal.svg" };
        public static readonly NavigationItem EditMyProfile = new NavigationItem() { Title = "Edit My Profile", CtaUri = "/Profile/EditMyProfile", IconUri = "~/img/icons/edit-personal.svg", PageTitle = "Edit My Profile" };
        public static readonly NavigationItem ToDo = new NavigationItem() { Title = "To Do List", CtaUri = "/Profile/ToDo", IconUri = "~/img/icons/todo-list.svg", PageTitle = "To Do List" };
        public static readonly NavigationItem MailMessages = new NavigationItem() { Title = "Mail Messages", CtaUri = "/Profile/MailMessages", IconUri = "~/img/icons/message.svg", PageTitle = "Mail Messages" };
        #endregion

        #region Contact Section
        public static readonly NavigationItem Contact = new NavigationItem() { Title = "Contact", CtaUri = "/Contact", PageTitle = "Contact", IconUri = "~/img/icons/contact.svg" };
        public static readonly NavigationItem AllCompanies = new NavigationItem() { ID = 1, Title = "All Companies", CtaUri = "/Contact/AllCompanies?clt=0", IconUri = "~/img/icons/company.svg", PageTitle = "All Companies" };
        public static readonly NavigationItem Customers = new NavigationItem() { ID = 2, Title = "Customers", CtaUri = "/Contact/AllCompanies?clt=1", IconUri = "~/img/icons/customers.svg", PageTitle = "Customers" };
        public static readonly NavigationItem Manufacturers = new NavigationItem() { ID = 3, Title = "Manufacturers", CtaUri = "/Contact/Manufacturers", IconUri = "~/img/icons/manufacturer.svg", PageTitle = "Manufacturers" };
        public static readonly NavigationItem Suppliers = new NavigationItem() { ID = 4, Title = "Suppliers", CtaUri = "/Contact/AllCompanies?clt=2", IconUri = "~/img/icons/supplier.svg", PageTitle = "Suppliers" };
        public static readonly NavigationItem Prospects = new NavigationItem() { ID = 5, Title = "Prospects", CtaUri = "/Contact/AllCompanies?clt=3", IconUri = "~/img/icons/prospect.svg", PageTitle = "Prospects" };
        public static readonly NavigationItem Contacts = new NavigationItem() { ID = 6, Title = "Contacts", CtaUri = "/Contact/Contacts", IconUri = "~/img/icons/contact.svg", PageTitle = "Contacts" };
        public static readonly NavigationItem AddInManufacturerGroupCode = new NavigationItem() { ID = 7, Title = "Add in Manufacturer Group Code", CtaUri = "/Contact/Manufacturers/AddInGroupCode", IconUri = "~/img/icons/plus-black.svg", PageTitle = "Add in Manufacturer Group Code" };
        #region All Companies
        public static NavigationItem CompanyDetails(string name) => new NavigationItem() { Title = name, CtaUri = "/Contact/AllCompanies/Details", IconUri = "~/img/icons/company.svg", PageTitle = name };
        public static readonly NavigationItem AddCompany = new NavigationItem() { Title = "Add New Company", CtaUri = "/Contact/AllCompanies/AddCompany", IconUri = "~/img/icons/plus-black.svg", PageTitle = "Add New Company" };
        public static readonly NavigationItem BrowseCompanies = new NavigationItem() { ID = 1, Title = "Browse Companies", CtaUri = "/Contact/AllCompanies?clt=0", IconUri = "~/img/icons/company.svg", PageTitle = "Browse Companies" };
        public static readonly NavigationItem CustomerGroupCode = new NavigationItem() { Title = "Customer Group Code", CtaUri = "/Contact/CustomerGroupCode", IconUri = "~/img/icons/team.svg", PageTitle = "Customer Group Code" };
        #endregion
        #region Contacts
        public static NavigationItem ContactDetails(string name) => new NavigationItem() { Title = name, CtaUri = "/Contact/Contacts/Details", IconUri = "~/img/icons/contact.svg", PageTitle = name };

        #endregion
        #region Manufacturers
        public static NavigationItem ManufacturerDetails(string name) => new NavigationItem() { Title = name, CtaUri = "/Contact/Manufacturers/Details", IconUri = "~/img/icons/manufacturer.svg", PageTitle = name };
        public static readonly NavigationItem AddManufacturer = new NavigationItem() { Title = "Add New Manufacturer", CtaUri = "/Contact/Manufacturers/AddNewManufacturer", IconUri = "~/img/icons/plus-black.svg", PageTitle = "Add New Manufacturer" };
        public static readonly NavigationItem BrowseManufacturers = new NavigationItem() { Title = "Browse Manufacturers", CtaUri = "/Contact/Manufacturers", IconUri = "~/img/icons/manufacturer.svg", PageTitle = "Manufacturers" };
        #endregion
        #endregion

        #region Orders Section
        public static readonly NavigationItem Orders = new NavigationItem() { Title = "Orders", CtaUri = "/Orders", IconUri = "~/img/icons/orders.svg", PageTitle = "Orders" };
        public static readonly NavigationItem Sourcing = new NavigationItem() { ID = 1, Title = "Sourcing", CtaUri = "/Orders/Sourcing", IconUri = "~/img/icons/sourcing.svg", PageTitle = "Sourcing" };
        public static readonly NavigationItem Requirements = new NavigationItem() { ID = 2, Title = "Requirements", CtaUri = "/Orders/CustomerRequirement", IconUri = "~/img/icons/customer-requirement.svg", PageTitle = "Customer Requirements" };
        public static readonly NavigationItem CustomerRequirements = new NavigationItem() { ID = 2, Title = "Customer Requirements", CtaUri = "/Orders/CustomerRequirement", IconUri = "~/img/icons/customer-requirement.svg", PageTitle = "Customer Requirements" };
        public static readonly NavigationItem Quotes = new NavigationItem() { ID = 3, Title = "Quotes", CtaUri = "/Orders/Quotes", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem SalesOrders = new NavigationItem() { ID = 4, Title = "Sales Orders", CtaUri = "/Orders/SalesOrders", IconUri = "", PageTitle = "Sales Orders" };
        public static readonly NavigationItem Invoices = new NavigationItem() { ID = 5, Title = "Invoices", CtaUri = "/Orders/Invoice", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem PurchaseOrders = new NavigationItem() { ID = 6, Title = "Purchase Orders", CtaUri = "/Orders/PurchaseOrder", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem PurchaseRequisitions = new NavigationItem() { ID = 7, Title = "Purchase Requisitions", CtaUri = "/Orders/PurchaseRequisition", IconUri = "", PageTitle = "Purchase Requisitions" };
        public static readonly NavigationItem CustomerRMAs = new NavigationItem() { ID = 8, Title = "Customer RMAs", CtaUri = "/Orders/CustomerRma", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem SupplierRMAs = new NavigationItem() { ID = 9, Title = "Supplier RMAs", CtaUri = "/Orders/SupplierRma", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem CreditNotes = new NavigationItem() { ID = 10, Title = "Credit Notes", CtaUri = "/Orders/CreditNote", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem DebitNotes = new NavigationItem() { ID = 11, Title = "Debit Notes", CtaUri = "/Orders/DebitNote", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem InternalPurchaseOrders = new NavigationItem() { ID = 12, Title = "Internal Purchase Orders", CtaUri = "/Orders/InternalPurchaseOrder", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem BOMBrowse = new NavigationItem() { ID = 13, Title = "HUBRFQ", CtaUri = "/Orders/HUBRFQ", IconUri = "~/img/icons/bom.svg", PageTitle = "HUBRFQ" };
        public static readonly NavigationItem AddNewHUBRFQ = new NavigationItem() { ID = 14, Title = "Add New HUBRFQ", CtaUri = "/Orders/HUBRFQ/AddNewHUBRFQ", IconUri = "~/img/icons/bom.svg", PageTitle = "Add New HUBRFQ" };
        public static NavigationItem CustomerRequirementDetails(string customerRequirementId) => new NavigationItem() { Title = "Requirement " + customerRequirementId, CtaUri = "/Orders/CustomerRequirement/Details", IconUri = "~/img/icons/customer-requirement-detail.svg", PageTitle = "Requirement " + customerRequirementId };
        public static NavigationItem BOMDetail(string bomName) => new() { Title = bomName, CtaUri = "/Orders/HUBRFQ/Details", IconUri = "~/img/icons/bom.svg", PageTitle = bomName };
        public static readonly NavigationItem CustomerRequirementAdd = new NavigationItem() { ID = 14, Title = "Add New Requirement", CtaUri = "/Orders/CustomerRequirement/AddNewRequirement", IconUri = "~/img/icons/customer-requirement.svg", PageTitle = "Add New Requirement" };
        public static readonly NavigationItem CustomerRequirementDetail = new() { CtaUri = "/Orders/CustomerRequirement/Details" };
        public static readonly NavigationItem QuoteDetail = new() { CtaUri = "/Orders/Quotes/Details" };
        public static NavigationItem SalesOrderDetail(string salesOrderNo) => new() { CtaUri = "/Orders/SalesOrders/Details", IconUri = "", PageTitle = $"Sales Order {salesOrderNo}", Title = $"Sales Order {salesOrderNo}" };
        public static readonly NavigationItem InvoiceDetail = new() { CtaUri = "/Orders/Invoice/Details" };
        public static readonly NavigationItem PurchaseOrderDetail = new() { CtaUri = "/Orders/PurchaseOrder/Details" };
        public static NavigationItem PurchaseRequisitionDetail(string no) => new() { CtaUri = "/Orders/PurchaseRequisition/Details", Title = $"Purchase Requisition {no}", PageTitle = $"Purchase Requisition {no}" };
        public static readonly NavigationItem PurchaseRequisitionDetails = new() { CtaUri = "/Orders/PurchaseRequisition/Details" };
        public static readonly NavigationItem CustomerRmaDetail = new() { CtaUri = "/Orders/CustomerRma/Details" };
        public static readonly NavigationItem SupplierRmaDetail = new() { CtaUri = "/Orders/SupplierRma/Details" };
        public static readonly NavigationItem CreditNoteDetail = new() { CtaUri = "/Orders/CreditNote/Details" };
        public static readonly NavigationItem DebitNoteDetail = new() { CtaUri = "/Orders/DebitNote/Details" };
        public static readonly NavigationItem InternalPurchaseOrderDetail = new() { CtaUri = "/Orders/InternalPurchaseOrder/Details" };
        public static readonly NavigationItem BOMBrowseDetail = new() { CtaUri = "/Orders/HUBRFQ/Details" };

        //PurchaseQuote
        public static readonly NavigationItem PriceRequest = new NavigationItem() { ID = 14, Title = "Price Request", CtaUri = "/Orders/PoQuote", IconUri = "", PageTitle = "Price Request" };
        public static NavigationItem PriceRequestDetails(string id) => new NavigationItem() { Title = "Price Request " + id, CtaUri = "/Orders/PoQuote/Details", IconUri = "", PageTitle = "Price Request " + id };

        public static readonly NavigationItem ClientInvoice = new NavigationItem() { ID = 15, Title = "Client Invoice", CtaUri = "/Orders/ClientInvoice", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem DivisionKPI = new NavigationItem() { ID = 16, Title = "Division KPI", CtaUri = "/Orders/Division", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem TeamKPI = new NavigationItem() { ID = 17, Title = "Team KPI", CtaUri = "/Orders/Team", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem SalesKPI = new NavigationItem() { ID = 18, Title = "Sales KPI", CtaUri = "/Orders/SaleDetail", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem BOMManager = new NavigationItem() { ID = 19, Title = "BOM Manager", CtaUri = "/Orders/BomSearch", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem ProspectiveCrossSelling = new NavigationItem() { ID = 20, Title = "Prospective Cross Selling", CtaUri = "/Orders/ProspectiveCs", IconUri = "", PageTitle = "" };

        public static readonly NavigationItem PurchaseOrdersAdd = new NavigationItem() { Title = "Add New Purchase Order", CtaUri = "/Orders/PurchaseOrder/Add", IconUri = "", PageTitle = "Add New Purchase Order" };
        public static readonly NavigationItem QuoteOrderAdd = new NavigationItem() { Title = "Add New Quote", CtaUri = "/Orders/Quotes/Add", IconUri = "", PageTitle = "Add New Quote" };
        public static readonly NavigationItem InvoicesAdd = new NavigationItem() { Title = "Add New Invoice", CtaUri = "/Orders/Invoice/Add", IconUri = "", PageTitle = "Add New Invoice" };
        public static readonly NavigationItem CustomerRMAAdd = new NavigationItem() { Title = "Add New Customer RMA", CtaUri = "/Orders/CustomerRma/Add", IconUri = "", PageTitle = "Add New Customer RMA" };
        public static readonly NavigationItem SupplierRMAAdd = new NavigationItem() { Title = "Add New Supplier RMA", CtaUri = "/Orders/SupplierRma/Add", IconUri = "", PageTitle = "Add New Supplier RMA" };
        public static readonly NavigationItem CreditNoteAdd = new NavigationItem() { Title = "Add New Credit Note", CtaUri = "/Orders/CreditNote/Add", IconUri = "", PageTitle = "Add New Credit Note" };
        public static readonly NavigationItem DebitNoteAdd = new NavigationItem() { Title = "Add New Debit Note", CtaUri = "/Orders/DebitNote/Add", IconUri = "", PageTitle = "Add New Debit Note" };
        public static readonly NavigationItem SalesOrderAdd = new NavigationItem() { Title = "Add New Sales Order", CtaUri = "/Orders/SalesOrders/Add", IconUri = "~/img/icons/add-orders.svg", PageTitle = "Add New Sales Order" };

        #endregion

        #region Warehouse Section
        public static readonly NavigationItem Warehouse = new NavigationItem() { Title = "Warehouse", CtaUri = "/Warehouse", IconUri = "", PageTitle = "Warehouse" };
        public static readonly NavigationItem ReceivePOs = new NavigationItem() { ID = 1, Title = "Receive POs", CtaUri = "/Warehouse/ReceivePo", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem ShipSOs = new NavigationItem() { ID = 2, Title = "Ship SOs", CtaUri = "/Warehouse/ShipSo", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem ReceiveCRMAs = new NavigationItem() { ID = 3, Title = "Receive CRMAs", CtaUri = "/Warehouse/ReceiveCrma", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem ShipSRMAs = new NavigationItem() { ID = 4, Title = "Ship SRMAs", CtaUri = "/Warehouse/ShipSrma", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem Stock = new NavigationItem() { ID = 5, Title = "Stock", CtaUri = "/Warehouse/Stock", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem Services = new NavigationItem() { ID = 6, Title = "Services", CtaUri = "/Warehouse/Services", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem ServicesDetails = new NavigationItem() { ID = 6, Title = "Service Details", CtaUri = "/Warehouse/Services/Details", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem Lots = new NavigationItem() { ID = 7, Title = "Lots", CtaUri = "/Warehouse/Lots", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem LotsDetails = new NavigationItem() { Title = "Lots Details", CtaUri = "/Warehouse/Lots/Details", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem GoodsIn = new NavigationItem() { ID = 8, Title = "Goods In", CtaUri = "/Warehouse/GoodsIn", IconUri = "", PageTitle = "Goods In" };
        public static readonly NavigationItem SupplierInvoice = new NavigationItem() { ID = 9, Title = "Supplier Invoice", CtaUri = "/Warehouse/SupplierInvoice", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem NPR = new NavigationItem() { ID = 10, Title = "NPR", CtaUri = "/Warehouse/Npr", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem HubInvoice = new NavigationItem() { ID = 11, Title = "Hub Invoice", CtaUri = "/Orders/ClientInvoice", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem HubCreditNotes = new NavigationItem() { ID = 12, Title = "Hub Credit Notes", CtaUri = "/Orders/CreditNote", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem IHSCatalogue = new NavigationItem() { ID = 13, Title = "IHS Catalogue", CtaUri = "/Warehouse/IhsCatalogue", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem ShortShipment = new NavigationItem() { ID = 14, Title = "Short Shipment", CtaUri = "/Warehouse/ShortShipment", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem OGELLines = new NavigationItem() { ID = 15, Title = "OGEL Lines", CtaUri = "/Warehouse/OgelLine", IconUri = "", PageTitle = "" };
        public static NavigationItem StockDetail(string name) => new NavigationItem() { Title = name, CtaUri = "/Warehouse/StockDetail", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem SupplierInvoiceAdd = new NavigationItem() { Title = "Add New Supplier Invoice", CtaUri = "/Warehouse/SupplierInvoice/Add", IconUri = "", PageTitle = "Add New Supplier Invoice" };

        #endregion

        #region Reports Section
        public static readonly NavigationItem Reports = new NavigationItem() { Title = "Reports", CtaUri = "/Reports", IconUri = "", PageTitle = "" };
        #endregion

        #region Setup Section
        public static readonly NavigationItem Setup = new NavigationItem() { Title = SetupPageTitle, CtaUri = SetupModulePath, IconUri = "~/img/icons/setup.svg", PageTitle = SetupPageTitle };

        #region Company Settings
        public static readonly NavigationItem CompanySettings = new NavigationItem() { Title = "Company Settings", CtaUri = SetupModulePath, IconUri = "~/img/icons/company-settings.svg", PageTitle = SetupPageTitle };
        public static readonly NavigationItem ApplicationSettings = new NavigationItem() { Title = "Application Settings", CtaUri = $"{SetupModulePath}/CompanySettings/ApplicationSettings", IconUri = "~/img/icons/application-settings.svg", PageTitle = "Application Settings" };
        public static readonly NavigationItem Teams = new NavigationItem() { Title = "Teams", CtaUri = $"{SetupModulePath}/CompanySettings/Teams", IconUri = "~/img/icons/team.svg", PageTitle = "Teams" };
        public static readonly NavigationItem Terms = new NavigationItem() { Title = "Terms", CtaUri = $"{SetupModulePath}/CompanySettings/Terms", IconUri = "~/img/icons/term.svg", PageTitle = "Terms" };
        public static readonly NavigationItem LocalCurrency = new NavigationItem() { Title = "Local Currency", CtaUri = $"{SetupModulePath}/CompanySettings/LocalCurrency", IconUri = "~/img/icons/currencies.svg", PageTitle = "Local Currency" };
        public static readonly NavigationItem Warehouses = new NavigationItem() { Title = "Warehouses", CtaUri = $"{SetupModulePath}/CompanySettings/Warehouses", IconUri = "~/img/icons/warehouse.svg", PageTitle = "Warehouses" };
        public static readonly NavigationItem Printer = new NavigationItem() { Title = "Printer", CtaUri = $"{SetupModulePath}/CompanySettings/Printer", IconUri = "~/img/icons/printer.svg", PageTitle = "Printer" };
        public static readonly NavigationItem SequenceNumbers = new NavigationItem() { Title = "Sequence Numbers", CtaUri = $"{SetupModulePath}/CompanySettings/SequenceNumbers", IconUri = "~/img/icons/sequence-number.svg", PageTitle = "Sequence Numbers" };
        public static readonly NavigationItem BulkInvoiceEmailComposer = new NavigationItem() { Title = "Bulk Invoice Email Composer", CtaUri = $"{SetupModulePath}/CompanySettings/BulkInvoiceEmailComposer", IconUri = "~/img/icons/email-composer.svg", PageTitle = "Bulk Invoice Email Composer" };
        public static readonly NavigationItem MailGroup = new NavigationItem() { Title = "Mail Groups", CtaUri = $"{SetupModulePath}/CompanySettings/MailGroups", IconUri = "~/img/icons/mail-group.svg", PageTitle = "Mail Groups" };
        public static readonly NavigationItem Warnings = new NavigationItem() { Title = "Warnings", CtaUri = $"{SetupModulePath}/CompanySettings/Warnings", IconUri = "~/img/icons/warning.svg", PageTitle = "Warnings" };
        public static readonly NavigationItem OGELLicenses = new NavigationItem() { Title = "OGEL Licenses", CtaUri = $"{SetupModulePath}/CompanySettings/OGELLicenses", IconUri = "~/img/icons/license.svg", PageTitle = "OGEL Licenses" };
        public static readonly NavigationItem RestrictedManufacturer = new NavigationItem() { Title = "Restricted Manufacturer", CtaUri = $"{SetupModulePath}/CompanySettings/RestrictedManufacturer", IconUri = "~/img/icons/restricted-manufacturer.svg", PageTitle = "Restricted Manufacturer" };
        public static readonly NavigationItem ClientInvoiceHeader = new NavigationItem() { Title = "Client Invoice Header", CtaUri = $"{SetupModulePath}/CompanySettings/ClientInvoiceHeader", IconUri = "~/img/icons/invoice.svg", PageTitle = "Client Invoice Header" };
        public static readonly NavigationItem Countries = new NavigationItem() { Title = "Countries", CtaUri = $"{SetupModulePath}/CompanySettings/Countries", IconUri = "~/img/icons/country.svg", PageTitle = "Countries" };
        public static readonly NavigationItem Divisions = new NavigationItem() { Title = "Divisions", CtaUri = $"{SetupModulePath}/CompanySettings/Divisions", IconUri = "~/img/icons/division.svg", PageTitle = "Divisions" };
        public static readonly NavigationItem StockLogReasons = new NavigationItem() { Title = "Stock Log Reasons", CtaUri = $"{SetupModulePath}/CompanySettings/StockLogReason", IconUri = "~/img/icons/stock-log-reason.svg", PageTitle = "Stock Log Reasons" };
        public static readonly NavigationItem Products = new NavigationItem() { Title = ProductTitle, CtaUri = $"{SetupModulePath}/CompanySettings/Products", IconUri = "~/img/icons/package.svg", PageTitle = ProductTitle };
        public static readonly NavigationItem SourcingLinks = new NavigationItem() { Title = "Sourcing Links", CtaUri = $"{SetupModulePath}/CompanySettings/SourcingLinks", IconUri = "~/img/icons/sourcing-link.svg", PageTitle = "Sourcing Links" };
        public static readonly NavigationItem ShippingMethods = new NavigationItem() { Title = "Shipping Methods", CtaUri = $"{SetupModulePath}/CompanySettings/ShippingMethods", IconUri = "~/img/icons/shipping.svg", PageTitle = "Shipping Methods" };
        public static readonly NavigationItem Taxes = new NavigationItem() { Title = "Taxes", CtaUri = $"{SetupModulePath}/CompanySettings/Taxes", IconUri = "~/img/icons/tax.svg", PageTitle = "Taxes" };
        public static readonly NavigationItem Currencies = new NavigationItem() { Title = "Currencies", CtaUri = $"{SetupModulePath}/CompanySettings/Currencies", IconUri = "~/img/icons/currencies.svg", PageTitle = "Currencies" };
        public static readonly NavigationItem BulkCreditEmailComposer = new NavigationItem() { Title = "Bulk Credit Email Composer", CtaUri = $"{SetupModulePath}/CompanySettings/BulkCreditEmailComposer", IconUri = "~/img/icons/email-composer.svg", PageTitle = "Bulk Credit Email Composer" };
        public static readonly NavigationItem PrintedDocuments = new NavigationItem() { Title = "Printed Documents", CtaUri = $"{SetupModulePath}/CompanySettings/PrintedDocuments", IconUri = "~/img/icons/printer.svg", PageTitle = "Printed Documents" };
        #endregion

        #region Global Settings
        public static readonly NavigationItem AS6081 = new() { Title = "AS6081", CtaUri = $"{SetupModulePath}/GlobalSettings/AS6081", IconUri = "~/img/icons/as6081.svg", PageTitle = "AS6081" };
        public static readonly NavigationItem GlobalSecuritySettings = new NavigationItem() { Title = "Global Security Settings", CtaUri = SetupModulePath, IconUri = "~/img/icons/shield.svg", PageTitle = SetupPageTitle };
        public static readonly NavigationItem GlobalSettings = new NavigationItem() { Title = "Global Settings", CtaUri = SetupModulePath, IconUri = "~/img/icons/globe.svg", PageTitle = SetupPageTitle };
        public static readonly NavigationItem CompanyTypes = new NavigationItem() { Title = "Company Types", CtaUri = $"{SetupModulePath}/GlobalSettings/CompanyType", IconUri = "~/img/icons/company-type.svg", PageTitle = "Company Types" };
        public static readonly NavigationItem CommunicationLogTypes = new NavigationItem() { Title = "Communication Log Types", CtaUri = $"{SetupModulePath}/GlobalSettings/CommunicationLogType", IconUri = "~/img/icons/communication-log-type.svg", PageTitle = "Communication Log Types" };
        public static readonly NavigationItem MasterCountryList = new NavigationItem() { Title = "Master Country List", CtaUri = $"{SetupModulePath}/GlobalSettings/MasterCountry", IconUri = "~/img/icons/master-country-list.svg", PageTitle = "Master Country List" };
        public static readonly NavigationItem MasterAppSetting = new() { Title = "Application Settings", CtaUri = $"{SetupModulePath}/GlobalSettings/MasterAppSettings", IconUri = "~/img/icons/application-settings.svg", PageTitle = "Master Application Settings" };
        public static readonly NavigationItem MasterStatus = new() { Title = "Master Status", CtaUri = $"{SetupModulePath}/GlobalSettings/MasterStatus", IconUri = "~/img/icons/master-status.svg", PageTitle = "Master Status" };
        public static readonly NavigationItem MasterTaxes = new NavigationItem() { Title = "Master Taxes", CtaUri = $"{SetupModulePath}/GlobalSettings/MasterTax", IconUri = "~/img/icons/tax.svg", PageTitle = "Master Taxes" };
        public static readonly NavigationItem MasterCurrencyList = new NavigationItem() { Title = "Master Currency List", CtaUri = $"{SetupModulePath}/GlobalSettings/MasterCurrency", IconUri = "~/img/icons/currencies.svg", PageTitle = "Master Currency List" };
        public static readonly NavigationItem MasterLogins = new NavigationItem() { Title = "Master Login", CtaUri = $"{SetupModulePath}/GlobalSettings/MasterLogin", IconUri = "~/img/icons/master-login.svg", PageTitle = "Master Login" };
        public static readonly NavigationItem CountingMethods = new() { Title = "Counting Methods", CtaUri = $"{SetupModulePath}/GlobalSettings/CountingMethod", IconUri = "~/img/icons/counting-method.svg", PageTitle = "Counting Methods" };
        public static readonly NavigationItem IndustryTypes = new NavigationItem() { Title = "Industry Types", CtaUri = $"{SetupModulePath}/GlobalSettings/IndustryType", IconUri = "~/img/icons/industry.svg", PageTitle = "Industry Types" };
        public static readonly NavigationItem Incoterms = new NavigationItem() { Title = "Incoterms", CtaUri = $"{SetupModulePath}/GlobalSettings/Incoterm", IconUri = "~/img/icons/incoterms.svg", PageTitle = "Incoterms" };
        public static readonly NavigationItem Packages = new NavigationItem() { Title = "Packages", CtaUri = $"{SetupModulePath}/GlobalSettings/Package", IconUri = "~/img/icons/package.svg", PageTitle = "Packages" };
        public static readonly NavigationItem DocumentSizes = new() { Title = "DocumentSizes", CtaUri = $"{SetupModulePath}/GlobalSettings/DocumentSize", IconUri = "~/img/icons/document-size.svg", PageTitle = "Document File Size" };
        public static readonly NavigationItem CloseReasons = new NavigationItem() { Title = "Close Reasons", CtaUri = $"{SetupModulePath}/GlobalSettings/CloseReason", IconUri = "~/img/icons/close-reason.svg", PageTitle = "Close Reasons" };
        public static readonly NavigationItem Certificates = new NavigationItem() { Title = "Certificate", CtaUri = $"{SetupModulePath}/GlobalSettings/Certificate", IconUri = "~/img/icons/certificate.svg", PageTitle = "Certificate" };
        public static readonly NavigationItem ToDoListTypes = new NavigationItem() { Title = "To Do List Type", CtaUri = $"{SetupModulePath}/GlobalSettings/ToDoListType", IconUri = "~/img/icons/todo-list.svg", PageTitle = "To Do List Type" };
        public static readonly NavigationItem RootCauseCode = new NavigationItem() { Title = "Root Cause Code", CtaUri = $"{SetupModulePath}/GlobalSettings/RootCauseCode", IconUri = "~/img/icons/root-cause.svg", PageTitle = "Root Cause Code" };
        public static readonly NavigationItem GlobalProduct = new NavigationItem() { Title = "Global Products", CtaUri = $"{SetupModulePath}/GlobalSettings/GlobalProduct", IconUri = "~/img/icons/package.svg", PageTitle = "Global Products" };
        public static readonly NavigationItem ECCN = new NavigationItem() { Title = "ECCN", CtaUri = $"{SetupModulePath}/GlobalSettings/ECCN", IconUri = "~/img/icons/tax.svg", PageTitle = "ECCN" };
        public static readonly NavigationItem Client = new NavigationItem() { Title = "Client", CtaUri = $"{SetupModulePath}/GlobalSettings/Client", IconUri = "~/img/icons/client.svg", PageTitle = "Client" };
        public static readonly NavigationItem StarRatings = new NavigationItem() { Title = "Star Rating", CtaUri = $"{SetupModulePath}/GlobalSettings/StarRating", IconUri = "~/img/icons/star-rating.svg", PageTitle = "Star Rating" };
        public static readonly NavigationItem EntertainmentTypes = new NavigationItem() { Title = "Entertainment Type", CtaUri = $"{SetupModulePath}/GlobalSettings/EntertainmentType", IconUri = "~/img/icons/entertainment-types.svg", PageTitle = "Entertainment Type" };
        public static readonly NavigationItem PpvBomQualification = new NavigationItem() { Title = "PPV/ BOM Qualification", CtaUri = $"{SetupModulePath}/GlobalSettings/PpvBomQualification", IconUri = "~/img/icons/bom.svg", PageTitle = "PPV/ BOM Qualification" };
        #endregion

        #region Security Settings
        public static readonly NavigationItem GlobalSecurityGroups = new NavigationItem() { Title = "Global Security Groups", CtaUri = $"{SetupModulePath}/SecuritySettings/GlobalSecurityGroups", IconUri = UserIcon, PageTitle = "Global Security Groups" };
        public static readonly NavigationItem SecuritySettings = new NavigationItem() { Title = "Security Settings", CtaUri = SetupModulePath, IconUri = "~/img/icons/shield.svg", PageTitle = SetupPageTitle };
        public static readonly NavigationItem SecurityUsers = new NavigationItem() { Title = "Security Users", CtaUri = $"{SetupModulePath}/SecuritySettings/SecurityUsers", IconUri = UserIcon, PageTitle = "Security Users" };
        public static readonly NavigationItem SecurityGroups = new NavigationItem() { Title = "Security Groups", CtaUri = $"{SetupModulePath}/SecuritySettings/SecurityGroups", IconUri = UserIcon, PageTitle = "Security Groups" };
        #endregion

        #endregion

        #region Dashboard Section
        public static readonly NavigationItem Dashboards = new NavigationItem() { Title = "Dashboards", CtaUri = "", IconUri = "", PageTitle = "" };
        // Dashboards_Stock
        public static NavigationItem SalesPerformance { get; private set; }
        // Dashboards_SO
        public static NavigationItem Sales { get; private set; }
        #endregion

        #region Utility Section
        public static readonly NavigationItem Utility = new NavigationItem() { Title = "Utility", CtaUri = "", IconUri = "", PageTitle = "Utility" };
        public static readonly NavigationItem BOMImport = new NavigationItem() { ID = 1, Title = "BOM Import", CtaUri = "/Utility/BOMImport", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem OfferImport = new NavigationItem() { ID = 1, Title = "Offer Import", CtaUri = "/Utility/OfferImport", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem HUBOfferImport = new NavigationItem() { ID = 1, Title = "Strategic Offers Import Tool", CtaUri = "Utility/HUBOfferImport", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem XMatchUtility = new NavigationItem() { ID = 1, Title = "XMatch Utility", CtaUri = "/Utility/Xmatch", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem StockImport = new NavigationItem() { ID = 1, Title = "Stock Import", CtaUri = "/Utility/StockImport", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem UtilityLog = new NavigationItem() { ID = 1, Title = "Utility Log", CtaUri = "/Utility/LogImport", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem ReverseLogisticsImportTool = new NavigationItem() { ID = 1, Title = "Reverse Logistics Import Tool", CtaUri = "/Utility/ReverseLogisticsImport", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem BOMManagerImport = new NavigationItem() { ID = 1, Title = "BOM Manager Import", CtaUri = "/Utility/BOMManagerImport", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem PriceQuoteImport = new NavigationItem() { ID = 1, Title = "Price Quote Import", CtaUri = "/Utility/PriceQuoteImport", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem AlternativeImport = new NavigationItem() { ID = 1, Title = "Alternative Import", CtaUri = "/Utility/AlternativeImport", IconUri = "", PageTitle = "" };
        // Utility_HUBOfferImportLarge
        public static readonly NavigationItem BulkOfferScheduledImport = new NavigationItem() { ID = 1, Title = "Bulk Offer Scheduled Import", CtaUri = "/Utility/HUBOfferImportLarge", IconUri = "", PageTitle = "" };
        public static readonly NavigationItem UtilityEmailCampaign = new NavigationItem() { ID = 1, Title = "Email Campaign", CtaUri = "/Utility/EmailCampaign", IconUri = "", PageTitle = "" };
        #endregion
    }
}
