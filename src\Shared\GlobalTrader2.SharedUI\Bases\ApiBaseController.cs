﻿using GlobalTrader2.Core.Bases;
using GlobalTrader2.SharedUI.Constants;
using GlobalTrader2.SharedUI.Helper;
using GlobalTrader2.SharedUI.Services;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries.GetNameByLoginId;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Globalization;

namespace GlobalTrader2.SharedUI.Bases
{
    [ApiController]
    public class ApiBaseController : ControllerBase
    {
        protected int UserId
        {
            get
            {
                return HttpContext.Session.GetInt32(SessionKey.LoginID) ?? throw new InvalidOperationException("User ID is required but null.");
            }
        }
        protected int ClientId
        {
            get
            {
                return HttpContext.Session.GetInt32(SessionKey.ClientID) ?? throw new InvalidOperationException("Client ID is required but null.");
            }
        }

        protected string Culture
        {
            get
            {
                return HttpContext.Session.GetString(SessionKey.Culture) ?? Core.Constants.Culture.Default;
            }
        }

        protected string LoginFullName
        {
            get
            {
                return HttpContext.Session.GetString(SessionKey.LoginFullName) ?? throw new InvalidOperationException("LoginFullName is required but null.");
            }
        }

        protected string ClientCurrencyCode
        {
            get
            {
                return HttpContext.Session.GetString(SessionKey.ClientCurrencyCode) ?? throw new InvalidOperationException("ClientCurrencyCode is required but null.");
            }
        }
        protected int ClientCurrencyId
        {
            get
            {
                return HttpContext.Session.GetInt32(SessionKey.ClientCurrencyID) ?? throw new InvalidOperationException("ClientCurrencyID is required but null.");
            }
        }
        protected int MasterLoginId
        {
            get
            {
                return HttpContext.Session.GetInt32(SessionKey.MasterLoginNo) ?? throw new InvalidOperationException("MasterLoginId is required but null.");
            }
        }
        protected string LoginEmail
        {
            get
            {
                return HttpContext.Session.GetString(SessionKey.LoginEmail) ?? throw new InvalidOperationException("LoginEmail is required but null.");
            }
        }

        protected bool IsGlobalLogin
        {
            get
            {
                return HttpContext.Session.Get<bool>(SessionKey.IsGlobalUser);
            }
        }

        protected bool IsPOHub
        {
            get
            {
                return HttpContext.Session.Get<bool>(SessionKey.IsPOHub);
            }
        }

        protected int LoginTeamID
        {
            get
            {
                return HttpContext.Session.GetInt32(SessionKey.LoginTeamID) ?? throw new InvalidOperationException("LoginTeamID is required but null.");
            }
        }
        protected int LoginDivisionID
        {
            get
            {
                return HttpContext.Session.GetInt32(SessionKey.LoginDivisionID) ?? throw new InvalidOperationException("LoginDivisionID is required but null.");
            }
        }
        protected static async Task<string> FormatDLUP(IMediator sender, IStringLocalizer<Misc> stringLocalizer, int? updatedBy, DateTime? dlup)
        {
            BaseResponse<string> updatedByResult = new BaseResponse<string>();
            var dateInfo = string.Empty;

            if (updatedBy.HasValue)
            {
                updatedByResult = await sender.Send(new GetNameByLoginIdQuery(updatedBy.Value));
            }

            if (dlup.HasValue)
            {
                dateInfo = LocalizerHelper.FormatDLUP(dlup.Value, updatedByResult.Data, stringLocalizer, CultureInfo.CurrentCulture);
            }
            
            return $"{stringLocalizer.GetString("DLUP")}: {dateInfo}";
        }
    }
}
