@page
@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.IndexModel
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.CustomerRequirement.Components.AddNewRequirement
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LeftNugget
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LiteDatatable
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SelectionMenu
@using GlobalTrader2.SharedUI.Areas.Containers.Pages.Shared.SourcingSectionBox
@using GlobalTrader2.SharedUI.Constants
@using GlobalTrader2.SharedUI.Services
@using GlobalTrader2.SharedUI.Helper
@using GlobalTrader2.SharedUI.Models
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@using Microsoft.AspNetCore.Http
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject SessionManager _sessionManager
@inject SettingManager _settingManager

@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
}

@section LeftSidebar {
    @await Component.InvokeAsync(
                nameof(LeftNugget),
                new LeftNuggetProps()
        {
            IsInitiallyExpanded = true,
            Item = SideBar.Selection,
            ChildComponent = nameof(SelectionMenu),
        })
}

@{
    ViewData["Title"] = "HUBRFQ";
    var isGSA = HttpContext.Session.Get<bool>(SessionKey.IsGSA);
    var isGlobalUser = HttpContext.Session.Get<bool>(SessionKey.IsGlobalUser);
    var isPoHub = HttpContext.Session.Get<bool>(SessionKey.IsPOHub);
}

<style>
    .filter-form {
        justify-content: end;
    }
</style>



<div id="hubrfq-container" class="page-content-container">
    <div class="d-flex justify-content-between align-items-end border-bottom mt-2">
        <h2 class="page-primary-title">@_localizer["HUBRFQ"]</h2>
        <div id="right-controls" class="d-flex flex-column gap-2">
            <span class="d-flex gap-2 justify-content-end">
                <a class="btn btn-primary" id="add-new-hub" href="/Orders/HUBRFQ/AddNewHUBRFQ">
                    <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                    <span class="lh-base">@_localizer["AddNewHUBRFQ"]</span>
                </a>
            </span>
            <div id="hurfq-nav-tabs-wrapper" role="tablist">
                <ul class="nav nav-tabs border-0 justify-content-end" id="hurfq-tab">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link"
                                id="my-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#datatable" type="button"
                                role="tab" aria-controls="datatable"
                                aria-selected="true" tabId="0">
                            @_localizer["My"]
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link"
                                id="team-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#datatable"
                                type="button"
                                role="tab"
                                aria-controls="datatable"
                                aria-selected="false" tabId="1">
                            @_localizer["Team"]
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link"
                                id="division-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#datatable"
                                type="button" role="tab"
                                aria-controls="datatable"
                                aria-selected="false" tabId="2">
                            @_localizer["Division"]
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link"
                                id="company-tab"
                                data-bs-toggle="tab"
                                data-bs-target="#datatable"
                                type="button" role="tab"
                                aria-controls="datatable"
                                aria-selected="false" tabId="3">
                            @_localizer["Company"]
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="tab-content mt-3" id="supplierTabContent">
        <div class="tab-pane fade" id="datatable" role="tabpanel" aria-labelledby="my-tab">
            @{
                var sectionModel = new FilterTableSectionBoxModel("hubrfq", "HUBRFQ",
                typeof(LiteDatatable), new LiteDatatableModel("hurfqTbl"),
                null,
                null, null)
            {
                ContentClasses = contentClasses,
                HeaderClasses = headerClasses,
                SectionBoxClasses = sectionBoxClasses
            };
            }

            @await Html.PartialAsync("Partials/_FilterTableSectionBox", sectionModel)

        </div>

    </div>
</div>

@section Scripts {
        <script>
            var currentTab = "@Model.CurrentTab";
        var commonLocalize = {
            Apply: "@_commonLocalizer["Apply"]",
            Off: "@_commonLocalizer["Off"]",
            Hide: "@_commonLocalizer["Hide"]",
            Reset: "@_commonLocalizer["Reset"]",
            Cancel: "@_commonLocalizer["Cancel"]",
            Show: "@_commonLocalizer["Show"]",
        }

        var hubrfqLocalizedTitles = {
            Alternative: "@_localizer["Alternative"]",
            PossibleAlternative: "@_localizer["Possible Alternative"]",
            FirmAlternative: "@_localizer["Firm Alternative"]",
            HUBRFQName: "@_localizer["HUBRFQName"]",
            ReqNo: "@_localizer["ReqNo"]",
            AssignedUser: "@_localizer["AssignedUser"]",
            Quantity: "@_localizer["Quantity"]",
            PartNo: "@_localizer["PartNo"]",
            Manufacturer: "@_localizer["Manufacturer"]",
            HUBRFQCode: "@_localizer["HUBRFQCode"]",
            Salesperson: "@_localizer["Salesperson"]",
            SupportTeamMember: "@_localizer["SupportTeamMember"]",
            Company: "@_localizer["Company"]",
            Contact: "@_localizer["Contact"]",
            Status: "@_localizer["Status"]",
            Division: "@_localizer["Division"]",
            ReceivedDate: "@_localizer["Received Date"]",
            Promised: "@_localizer["Promised"]",
            HUBRFQRequiredDate: "@_localizer["HUBRFQRequiredDate"]",
            TotalValue: "@_localizer["TotalValue"]",
            No: "@_localizer["No"]",
            Filter: "@_localizer["filter"]",
            Filters: "@_localizer["filters"]",
            Applied: "@_localizer["applied"]"
        }
        var priceRequestPageState = {
            IsPoHub: @Json.Serialize(isPoHub),
            IsGlobalUser: @Json.Serialize(isGlobalUser),
            IsGSA: @Json.Serialize(isGSA)
        }
        </script>

    @await Html.PartialAsync("_ValidationScriptsPartial")
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/loading-spinner.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/dialog-custom-events.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datetime-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/dropdown-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/form-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    @* <script src="@_settingManager.GetCdnUrl("/js/directives/input-directive.js" asp-append-version="true"></script> *@
    @* <script src="@_settingManager.GetCdnUrl("/js/directives/custom-input-directive.js" asp-append-version="true"></script> *@
    <script src="@_settingManager.GetCdnUrl("/js/widgets/resize-data-table-extensions.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    
    <environment include="Development">
        <script type="module" src="/js/modules/orders/hubrfq/hubrfq.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script type="module" src="/dist/js/orders-hubrfq.bundle.js" asp-append-version="true"></script>
    </environment>

    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>
}