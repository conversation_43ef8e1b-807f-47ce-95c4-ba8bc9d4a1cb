using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine;
using System.Net.Mail;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.SOLine.Commands
{
    public class EditSalesOrderLineHandlerTest
    {
        private readonly Mock<IBaseRepository<AffectedRows>> _repositoryMock;
        private readonly Mock<IEmailService> _emailServiceMock;
        private readonly Mock<IEmailTemplate> _emailTemplateMock;
        private readonly EditSalesOrderLineHandler _handler;

        public EditSalesOrderLineHandlerTest()
        {
            _repositoryMock = new Mock<IBaseRepository<AffectedRows>>();
            _emailServiceMock = new Mock<IEmailService>();
            _emailTemplateMock = new Mock<IEmailTemplate>();
            _handler = new EditSalesOrderLineHandler(
                _repositoryMock.Object,
                _emailServiceMock.Object,
                _emailTemplateMock.Object);
        }

        [Fact]
        public async Task EditSalesOrderLineHandler_WhenValidRequest_UpdatesDataAndReturnsSuccess()
        {
            // Arrange
            var command = new EditSalesOrderLineCommand
            {
                SalesOrderLineId = 123,
                UpdatedBy = 456,
                ClientId = 789,
                ProductNo = 101,
                Quantity = 100,
                Price = 50.00d,
            };

            var affectedRows = new AffectedRows { RowsAffected = 1 };

            _repositoryMock
                .Setup(r => r.SqlQueryRawReturnValueAsync(
                    It.IsAny<string>(),
                    It.IsAny<object[]>()))
                .ReturnsAsync(affectedRows);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.True(result.Data);

            // Verify repository was called with correct parameters
            _repositoryMock.Verify(
                r => r.SqlQueryRawReturnValueAsync(
                    It.Is<string>(sql => sql.Contains("usp_update_SalesOrderLine")),
                    It.Is<object[]>(parameters => parameters.Length == 31)),
                Times.Once);
        }

        [Fact]
        public async Task EditSalesOrderLineHandler_WhenPromiseDateChanges_SendsEmailNotification()
        {
            // Arrange
            var command = new EditSalesOrderLineCommand
            {
                SalesOrderLineId = 123,
                UpdatedBy = 456,
                ClientId = 789,
                DatePromised = new DateTime(2024, 6, 15),
                PreviousPromiseDate = new DateTime(2024, 6, 10), // Different dates
                LoginFullName = "TestUser",
                LoginEmail = "<EMAIL>",
                Email = "<EMAIL>",
                SalesOrderNumber = 12345,
                PromiseReasonString = "Supplier delay"
            };

            var affectedRows = new AffectedRows { RowsAffected = 1 };

            _repositoryMock
                .Setup(r => r.SqlQueryRawReturnValueAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(affectedRows);

            _emailTemplateMock
                .Setup(e => e.PopulateTemplate(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()))
                .Returns("Promise date changed email body");

            _emailServiceMock
                .Setup(e => e.TrySendEmailAsync(
                    It.IsAny<string>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<Attachment>>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);

            // Verify email template was populated for promise date change
            _emailTemplateMock.Verify(
                e => e.PopulateTemplate("PromiseReasonEmail", It.IsAny<Dictionary<string, string>>()),
                Times.Once);

            // Verify email was sent
            _emailServiceMock.Verify(
                e => e.TrySendEmailAsync(
                    command.LoginEmail,
                    It.Is<List<string>>(emails => emails.Contains(command.Email)),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<string>>(),
                    It.Is<string>(subject => subject.Contains("Promised Date Changed")),
                    It.IsAny<string>(),
                    It.Is<List<string>>(replyTos => replyTos.Contains(command.LoginEmail)),
                    It.IsAny<List<Attachment>>(),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task EditSalesOrderLineHandler_WhenPromiseDateUnchanged_DoesNotSendEmail()
        {
            // Arrange
            var command = new EditSalesOrderLineCommand
            {
                SalesOrderLineId = 123,
                UpdatedBy = 456,
                ClientId = 789,
                DatePromised = new DateTime(2024, 6, 15),
                PreviousPromiseDate = new DateTime(2024, 6, 15), // Same dates
                LoginFullName = "TestUser",
                LoginEmail = "<EMAIL>",
                Email = "<EMAIL>"
            };

            var affectedRows = new AffectedRows { RowsAffected = 1 };

            _repositoryMock
                .Setup(r => r.SqlQueryRawReturnValueAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(affectedRows);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);

            // Verify email services were not called
            _emailTemplateMock.Verify(
                e => e.PopulateTemplate(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()),
                Times.Never);

            _emailServiceMock.Verify(
                e => e.TrySendEmailAsync(
                    It.IsAny<string>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<Attachment>>(),
                    It.IsAny<CancellationToken>()),
                Times.Never);
        }

        [Fact]
        public async Task EditSalesOrderLineHandler_WhenNoEmailProvided_DoesNotSendEmail()
        {
            // Arrange
            var command = new EditSalesOrderLineCommand
            {
                SalesOrderLineId = 123,
                UpdatedBy = 456,
                ClientId = 789,
                DatePromised = new DateTime(2024, 6, 15),
                PreviousPromiseDate = new DateTime(2024, 6, 10), // Different dates
                LoginFullName = "TestUser",
                LoginEmail = "<EMAIL>",
                Email = "", // No email provided
                SalesOrderNumber = 12345
            };

            var affectedRows = new AffectedRows { RowsAffected = 1 };

            _repositoryMock
                .Setup(r => r.SqlQueryRawReturnValueAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(affectedRows);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);

            // Verify no email notifications were sent
            _emailTemplateMock.Verify(
                e => e.PopulateTemplate(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()),
                Times.Never);

            _emailServiceMock.Verify(
                e => e.TrySendEmailAsync(
                    It.IsAny<string>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<Attachment>>(),
                    It.IsAny<CancellationToken>()),
                Times.Never);
        }

        [Fact]
        public async Task EditSalesOrderLineHandler_WhenRepositoryThrowsException_ThrowsException()
        {
            // Arrange
            var command = new EditSalesOrderLineCommand
            {
                SalesOrderLineId = 123,
                UpdatedBy = 456,
                ClientId = 789
            };

            _repositoryMock
                .Setup(r => r.SqlQueryRawReturnValueAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));

            // Verify no email services were called due to exception
            _emailTemplateMock.Verify(
                e => e.PopulateTemplate(It.IsAny<string>(), It.IsAny<Dictionary<string, string>>()),
                Times.Never);

            _emailServiceMock.Verify(
                e => e.TrySendEmailAsync(
                    It.IsAny<string>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<List<string>>(),
                    It.IsAny<List<Attachment>>(),
                    It.IsAny<CancellationToken>()),
                Times.Never);
        }
    }
}
