﻿using GlobalTrader2.Orders.UserCases.Orders.PriceRequest;

namespace GlobalTrader2.Orders.Test.Orders.PriceRequest
{
    public class GetPOQuoteLineDataListNuggetHandlerTests
    {
        private readonly Mock<IBaseRepository<POQuoteLineDataListNuggetReadModel>> _repositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly GetPOQuoteLineDataListNuggetHandler _handler;
        private readonly IFixture _fixture;

        public GetPOQuoteLineDataListNuggetHandlerTests()
        {
            _fixture = new Fixture();
            _repositoryMock = new Mock<IBaseRepository<POQuoteLineDataListNuggetReadModel>>();
            _mapperMock = new Mock<IMapper>();
            _handler = new GetPOQuoteLineDataListNuggetHandler(_repositoryMock.Object, _mapperMock.Object);
        }

        [Fact]
        public async Task Handle_WithValidRequest_ReturnsSuccessWithMappedData()
        {
            // Arrange
            var request = _fixture.Create<GetPOQuoteLineDataListNuggetQuery>();
            var repositoryData = _fixture.CreateMany<POQuoteLineDataListNuggetReadModel>().ToList();
            var expectedResponse = _fixture.CreateMany<POQuoteLineDataListNuggetDto>().ToList();

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(repositoryData);

            _mapperMock.Setup(m => m.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(repositoryData))
                .Returns(expectedResponse);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().BeEquivalentTo(expectedResponse);
        }

        [Fact]
        public async Task Handle_WithNullClientId_UsesZeroAsDefault()
        {
            // Arrange
            var request = _fixture.Build<GetPOQuoteLineDataListNuggetQuery>()
                .With(x => x.ClientId, (int?)null)
                .Create();

            var repositoryData = _fixture.CreateMany<POQuoteLineDataListNuggetReadModel>().ToList();
            var expectedResponse = _fixture.CreateMany<POQuoteLineDataListNuggetDto>().ToList();

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.Is<SqlParameter[]>(p => p.Any(param => param.ParameterName == "@ClientId" && param.Value.Equals(0)))))
                .ReturnsAsync(repositoryData);

            _mapperMock.Setup(m => m.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(It.IsAny<IEnumerable<POQuoteLineDataListNuggetReadModel>>()))
                .Returns(expectedResponse);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            _repositoryMock.Verify(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.Is<SqlParameter[]>(p => p.Any(param => param.ParameterName == "@ClientId" && param.Value.Equals(0)))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_WithNullOptionalParameters_UsesDBNullValue()
        {
            // Arrange
            var request = _fixture.Build<GetPOQuoteLineDataListNuggetQuery>()
                .With(x => x.TeamId, (int?)null)
                .With(x => x.DivisionId, (int?)null)
                .With(x => x.LoginId, (int?)null)
                .With(x => x.OrderBy, (int?)null)
                .With(x => x.SortDir, (int?)null)
                .Create();

            var repositoryData = _fixture.CreateMany<POQuoteLineDataListNuggetReadModel>().ToList();
            var expectedResponse = _fixture.CreateMany<POQuoteLineDataListNuggetDto>().ToList();

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(repositoryData);

            _mapperMock.Setup(m => m.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(It.IsAny<IEnumerable<POQuoteLineDataListNuggetReadModel>>()))
                .Returns(expectedResponse);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            _repositoryMock.Verify(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.Is<SqlParameter[]>(p =>
                    p.Any(param => param.ParameterName == "@TeamId" && param.Value == System.DBNull.Value) &&
                    p.Any(param => param.ParameterName == "@DivisionId" && param.Value == System.DBNull.Value) &&
                    p.Any(param => param.ParameterName == "@LoginId" && param.Value == System.DBNull.Value) &&
                    p.Any(param => param.ParameterName == "@OrderBy" && param.Value == System.DBNull.Value) &&
                    p.Any(param => param.ParameterName == "@SortDir" && param.Value == System.DBNull.Value))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_WithNullBooleanParameters_UsesFalseAsDefault()
        {
            // Arrange
            var request = _fixture.Build<GetPOQuoteLineDataListNuggetQuery>()
                .With(x => x.IncludeClosed, (bool?)null)
                .With(x => x.RecentOnly, (bool?)null)
                .Create();

            var repositoryData = _fixture.CreateMany<POQuoteLineDataListNuggetReadModel>().ToList();
            var expectedResponse = _fixture.CreateMany<POQuoteLineDataListNuggetDto>().ToList();

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(repositoryData);

            _mapperMock.Setup(m => m.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(It.IsAny<IEnumerable<POQuoteLineDataListNuggetReadModel>>()))
                .Returns(expectedResponse);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            _repositoryMock.Verify(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.Is<SqlParameter[]>(p =>
                    p.Any(param => param.ParameterName == "@IncludeClosed" && param.Value.Equals(false)) &&
                    p.Any(param => param.ParameterName == "@RecentOnly" && param.Value.Equals(false)))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_WithAllParameters_PassesCorrectParametersToRepository()
        {
            // Arrange
            var request = _fixture.Build<GetPOQuoteLineDataListNuggetQuery>()
                .With(x => x.ClientId, 123)
                .With(x => x.TeamId, 456)
                .With(x => x.DivisionId, 789)
                .With(x => x.LoginId, 101)
                .With(x => x.OrderBy, 1)
                .With(x => x.SortDir, 1)
                .With(x => x.PageIndex, 1)
                .With(x => x.PageSize, 10)
                .With(x => x.PartSearch, "TestPart")
                .With(x => x.CmSearch, "TestCM")
                .With(x => x.SalesmanSearch, 1)
                .With(x => x.IncludeClosed, true)
                .With(x => x.PoQuoteNoLo, 1)
                .With(x => x.PoQuoteNoHi, 2)
                .With(x => x.RecentOnly, true)
                .Create();

            var repositoryData = _fixture.CreateMany<POQuoteLineDataListNuggetReadModel>().ToList();
            var expectedResponse = _fixture.CreateMany<POQuoteLineDataListNuggetDto>().ToList();

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(repositoryData);

            _mapperMock.Setup(m => m.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(It.IsAny<IEnumerable<POQuoteLineDataListNuggetReadModel>>()))
                .Returns(expectedResponse);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            _repositoryMock.Verify(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.Is<SqlParameter[]>(p =>
                    p.Any(param => param.ParameterName == "@ClientId" && param.Value.Equals(123)) &&
                    p.Any(param => param.ParameterName == "@TeamId" && param.Value.Equals(456)) &&
                    p.Any(param => param.ParameterName == "@DivisionId" && param.Value.Equals(789)) &&
                    p.Any(param => param.ParameterName == "@LoginId" && param.Value.Equals(101)) &&
                    p.Any(param => param.ParameterName == "@OrderBy" && param.Value.Equals(1)) &&
                    p.Any(param => param.ParameterName == "@SortDir" && param.Value.Equals(1)) &&
                    p.Any(param => param.ParameterName == "@PageIndex" && param.Value.Equals(1)) &&
                    p.Any(param => param.ParameterName == "@PageSize" && param.Value.Equals(10)) &&
                    p.Any(param => param.ParameterName == "@PartSearch" && param.Value.Equals("TestPart")) &&
                    p.Any(param => param.ParameterName == "@CMSearch" && param.Value.Equals("TestCM")) &&
                    p.Any(param => param.ParameterName == "@SalesmanSearch" && param.Value.Equals(1)) &&
                    p.Any(param => param.ParameterName == "@IncludeClosed" && param.Value.Equals(true)) &&
                    p.Any(param => param.ParameterName == "@POQuoteNoLo" && param.Value.Equals(1)) &&
                    p.Any(param => param.ParameterName == "@POQuoteNoHi" && param.Value.Equals(2)) &&
                    p.Any(param => param.ParameterName == "@RecentOnly" && param.Value.Equals(true)))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_WithDateParameters_PassesCorrectDatesToRepository()
        {
            // Arrange
            var dateFrom = _fixture.Create<DateTime>();
            var dateTo = _fixture.Create<DateTime>();

            var request = _fixture.Build<GetPOQuoteLineDataListNuggetQuery>()
                .With(x => x.DateQuotedFrom, dateFrom)
                .With(x => x.DateQuotedTo, dateTo)
                .Create();

            var repositoryData = _fixture.CreateMany<POQuoteLineDataListNuggetReadModel>().ToList();
            var expectedResponse = _fixture.CreateMany<POQuoteLineDataListNuggetDto>().ToList();

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(repositoryData);

            _mapperMock.Setup(m => m.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(It.IsAny<IEnumerable<POQuoteLineDataListNuggetReadModel>>()))
                .Returns(expectedResponse);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            _repositoryMock.Verify(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.Is<SqlParameter[]>(p =>
                    p.Any(param => param.ParameterName == "@DatePOQuotedFrom" && param.Value.Equals(dateFrom)) &&
                    p.Any(param => param.ParameterName == "@DatePOQuotedTo" && param.Value.Equals(dateTo)))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_WithCancellationToken_PassesTokenToRepository()
        {
            // Arrange
            var request = _fixture.Create<GetPOQuoteLineDataListNuggetQuery>();
            var repositoryData = _fixture.CreateMany<POQuoteLineDataListNuggetReadModel>().ToList();
            var expectedResponse = _fixture.CreateMany<POQuoteLineDataListNuggetDto>().ToList();
            var cancellationToken = new CancellationToken();

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(repositoryData);

            _mapperMock.Setup(m => m.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(It.IsAny<IEnumerable<POQuoteLineDataListNuggetReadModel>>()))
                .Returns(expectedResponse);

            // Act
            var result = await _handler.Handle(request, cancellationToken);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            _repositoryMock.Verify(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()),
                Times.Once);
        }

        [Fact]
        public async Task Handle_WhenRepositoryReturnsEmptyList_ReturnsSuccessWithEmptyData()
        {
            // Arrange
            var request = _fixture.Create<GetPOQuoteLineDataListNuggetQuery>();
            var emptyRepositoryData = new List<POQuoteLineDataListNuggetReadModel>();
            var emptyResponse = new List<POQuoteLineDataListNuggetDto>();

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(emptyRepositoryData);

            _mapperMock.Setup(m => m.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(emptyRepositoryData))
                .Returns(emptyResponse);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().BeEmpty();
        }

        [Fact]
        public async Task Handle_WhenRepositoryThrowsException_PropagatesException()
        {
            // Arrange
            var request = _fixture.Create<GetPOQuoteLineDataListNuggetQuery>();
            var expectedException = new Exception("Repository error");

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(request, CancellationToken.None));
            exception.Should().Be(expectedException);
        }

        [Fact]
        public async Task Handle_WhenMapperThrowsException_PropagatesException()
        {
            // Arrange
            var request = _fixture.Create<GetPOQuoteLineDataListNuggetQuery>();
            var repositoryData = _fixture.CreateMany<POQuoteLineDataListNuggetReadModel>().ToList();
            var expectedException = new Exception("Mapper error");

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(repositoryData);

            _mapperMock.Setup(m => m.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(It.IsAny<IEnumerable<POQuoteLineDataListNuggetReadModel>>()))
                .Throws(expectedException);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(request, CancellationToken.None));
            exception.Should().Be(expectedException);
        }

        [Fact]
        public async Task Handle_VerifiesCorrectStoredProcedureCall()
        {
            // Arrange
            var request = _fixture.Create<GetPOQuoteLineDataListNuggetQuery>();
            var repositoryData = _fixture.CreateMany<POQuoteLineDataListNuggetReadModel>().ToList();
            var expectedResponse = _fixture.CreateMany<POQuoteLineDataListNuggetDto>().ToList();

            _repositoryMock.Setup(r => r.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(repositoryData);

            _mapperMock.Setup(m => m.Map<IEnumerable<POQuoteLineDataListNuggetDto>>(It.IsAny<IEnumerable<POQuoteLineDataListNuggetReadModel>>()))
                .Returns(expectedResponse);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            _repositoryMock.Verify(r => r.SqlQueryRawAsync(
                It.Is<string>(sql =>
                                   sql.Contains("@ClientId") &&
                                   sql.Contains("@TeamId") &&
                                   sql.Contains("@DivisionId") &&
                                   sql.Contains("@LoginId") &&
                                   sql.Contains("@OrderBy") &&
                                   sql.Contains("@SortDir") &&
                                   sql.Contains("@PageIndex") &&
                                   sql.Contains("@PageSize") &&
                                   sql.Contains("@PartSearch") &&
                                   sql.Contains("@CMSearch") &&
                                   sql.Contains("@SalesmanSearch") &&
                                   sql.Contains("@IncludeClosed") &&
                                   sql.Contains("@POQuoteNoLo") &&
                                   sql.Contains("@POQuoteNoHi") &&
                                   sql.Contains("@DatePOQuotedFrom") &&
                                   sql.Contains("@DatePOQuotedTo") &&
                                   sql.Contains("@RecentOnly")),
                It.IsAny<SqlParameter[]>()),
                Times.Once);
        }
    }
}