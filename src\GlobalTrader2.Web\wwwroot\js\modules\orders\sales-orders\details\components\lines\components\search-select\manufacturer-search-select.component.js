import { SearchSelectComponent } from '../../../../../../../../components/search-select/search-select.component.js?v=#{BuildVersion}#';
export class ManufactureSearchSelectComponent extends SearchSelectComponent {
    constructor(searchInputId, inputHiddenValueId, mode, apiKeySearch, apiURL, charsToSearch = 2, filterSearchIndex = null) {
        super(searchInputId, inputHiddenValueId, mode, apiKeySearch, apiURL, charsToSearch, filterSearchIndex);
        this.isRestrictedManufacturer = false;
    }

    // Override
    createResultItem(result) {
        const item = super.createResultItem(result);
        item.addEventListener("mousedown", (event) => {
            event.preventDefault();
            $(this.searchInput).trigger("manufacturerSelected", result);
        });
        return item;
    }

    async createSelectedItem(item) {
        let response = await GlobalTrader.ApiClient.getAsync(`/manufacturers/check-restricted/${item.value}`);

        const condition = response.data.isRestrictedManufacturer;
        this.trigger('selectItem', { ...item, condition });

        const result = await GlobalTrader.ApiClient.getAsync(`/manufacturers/advisory-note/${item.value}`);
        let advisoryNote = result.data[0].advisoryNotes;

        const selectedItem = document.createElement("span");
        selectedItem.className = "selected-item me-1";
        selectedItem.innerHTML = `
        <span class="selected-item-content">${item.label}</span>
        ${advisoryNote ? `<img src="/img/icons/circle-exclamation-red.svg" class="ms-1 rounded-circle bg-white" height="14" title="${advisoryNote}"/>` : ''}
        <button type="button" class="btn-close" aria-label="Close"></button>
        `;

        // Add event listener for remove button
        selectedItem.querySelector(".btn-close").addEventListener("click", () => {
            this.removeItem(selectedItem, item);
        });
        
        if (condition) {
            this.isRestrictedManufacturer = true;
            const wrapper = document.createElement("span");
            const errorMessage = document.createElement("p");
            errorMessage.className = "text-break text-danger mb-0 mt-1";
            errorMessage.innerHTML = response.data.restrictedMFRMessage;
            wrapper.appendChild(selectedItem);
            wrapper.appendChild(errorMessage);
            return wrapper;
        }


        return selectedItem;
    }

    removeItem(selectedItem, itemToRemove) {
        this.isRestrictedManufacturer = false;
        super.removeItem(selectedItem, itemToRemove);
    }
}
