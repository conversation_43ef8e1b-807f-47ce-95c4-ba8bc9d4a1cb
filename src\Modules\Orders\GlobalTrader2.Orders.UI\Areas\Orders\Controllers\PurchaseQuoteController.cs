﻿using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.Orders.PurchaseQuote;
using GlobalTrader2.Orders.UI.ViewModel.PurchaseQuote;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PriceRequest;
using GlobalTrader2.Orders.UserCases.Orders.PriceRequest.Queries;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Helper;
using Microsoft.Extensions.Localization;
using System.Globalization;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Core.Enums;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;

[ApiController]
[Authorize]
[Route("api/orders/purchase-quote")]
public class PurchaseQuoteController(IMediator mediator, SessionManager sessionManager, IMapper mapper, IStringLocalizer<Misc> miscLocalizer, SecurityManager securityManager) : ApiBaseController
{
    private readonly IMediator _mediator = mediator;
    private readonly SessionManager _sessionManager = sessionManager;
    private readonly IMapper _mapper = mapper;
    private readonly IStringLocalizer<Misc> _miscLocalizer = miscLocalizer;

    [HttpPost("list")]
    public async Task<IActionResult> GetPurchaseQuote(GetPurchaseQuoteRequest request, CancellationToken cancellationToken)
    {
        request.ClientId = ClientId;
        request.LoginId = (request.ViewLevelList == (int)ViewLevelList.My) ? UserId : null;
        request.TeamId = (request.ViewLevelList == (int)ViewLevelList.Team) ? _sessionManager.GetInt32(SessionKey.LoginTeamID) : null;
        request.DivisionId = (request.ViewLevelList == (int)ViewLevelList.Division) ? _sessionManager.GetInt32(SessionKey.LoginDivisionID) : null;

        var query = new GetPOQuoteLineDataListNuggetQuery
        {
            PageIndex = request.Index,
            PageSize = request.Size,
            OrderBy = request.OrderBy,
            SortDir = request.SortDir,
            ClientId = request.ClientId,
            LoginId = request.LoginId,
            TeamId = request.TeamId,
            DivisionId = request.DivisionId,
            PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
            CmSearch = request.CMSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.CMSearch) : null,
            PoQuoteNoHi = request.POQuoteNoHi,
            PoQuoteNoLo = request.POQuoteNoLo,
            DateQuotedFrom = request.DatePOQuotedFrom,
            DateQuotedTo = request.DatePOQuotedTo,
            IncludeClosed = request.IncludeClosed ?? false,
            RecentOnly = request.RecentOnly ?? false,
            SalesmanSearch = request.Salesman,
        };

        var result = await _mediator.Send(query, cancellationToken);
        var resultView = _mapper.Map<BaseResponse<IEnumerable<PurchaseQuoteViewModel>>>(result);

        var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

        var response = new DatatableResponse<IEnumerable<PurchaseQuoteViewModel>>()
        {
            Success = resultView.Success,
            Data = resultView.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetPurchaseOrder(int id)
    {
        var result = await _mediator.Send(new GetPOQuoteDetailsQuery(id));

        if(result.Data != null)
        {
            result.Data.LastUpdatedByInText = await FormatDLUP(_mediator, _miscLocalizer, null, result.Data.DLUP);
        }

        return Ok(result);
    }
}
