import { SearchSelectComponent } from '../../../../../../../../components/search-select/search-select.component.js?v=#{BuildVersion}#';
export class ProductSearchSelectComponent extends SearchSelectComponent {
    constructor(searchInputId, inputHiddenValueId, mode, apiKeySearch, apiURL, charsToSearch = 2, filterSearchIndex = null) {
        super(searchInputId, inputHiddenValueId, mode, apiKeySearch, apiURL, charsToSearch, filterSearchIndex);
        this.isRestrictedManufacturer = false;
    }

    // Override
    async selectItem(item) {
        this.trigger('selectItem', item);
        await super.selectItem(item);
    }

    handleResultSelection(item) {
        super.handleResultSelection(item);
        if (item.isDisplayProductMessage) {
            setTimeout(function () {
                let formattedStr = GlobalTrader.StringHelper.replaceBRTags(item.productMessage.replaceAll("&#013;", "\n\n")).trim();
                alert(formattedStr);
            }, 300);
        }
    }

    renderResults(data) {
        data.label = GlobalTrader.StringHelper.setCleanTextValue(data.label);
        super.renderResults(data);
    }
}
