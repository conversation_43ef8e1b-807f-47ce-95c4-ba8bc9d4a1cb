using GlobalTrader2.Aggregator.UseCases.DataList.DropDownStatusReason;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.StatusReason;
using Moq;
using System.Linq.Expressions;

namespace GlobalTrader2.Aggregator.Test.DataList.DropDownStatusReason
{
    public class DropDownStatusReasonHandlerTest
    {
        private readonly Mock<IBaseRepository<StatusReason>> _repositoryMock;
        private readonly DropDownStatusReasonHandler _handler;

        public DropDownStatusReasonHandlerTest()
        {
            _repositoryMock = new Mock<IBaseRepository<StatusReason>>();
            _handler = new DropDownStatusReasonHandler(_repositoryMock.Object);
        }

        [Fact]
        public async Task DropDownStatusReasonHandler_WhenValidSectionAndActiveReasons_ReturnsSuccessWithOrderedData()
        {
            // Arrange
            var section = "PS";
            var query = new DropDownStatusReasonQuery(section);
            var statusReasons = new List<StatusReason>
            {
                new StatusReason 
                { 
                    StatusReasonId = 2, 
                    Name = "Reason B", 
                    Email = "<EMAIL>", 
                    Section = "PS", 
                    Inactive = false, 
                    SortOrder = 2 
                },
                new StatusReason 
                { 
                    StatusReasonId = 1, 
                    Name = "Reason A", 
                    Email = "<EMAIL>", 
                    Section = "PS", 
                    Inactive = false, 
                    SortOrder = 1 
                },
                new StatusReason 
                { 
                    StatusReasonId = 3, 
                    Name = "Reason A2", 
                    Email = "<EMAIL>", 
                    Section = "PS", 
                    Inactive = false, 
                    SortOrder = 1 
                }
            };

            var filteredAndOrdered = statusReasons
                .Where(sr => sr.Section == section && !sr.Inactive)
                .OrderBy(sr => sr.SortOrder)
                .ThenBy(sr => sr.Name)
                .ToList();

            _repositoryMock
                .Setup(r => r.ListAsync(
                    It.IsAny<Expression<Func<StatusReason, bool>>>(),
                    It.IsAny<Func<IQueryable<StatusReason>, IOrderedQueryable<StatusReason>>>(),
                    It.IsAny<Expression<Func<StatusReason, object?>>[]>()))
                .ReturnsAsync(filteredAndOrdered);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(3, result.Data.Count);
            
            // Verify ordering: first by SortOrder, then by Name
            Assert.Equal(1, result.Data[0].Id);
            Assert.Equal("Reason A", result.Data[0].Name);
            Assert.Equal("<EMAIL>", result.Data[0].Email);
            
            Assert.Equal(3, result.Data[1].Id);
            Assert.Equal("Reason A2", result.Data[1].Name);
            Assert.Equal("<EMAIL>", result.Data[1].Email);
            
            Assert.Equal(2, result.Data[2].Id);
            Assert.Equal("Reason B", result.Data[2].Name);
            Assert.Equal("<EMAIL>", result.Data[2].Email);
        }

        [Fact]
        public async Task DropDownStatusReasonHandler_WhenNoMatchingStatusReasons_ReturnsSuccessWithEmptyList()
        {
            // Arrange
            var section = "NONEXISTENT";
            var query = new DropDownStatusReasonQuery(section);
            var statusReasons = new List<StatusReason>();

            _repositoryMock
                .Setup(r => r.ListAsync(
                    It.IsAny<Expression<Func<StatusReason, bool>>>(),
                    It.IsAny<Func<IQueryable<StatusReason>, IOrderedQueryable<StatusReason>>>(),
                    It.IsAny<Expression<Func<StatusReason, object?>>[]>()))
                .ReturnsAsync(statusReasons);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Empty(result.Data);
        }

        [Fact]
        public async Task DropDownStatusReasonHandler_WhenRepositoryThrowsException_ThrowsException()
        {
            // Arrange
            var section = "PS";
            var query = new DropDownStatusReasonQuery(section);

            _repositoryMock
                .Setup(r => r.ListAsync(
                    It.IsAny<Expression<Func<StatusReason, bool>>>(),
                    It.IsAny<Func<IQueryable<StatusReason>, IOrderedQueryable<StatusReason>>>(),
                    It.IsAny<Expression<Func<StatusReason, object?>>[]>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));
        }
    }
}
