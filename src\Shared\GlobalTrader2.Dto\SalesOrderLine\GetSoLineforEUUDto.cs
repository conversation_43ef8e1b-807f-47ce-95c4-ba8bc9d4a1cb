﻿namespace GlobalTrader2.Dto.SalesOrderLine
{
    public class GetAllSoLinesforEuuDto
    {
        public int SalesOrderLineId { get; set; }
        public int SalesOrderNo { get; set; }
        public string? CustomerPart { get; set; }
        public string Part { get; set; } = string.Empty;
        public bool Inactive { get; set; }
        public int? SOSerialNo { get; set; }
        public string? ECCNCode { get; set; }
        public int? ECCNCodeNo { get; set; }
        public bool IsECCNWarning { get; set; }
        public bool? IsEUUPDFAvailable { get; set; }
        public string? UploadDate { get; set; }
        public string? EUUUPDFploadName { get; set; }
        public bool OGEL_Required { get; set; }
    }
}