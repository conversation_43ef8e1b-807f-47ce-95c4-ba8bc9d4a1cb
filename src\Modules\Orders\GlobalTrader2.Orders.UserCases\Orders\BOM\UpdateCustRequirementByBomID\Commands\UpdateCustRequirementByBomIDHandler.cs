
namespace GlobalTrader2.Orders.UserCases.Orders.BOM.UpdateCustRequirementByBomID.Commands;

public class UpdateCustRequirementByBomIDHandler(IBaseRepository<object> repository) : IRequestHandler<UpdateCustRequirementByBomIDCommand, BaseResponse<bool>>
{
    public async Task<BaseResponse<bool>> Handle(UpdateCustRequirementByBomIDCommand request, CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(request);

        var response = new BaseResponse<bool>();

        var output = new SqlParameter("@RowsAffected", SqlDbType.Int) { Direction = ParameterDirection.Output };

        var parameters = new List<SqlParameter>
        {
            new("@BomId", SqlDbType.Int) { Value = request.BomId },
            new("@UpdatedBy", SqlDbType.Int) { Value = request.UpdatedBy ?? (object)DBNull.Value },
            new("@ClientNo", SqlDbType.Int) { Value = request.ClientNo ?? (object)DBNull.Value },
            new("@ReqIds", SqlDbType.NVarChar, -1) { Value = request.ReqIds ?? (object)DBNull.Value },
            new("@BOMStatus", SqlDbType.Int) { Value = request.BOMStatus ?? (object)DBNull.Value },
            output
        };

        var queryStr = $"{StoredProcedures.Update_CustRequirementByBomID} @BomId, @UpdatedBy, @ClientNo, @ReqIds, @BOMStatus, @RowsAffected OUTPUT";

        await repository.ExecuteSqlRawAsync(queryStr, [.. parameters]);

        int rowsAffected = output.Value != DBNull.Value && output.Value != null
            ? Convert.ToInt32(output.Value.ToString())
            : 0;

        response.Success = rowsAffected > 0;
        response.Data = rowsAffected > 0;

        return response;
    }
}