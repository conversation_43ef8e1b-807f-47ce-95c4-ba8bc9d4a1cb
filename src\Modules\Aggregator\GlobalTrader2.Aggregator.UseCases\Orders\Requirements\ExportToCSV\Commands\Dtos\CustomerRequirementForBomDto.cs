﻿namespace GlobalTrader2.Aggregator.UseCases.Orders.Requirements.ExportToCSV.Commands.Dtos
{
    public class CustomerRequirementForBomDto
    {
        public string? RequirementNumber { get; init; }
        public int Company { get; init; }
        public int QuantityQuoted { get; init; }
        public string? MPNQuoted { get; init; }
        public string? ManufacturerName { get; init; }
        public string? DateCode { get; init; }
        public string? PackageType { get; init; }
        public string? ProductType { get; init; }
        public string? SPQ { get; init; }
        public string? MOQ { get; init; }
        public string? LeadTimeWks { get; init; }
        public string? RohsYN { get; init; }
        public string? TQSA { get; init; }
        public string? LTB { get; init; }
        public string? FactorySealed { get; init; }
        public string? MSL { get; init; }
        public decimal UnitPrice { get; init; }
        public string? SupplierNotes { get; init; }
        public string? RequiredDate { get; init; }
        public string? TargetPrice { get; init; }
        public string? CRInstructionsNotes { get; init; }
        public string? LyticaManufacturerRef { get; init; }
        public decimal? LyticaAveragePrice { get; init; }
        public decimal? LyticaTargetPrice { get; init; }
        public decimal? LyticaMarketLeading { get; init; }
    }
}
