﻿$(() => {
    $.widget("custom.dropdown", {
        options: {
            // API
            serverside: true,
            deferLoad: false, // for first time initialize
            endpoint: '',
            isCacheApplied: true,
            isCacheAppliedOnInit: false,
            params: {},
            handleResponse: (response) => response.data || [],

            // UI
            valueKey: 'id',
            textKey: 'name',
            placeholder: 'Select...',
            placeholderValue: "0",
            fixedDataSource: [],
            selectedValue: null,
            refreshButton: null,
            // Callback
            refreshCallback: null,
            onSelected: null,
            isHideRefresButton: false,
        },

        _create: async function () {
            this._initializeElement();

            this._setupEvents();
            this.defaultSelect = this.options.placeholderValue;
            this.currentSelected = this.options.selectedValue == this.defaultSelect ? null : this.options.selectedValue;

            if (!this.options.serverside) {
                if (!this.options.isHideRefresButton) {
                    this._createRefreshButton();
                }
                if (!this.options.deferLoad) {
                    await this._initializeDropdown();
                }
            }

            this.refreshButton = this.options.refreshButton || this.refreshButton;
            this._setupRefreshEvent(this.refreshButton);
        },

        reload: async function (url, params) {
            if (url != null) {
                this._setOption("endpoint", url)
            }
            if (params != null) {
                this._setOption("params", { ...this.options.params, ...params })
            }
            await this._initializeDropdown();
        },

        select: function (value) {
            this._select(value == this.defaultSelect ? null : value);
        },

        _select: function (value) {
            if (value == null || (this.data && !this.data.some(item => item[this.options.valueKey] == value))) {
                $(this.element).val(this.defaultSelect);
            } else {
                $(this.element).val(value);
            }

            this.currentSelected = value;
            this._onSelected(this.currentSelected);
        },

        _initializeElement: function () {
            this.element.addClass("form-select me-2");
        },

        _setupEvents: function () {
            this.element.on("change", (event) => {
                const selectedValue = $(event.target).val();
                this.currentSelected = selectedValue == this.defaultSelect ? null : selectedValue;
                this._onSelected(this.currentSelected);
            });
        },

        _setupRefreshEvent: function (refreshButton) {
            if (!refreshButton) {
                console.warn("Refresh button is null or undefined. Event binding skipped.");
                return;
            }

            refreshButton.on("click", (event) => {
                event.preventDefault();
                this._refresh();
            });
        },

        _createRefreshButton: function () {
            this.refreshButton = $("<a></a>")
                .attr("href", "#")
                .addClass("select-menu-gtv2-refresh-button")
                .html('<img src="/img/icons/refresh-button.png" alt="refresh-button">');

            this.wrapper = $("<div></div>")
                .addClass("d-flex align-items-center justify-content-center");

            this.element.before(this.wrapper);
            this.wrapper.append(this.element, this.refreshButton);
        },

        _initializeDropdown: async function () {
            try {

                this.data = await this._fetchData(this.options.isCacheApplied && this.options.isCacheAppliedOnInit);
                this._renderDropdown(this.data);
            } catch (error) {
                console.error("Error initializing dropdown:", error);
            }
        },

        _fetchData: async function (isRefreshCached = false) {
            if (!this.options.endpoint) {
                const result = this.options.handleResponse({ data: this.options.fixedDataSource });

                if (this.options.onSuccessLoaded) {
                    this.options.onSuccessLoaded.call(this, result, this.currentSelected);
                }

                return result
            }

            try {
                const response = this.options.isCacheApplied ?
                    await GlobalTrader.ApiClient.getAsync(this.options.endpoint, { ...this.options.params, refreshData: isRefreshCached })
                    : await GlobalTrader.ApiClient.getAsync(this.options.endpoint, { ...this.options.params });

                const result = this.options.handleResponse(response);

                if (this.options.onSuccessLoaded) {
                    this.options.onSuccessLoaded.call(this,result, this.currentSelected);
                }

                return result
            } catch (error) {
                console.error("Error fetching data:", error);
                throw error;
            }
        },


        _refresh: async function () {
            if (this._refreshing) return;

            this._refreshing = true;
            const stopRefresh = this._setRefreshingState();

            try {
                this.data = await this._fetchData(true);
                this._renderDropdown(this.data);

                if (this.options.refreshCallback) {
                    this.options.refreshCallback();
                }
            } catch (error) {
                console.error("Error refreshing data:", error);
            } finally {
                this._refreshing = false;
                stopRefresh();
            }
        },

        refresh: async function () {
            await this._refresh();
        },

        _setRefreshingState: function () {
            const currentState = this.element.prop("disabled");
            const _self = this;

            this.element.prop("disabled", true);
            this.refreshButton.toggleClass("disabled", true);

            this.element.html('<option>Loading...</option>');

            return function () {
                _self.refreshButton.toggleClass("disabled", false);
                _self.element.prop("disabled", currentState);
            }
        },

        _renderDropdown: function (data) {
            const { valueKey, textKey, placeholder } = this.options;
            this.element.empty();
            if (placeholder != null) {
                this.element.append(`<option value=${this.options.placeholderValue}>${placeholder}</option>`);
            }
            data.forEach(item => {
                const $option = $("<option>").attr("value", item[valueKey]).text(item[textKey]); // prevent XSS

                this.element.append($option);
            });

            this._onRendered();
        },
        _onSelected: function (selectedValue) {
            if (this.options.onSelected) {
                this.options.onSelected(this.data, selectedValue);
            }
        },
        _onRendered: function () {
            this._select(this.currentSelected);
        },
        _destroy: function () {
            this.element.off("change");
            this.refreshButton.remove();
        },
        reset: function () {
            this._select(null);
        },
        selectedValue: function () {
            return this.currentSelected;
        },
        getSelectedExtraData: function () {
            if (!this.data || !this.currentSelected) {
                return null;
            }
            const result = this.data.find(item => item[this.options.valueKey] === parseInt(this.currentSelected))
            return result ?? null;
        },
    });
});