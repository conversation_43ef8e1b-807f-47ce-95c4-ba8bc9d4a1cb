using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries.Dtos;
using Microsoft.Data.SqlClient;
using System.Data;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.BOM.BOMListForCustomerRequirement.Queries
{
    public class GetBOMListForCustomerRequirementHandlerTest
    {
        private readonly IFixture _fixture;
        private readonly Mock<IBaseRepository<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement>> _repositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly GetBOMListForCustomerRequirementHandler _handler;

        public GetBOMListForCustomerRequirementHandlerTest()
        {
            _fixture = new Fixture().Customize(new AutoMoqCustomization());
            _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

            _repositoryMock = new Mock<IBaseRepository<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement>>();
            _mapperMock = new Mock<IMapper>();

            _handler = new GetBOMListForCustomerRequirementHandler(
                _repositoryMock.Object,
                _mapperMock.Object);
        }

        [Fact]
        public async Task Handle_ShouldCallRepositoryWithCorrectStoredProcedure()
        {
            // Arrange
            var query = new GetBOMListForCustomerRequirementQuery
            {
                BOMNo = 789,
                ClientID = 101,
                IsPoHub = false
            };

            var entityResult = _fixture.Create<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement>();
            var dtoResult = _fixture.Create<BOMListForCustomerRequirementDto>();

            _repositoryMock
                .Setup(repo => repo.SqlQueryRawAsync(
                    It.IsAny<string>(),
                    It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement> { entityResult });

            _mapperMock
                .Setup(mapper => mapper.Map<BOMListForCustomerRequirementDto>(It.IsAny<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement>()))
                .Returns(dtoResult);

            // Act
            await _handler.Handle(query, CancellationToken.None);

            // Assert
            _repositoryMock.Verify(repo => repo.SqlQueryRawAsync(
                It.Is<string>(sql => sql.Contains("[dbo].[usp_selectAll_CustomerRequirement_for_BOM]") &&
                                   sql.Contains("@BOMNo") &&
                                   sql.Contains("@ClientID") &&
                                   sql.Contains("@IsPoHub")),
                It.IsAny<SqlParameter[]>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldSetCorrectSqlParameters()
        {
            // Arrange
            var query = new GetBOMListForCustomerRequirementQuery
            {
                BOMNo = 555,
                ClientID = 666,
                IsPoHub = true
            };

            var entityResult = _fixture.Create<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement>();
            var dtoResult = _fixture.Create<BOMListForCustomerRequirementDto>();

            _repositoryMock
                .Setup(repo => repo.SqlQueryRawAsync(
                    It.IsAny<string>(),
                    It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement> { entityResult });

            _mapperMock
                .Setup(mapper => mapper.Map<BOMListForCustomerRequirementDto>(It.IsAny<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement>()))
                .Returns(dtoResult);

            // Act
            await _handler.Handle(query, CancellationToken.None);

            // Assert
            _repositoryMock.Verify(repo => repo.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.Is<SqlParameter[]>(parameters =>
                    parameters.Length == 3 &&
                    parameters.Any(p => p.ParameterName == "@BOMNo" &&
                                       p.SqlDbType == SqlDbType.Int &&
                                       p.Value.Equals(555)) &&
                    parameters.Any(p => p.ParameterName == "@ClientID" &&
                                       p.SqlDbType == SqlDbType.Int &&
                                       p.Value.Equals(666)) &&
                    parameters.Any(p => p.ParameterName == "@IsPoHub" &&
                                       p.SqlDbType == SqlDbType.Bit &&
                                       p.Value.Equals(true)))),
                Times.Once);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task Handle_WithDifferentIsPoHubValues_ShouldSetCorrectParameter(bool isPoHub)
        {
            // Arrange
            var query = new GetBOMListForCustomerRequirementQuery
            {
                BOMNo = 123,
                ClientID = 456,
                IsPoHub = isPoHub
            };

            var entityResult = _fixture.Create<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement>();
            var dtoResult = _fixture.Create<BOMListForCustomerRequirementDto>();

            _repositoryMock
                .Setup(repo => repo.SqlQueryRawAsync(
                    It.IsAny<string>(),
                    It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement> { entityResult });

            _mapperMock
                .Setup(mapper => mapper.Map<BOMListForCustomerRequirementDto>(It.IsAny<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement>()))
                .Returns(dtoResult);

            // Act
            await _handler.Handle(query, CancellationToken.None);

            // Assert
            _repositoryMock.Verify(repo => repo.SqlQueryRawAsync(
                It.IsAny<string>(),
                It.Is<SqlParameter[]>(parameters =>
                    parameters.Any(p => p.ParameterName == "@IsPoHub" &&
                                       p.Value.Equals(isPoHub)))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_WhenMapperReturnsNull_ShouldReturnSuccessWithNullData()
        {
            // Arrange
            var query = _fixture.Create<GetBOMListForCustomerRequirementQuery>();
            var entityResult = _fixture.Create<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement>();
            BOMListForCustomerRequirementDto? nullDto = null;

            _repositoryMock
                .Setup(repo => repo.SqlQueryRawAsync(
                    It.IsAny<string>(),
                    It.IsAny<SqlParameter[]>()))
                .ReturnsAsync(new List<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement> { entityResult });

            _mapperMock
                .Setup(mapper => mapper.Map<BOMListForCustomerRequirementDto>(It.IsAny<GlobalTrader2.Core.Domain.Entities.BOMListForCustomerRequirement>()))
                .Returns(nullDto);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Null(result.Data);
        }

        [Fact]
        public async Task Handle_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var query = _fixture.Create<GetBOMListForCustomerRequirementQuery>();
            var expectedException = new InvalidOperationException("Database error");

            _repositoryMock
                .Setup(repo => repo.SqlQueryRawAsync(
                    It.IsAny<string>(),
                    It.IsAny<SqlParameter[]>()))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var actualException = await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _handler.Handle(query, CancellationToken.None));

            Assert.Equal(expectedException.Message, actualException.Message);
        }

    }
}
