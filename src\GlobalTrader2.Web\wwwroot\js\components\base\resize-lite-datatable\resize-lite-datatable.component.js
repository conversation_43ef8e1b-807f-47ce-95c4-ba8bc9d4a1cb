import { EventEmitter } from '../event-emmiter.js?v=#{BuildVersion}#'
import { ResizeDatatableEvents } from './resize-lite-datatable-events.constanst.js?v=#{BuildVersion}#'
export class ResizeLiteDatatable extends EventEmitter {
    constructor(tableId, url = '', datatableConfigs = {}) {
        super();
        this.$table = $(`#${tableId}`);
        this.datatable = null;
        this._datatableConfigs = datatableConfigs;
        this._url = url;
        this._hasUrl = url?.length > 0;
        this._isFirstDraw = true;
    }

    init() {
        this.$table
            .on('preXhr.dt', (e, settings, data) => { // Before calling ajax
                this.trigger(ResizeDatatableEvents.PRE_XHR, e, settings, data);
            })
            .on('draw.dt', () => { // After drawing is complete
                if (this._hasUrl) {
                    if (!this._isFirstDraw) {
                        this.trigger(ResizeDatatableEvents.DRAW);
                    }
                    else {
                        this._isFirstDraw = false
                    }
                }
                else {
                    this.trigger(ResizeDatatableEvents.DRAW);
                }
            });

        this.datatable = this.$table
            .ResizeDataTable({
                serverSide: false,
                ajax: {
                    url: this._url,
                    dataSrc: 'data',
                    type: 'GET',
                    contentType: 'application/json',
                },
                resizeConfig: {
                    numberOfRowToShow: 10
                },
                info: false,
                responsive: true,
                select: {
                    style: 'single',
                    toggleable: false,
                    info: false,
                },
                language: {
                    emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                },
                paging: false,
                ordering: false,
                searching: false,
                scrollCollapse: true,
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('text-start');
                },
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('tabindex', '0');
                },
                ...this._datatableConfigs
            });

        this.datatable
            .on('select', async (e, dt, type, indexes) => {
                await this.triggerAsync(ResizeDatatableEvents.SELECT, e, dt, type, indexes);
            })
            .on('deselect', async (e, dt, type, indexes) => {
                this.trigger(ResizeDatatableEvents.DESELECT, e, dt, type, indexes);
            });

        this.$table.on('keydown', 'tr', (e) => {
            if (e.key === 'Enter') {
                const $row = $(e.currentTarget);
                if (!$row.hasClass('selected')) {
                    this.datatable.row($row).select();
                }
            }
        });
    }

    async reload(selectedRowId = null) {
        await GlobalTrader.Helper.reloadResizeDatatable(this.datatable, selectedRowId);
    }

    renderDatatable(data, selectedRowId = null) {

        this.datatable.rows().deselect();
        this.datatable.clear();
        this.datatable.rows.add(data).draw();

        if (selectedRowId != null) {
            this.datatable.rows().every(function () {
                if (this.id() === String(selectedRowId)) {
                    this.select();
                }
            });
            GlobalTrader.Helper.scrollToRow(this.datatable, selectedRowId);
        }
    }

    selectRowById(rowId) {
        if (!rowId) {
            return;
        }
        this.datatable.rows().deselect();
        const targetId = String(rowId);
        let index = null;

        this.datatable.rows().every(function () {
            if (this.id() === targetId) {
                index = this.index();
                return false;
            }
            return true;
        });

        if (index !== null) {
            this.datatable.row(index).select();
            GlobalTrader.Helper.scrollToRow(this.datatable, targetId);
        }
    }
}
