﻿.status-text {
    font-size: 12px;
    color: #459E00;
}

.client-text {
    font-size: 12px;
}

.highlight {
    background-color: yellow;
}

input[readonly].has-value {
    background-color: var(--input-background) !important;
}

.cc-send-purchase-hub {
    width: 93.25%;
}

#header-message.form-error-summary {
    padding: 5px;
}

#header-message img {
    width: 20px;
    height: 20px;
}

.section-error-summary {
    display: flex;
    gap: 5px;
    align-items: center;
    background: #F8D7DA;
    color: #EC3814;
    line-height: 14.48px;
    padding: 5px;
}

.section-error-summary div p {
    margin-bottom: 0;
}


.section-error-summary img {
    width: 20px;
    height: 20px;
}

#part-detail-sourcing-results-box ::deep .dt-layout-row {
    margin: 0px !important;
}

#part-detail-sourcing-results-box ::deep .table thead th {
    vertical-align: top;
}

::deep .part-watch-match {
    background-color: rgb(211, 211, 211);
}

#part-detail-sourcing-results-box ::deep .dt-empty {
    font-style: italic;
}

.section-box-button-group .form-error-summary {
    cursor: initial;
}

.section-box-button-group .section-error-summary {
    cursor: initial;
}


.section-box-button-group .button-line {
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    display: inline-flex !important;
}

.section-box-button-group {
    display: inline-flex;
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
}

#dialogContainer {
    position: absolute;
}

#dialogContainer .modal-body table tr td {
    padding: 0.5rem 1rem;
    word-wrap: break-word;
    word-break: break-word;
    vertical-align: top;
}

#dialogContainer .modal-footer p {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

#dialogContainer .modal-body table tr td span,
#dialogContainer .modal-footer {
    font-size: 11px;
}