﻿using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries.Dtos;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.BOMListForCustomerRequirement.Queries
{
    public class GetBOMListForCustomerRequirementHandler : IRequestHandler<GetBOMListForCustomerRequirementQuery, BaseResponse<IList<BOMListForCustomerRequirementDto>>>
    {
        private readonly IBaseRepository<Core.Domain.Entities.BOMListForCustomerRequirement> _repository;
        private readonly IMapper _mapper;
        public GetBOMListForCustomerRequirementHandler(IBaseRepository<Core.Domain.Entities.BOMListForCustomerRequirement> repository, IMapper mapper)
        {
            _mapper = mapper;
            _repository = repository;
        }

        public async Task<BaseResponse<IList<BOMListForCustomerRequirementDto>>> Handle(GetBOMListForCustomerRequirementQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<IList<BOMListForCustomerRequirementDto>>();
            SqlParameter[] parameters =
            [
                new SqlParameter("@BOMNo", SqlDbType.Int) { Value = request.BOMNo },
                new SqlParameter("@ClientID", SqlDbType.Int) { Value = request.ClientID },
                new SqlParameter("@IsPoHub", SqlDbType.Bit) { Value = request.IsPoHub }
            ];
            var result = await _repository.SqlQueryRawAsync(
                    @$"{StoredProcedures.SelectAll_CustomerRequirement_for_BOM} @BOMNo, @ClientID, @IsPoHub",
                    parameters
                );

            response.Data = _mapper.Map<IList<BOMListForCustomerRequirementDto>>(result);
            response.Success = true;
            return response;
        }
    }
}