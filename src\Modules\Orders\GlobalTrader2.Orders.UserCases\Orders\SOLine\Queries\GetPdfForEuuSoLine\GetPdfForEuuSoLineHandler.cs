﻿using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Dto.Manufacturers.Document;


namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetPdfForEuuSoLine
{
    public class GetPdfForEuuSoLineHandler : IRequestHandler<GetPdfForEuuSoLineQuery, BaseResponse<IEnumerable<DocumentDto>>>
    {
        private readonly IBaseRepository<SoLineEuuPdf> _soLineEuuPdfRepository;
        private readonly IMapper _mapper;

        public GetPdfForEuuSoLineHandler(IBaseRepository<SoLineEuuPdf> soLineEuuPdfRepository, IMapper mapper)
        {
            _soLineEuuPdfRepository = soLineEuuPdfRepository;
            _mapper = mapper;
        }

        //usp_selectAll_PDF_for_EUUSOLine
        public async Task<BaseResponse<IEnumerable<DocumentDto>>> Handle(GetPdfForEuuSoLineQuery request, CancellationToken cancellationToken)
        {
            var result = await _soLineEuuPdfRepository.ListAsync(
                filter: x => (x.SoLineNo == request.SalesOrderLineId && x.FileType == request.FileType),
                orderBy: x => x.OrderBy(y => y.SoLineEuuPdfId),
                includeProperties: x => x.Login);

            return new()
            {
                Success = true,
                Data = _mapper.Map<IEnumerable<DocumentDto>>(result, opt => opt.Items["culture"] = request.Culture),
            };
        }
    }
}