using GlobalTrader2.Aggregator.UseCases.DataList.DropDownProductSource;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using Moq;
using System.Linq.Expressions;

namespace GlobalTrader2.Aggregator.Test.DataList.DropDownProductSource
{
    public class DropDownProductSourceHandlerTest
    {
        private readonly Mock<IBaseRepository<ProductSource>> _repositoryMock;
        private readonly DropDownProductSourceHandler _handler;

        public DropDownProductSourceHandlerTest()
        {
            _repositoryMock = new Mock<IBaseRepository<ProductSource>>();
            _handler = new DropDownProductSourceHandler(_repositoryMock.Object);
        }

        [Fact]
        public async Task DropDownProductSourceHandler_WhenProductSourcesExist_ReturnsSuccessWithOrderedData()
        {
            // Arrange
            var query = new DropDownProductSourceQuery();
            var productSources = new List<ProductSource>
            {
                new ProductSource { ProductSourceId = 2, Name = "Source B", DisplaySortOrder = 2 },
                new ProductSource { ProductSourceId = 1, Name = "Source A", DisplaySortOrder = 1 },
                new ProductSource { ProductSourceId = 3, Name = "Source C", DisplaySortOrder = 3 }
            };

            _repositoryMock
                .Setup(r => r.ListAsync(
                    It.IsAny<Expression<Func<ProductSource, bool>>>(),
                    It.IsAny<Func<IQueryable<ProductSource>, IOrderedQueryable<ProductSource>>>(),
                    It.IsAny<Expression<Func<ProductSource, object?>>[]>()))
                .ReturnsAsync(productSources.OrderBy(ps => ps.DisplaySortOrder).ToList());

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(3, result.Data.Count);

            // Verify ordering by DisplaySortOrder
            Assert.Equal(1, result.Data[0].Id);
            Assert.Equal("Source A", result.Data[0].Name);
            Assert.Equal(2, result.Data[1].Id);
            Assert.Equal("Source B", result.Data[1].Name);
            Assert.Equal(3, result.Data[2].Id);
            Assert.Equal("Source C", result.Data[2].Name);
            Assert.Equal(3, result.Data.Count);

            _repositoryMock.Verify(
                r => r.ListAsync(
                    It.IsAny<Expression<Func<ProductSource, bool>>>(),
                    It.IsAny<Func<IQueryable<ProductSource>, IOrderedQueryable<ProductSource>>>(),
                    It.IsAny<Expression<Func<ProductSource, object?>>[]>()),
                Times.Once);
        }

        [Fact]
        public async Task DropDownProductSourceHandler_WhenNoProductSources_ReturnsSuccessWithEmptyList()
        {
            // Arrange
            var query = new DropDownProductSourceQuery();
            var productSources = new List<ProductSource>();

            _repositoryMock
                .Setup(r => r.ListAsync(
                    It.IsAny<Expression<Func<ProductSource, bool>>>(),
                    It.IsAny<Func<IQueryable<ProductSource>, IOrderedQueryable<ProductSource>>>(),
                    It.IsAny<Expression<Func<ProductSource, object?>>[]>()))
                .ReturnsAsync(productSources);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Empty(result.Data);
        }

        [Fact]
        public async Task DropDownProductSourceHandler_WhenRepositoryThrowsException_ThrowsException()
        {
            // Arrange
            var query = new DropDownProductSourceQuery();

            _repositoryMock
                .Setup(r => r.ListAsync(
                    It.IsAny<Expression<Func<ProductSource, bool>>>(),
                    It.IsAny<Func<IQueryable<ProductSource>, IOrderedQueryable<ProductSource>>>(),
                    It.IsAny<Expression<Func<ProductSource, object?>>[]>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));
        }
    }
}
