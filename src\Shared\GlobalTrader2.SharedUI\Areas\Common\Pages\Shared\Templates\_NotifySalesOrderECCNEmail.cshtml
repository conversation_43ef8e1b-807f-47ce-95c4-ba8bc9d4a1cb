﻿@using GlobalTrader2.Dto.Templates
@using GlobalTrader2.SharedUI.Helper
@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor ContextAccessor
@model NotifySalesOrderECCNTemplate
@{

    var urlBase = $"{ContextAccessor.HttpContext?.Request.Scheme}://{ContextAccessor.HttpContext?.Request.Host}";
    string hrefUrl = $"{urlBase}{RedirectUrlHelper.GetUrlWithParams_SalesOrder(Model.SalesOrderNo)}";
}
<b>SO Number :</b> <a href="@hrefUrl">@Model.SalesOrderNumber</a><br><b>Part No :</b> @Model.Part<br><b>ECCN Code :</b> @Model.ECCNCode<br><b>Warning Message :</b> @Html.Raw(Model.WarningMessage)<br><b>ECCN Message :</b> @Html.Raw(Model.ECCNMessage)<br>