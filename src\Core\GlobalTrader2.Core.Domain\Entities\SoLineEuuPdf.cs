﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Core.Domain.Entities
{
    [Table("tbSOLineEUUPDF")]
    public class SoLineEuuPdf
    {
        public int SoLineEuuPdfId { get; set; }
        public int SoLineNo { get; set; }
        public string? Caption { get; set; }
        public string? FileName { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime? DLUP { get; set; }
        public string? FileType { get; set; }

        [ForeignKey("UpdatedBy")]
        public Login? Login { get; set; }
    }
}
