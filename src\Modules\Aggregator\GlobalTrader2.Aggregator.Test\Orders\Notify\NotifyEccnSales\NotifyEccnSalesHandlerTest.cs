using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyEccnSales;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyECCNMessageSO;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.GetMailGroupNoByName;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Enums;
using MediatR;
using Moq;
using System.Linq.Expressions;
using GlobalTrader2.Core;

namespace GlobalTrader2.Aggregator.Test.Orders.Notify.NotifyEccnSales
{
    public class NotifyEccnSalesHandlerTest
    {
        private readonly Mock<ISender> _senderMock;
        private readonly Mock<IBaseRepository<Core.Domain.Entities.SalesOrder>> _salesOrderRepositoryMock;
        private readonly NotifyEccnSalesHandler _handler;

        public NotifyEccnSalesHandlerTest()
        {
            _senderMock = new Mock<ISender>();
            _salesOrderRepositoryMock = new Mock<IBaseRepository<Core.Domain.Entities.SalesOrder>>();
            _handler = new NotifyEccnSalesHandler(_senderMock.Object, _salesOrderRepositoryMock.Object);
        }

        [Fact]
        public async Task NotifyEccnSalesHandler_WhenValidRequest_SendsNotificationAndReturnsSuccess()
        {
            // Arrange
            var command = new NotifyEccnSalesCommand(
                SalesOrderId: 123,
                SalesOrderLineId: 456,
                LoginId: 789,
                ClientId: 101,
                LoginEmail: "<EMAIL>",
                LoginName: "Test User");

            var eccnMailGroupNo = 555;
            var supportTeamMemberNo = 666;

            var salesOrders = new List<Core.Domain.Entities.SalesOrder>
            {
                new Core.Domain.Entities.SalesOrder 
                { 
                    SalesOrderId = 123,
                    ClientNo = 101,
                    SupportTeamMemberNo = supportTeamMemberNo
                }
            };

            _senderMock
                .Setup(s => s.Send(It.IsAny<GetMailGroupNoByNameQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(eccnMailGroupNo);

            _salesOrderRepositoryMock
                .Setup(r => r.ListAsync(
                    It.IsAny<Expression<Func<Core.Domain.Entities.SalesOrder, bool>>>(),
                    It.IsAny<Func<IQueryable<Core.Domain.Entities.SalesOrder>, IOrderedQueryable<Core.Domain.Entities.SalesOrder>>>(),
                    It.IsAny<Expression<Func<Core.Domain.Entities.SalesOrder, object?>>[]>()))
                .ReturnsAsync(salesOrders);

            _senderMock
                .Setup(s => s.Send(It.IsAny<NotifyECCNMessageSOCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<bool> { Success = true, Data = true });

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.True(result.Data);

            _senderMock.Verify(
                s => s.Send(It.Is<GetMailGroupNoByNameQuery>(q => 
                    q.ClientNo == command.ClientId && 
                    q.Name == Core.Constants.MailGroup.ECCNRestricted), 
                    It.IsAny<CancellationToken>()),
                Times.Once);

            _senderMock.Verify(
                s => s.Send(It.Is<NotifyECCNMessageSOCommand>(cmd =>
                    cmd.SalesOrderLineId == command.SalesOrderLineId &&
                    cmd.LoginId == command.LoginId &&
                    cmd.SenderEmail == command.LoginEmail &&
                    cmd.SenderName == command.LoginName &&
                    cmd.Recipient.Any(r => r.Value == eccnMailGroupNo && r.Type == (int)MailMessageAddressType.Group) &&
                    cmd.Recipient.Any(r => r.Value == supportTeamMemberNo && r.Type == (int)MailMessageAddressType.Individual) &&
                    cmd.Recipient.Any(r => r.Value == command.LoginId && r.Type == (int)MailMessageAddressType.Individual)),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task NotifyEccnSalesHandler_WhenNoSupportTeamMember_SendsNotificationWithoutSupportTeam()
        {
            // Arrange
            var command = new NotifyEccnSalesCommand(
                SalesOrderId: 123,
                SalesOrderLineId: 456,
                LoginId: 789,
                ClientId: 101,
                LoginEmail: "<EMAIL>",
                LoginName: "Test User");

            var eccnMailGroupNo = 555;

            var salesOrders = new List<Core.Domain.Entities.SalesOrder>
            {
                new Core.Domain.Entities.SalesOrder 
                { 
                    SalesOrderId = 123,
                    ClientNo = 101,
                    SupportTeamMemberNo = null
                }
            };

            _senderMock
                .Setup(s => s.Send(It.IsAny<GetMailGroupNoByNameQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(eccnMailGroupNo);

            _salesOrderRepositoryMock
                .Setup(r => r.ListAsync(
                    It.IsAny<Expression<Func<Core.Domain.Entities.SalesOrder, bool>>>(),
                    It.IsAny<Func<IQueryable<Core.Domain.Entities.SalesOrder>, IOrderedQueryable<Core.Domain.Entities.SalesOrder>>>(),
                    It.IsAny<Expression<Func<Core.Domain.Entities.SalesOrder, object?>>[]>()))
                .ReturnsAsync(salesOrders);

            _senderMock
                .Setup(s => s.Send(It.IsAny<NotifyECCNMessageSOCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<bool> { Success = true, Data = true });

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.True(result.Data);

            // Verify only 2 recipients (mail group and login user, no support team)
            _senderMock.Verify(
                s => s.Send(It.Is<NotifyECCNMessageSOCommand>(cmd =>
                    cmd.Recipient.Count == 2 &&
                    cmd.Recipient.Any(r => r.Value == eccnMailGroupNo && r.Type == (int)MailMessageAddressType.Group) &&
                    cmd.Recipient.Any(r => r.Value == command.LoginId && r.Type == (int)MailMessageAddressType.Individual) &&
                    !cmd.Recipient.Any(r => r.Type == (int)MailMessageAddressType.Individual && r.Value != command.LoginId)),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task NotifyEccnSalesHandler_WhenRepositoryThrowsException_ThrowsException()
        {
            // Arrange
            var command = new NotifyEccnSalesCommand(
                SalesOrderId: 123,
                SalesOrderLineId: 456,
                LoginId: 789,
                ClientId: 101,
                LoginEmail: "<EMAIL>",
                LoginName: "Test User");

            _senderMock
                .Setup(s => s.Send(It.IsAny<GetMailGroupNoByNameQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(555);

            _salesOrderRepositoryMock
                .Setup(r => r.ListAsync(
                    It.IsAny<Expression<Func<Core.Domain.Entities.SalesOrder, bool>>>(),
                    It.IsAny<Func<IQueryable<Core.Domain.Entities.SalesOrder>, IOrderedQueryable<Core.Domain.Entities.SalesOrder>>>(),
                    It.IsAny<Expression<Func<Core.Domain.Entities.SalesOrder, object?>>[]>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
        }

        [Fact]
        public async Task NotifyEccnSalesHandler_WhenGetMailGroupFails_ThrowsException()
        {
            // Arrange
            var command = new NotifyEccnSalesCommand(
                SalesOrderId: 123,
                SalesOrderLineId: 456,
                LoginId: 789,
                ClientId: 101,
                LoginEmail: "<EMAIL>",
                LoginName: "Test User");

            _senderMock
                .Setup(s => s.Send(It.IsAny<GetMailGroupNoByNameQuery>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new Exception("Mail group not found"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
        }
    }
}
