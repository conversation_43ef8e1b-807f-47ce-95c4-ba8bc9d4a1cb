﻿import { TableFilterComponent } from '../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#'
import { InputElement } from '../../../components/table-filter/models/input-element.model.js?v=#{BuildVersion}#'

InputElement.prototype.createWrapper = function (id, label, requiredCheckbox) {
    return `
            <div class="col-12 form-control-wrapper">
                <div class="row g-3 align-items-center">
                    <div class="col-3">
                        <label for="${id}" class="form-label">${label}</label>
                    </div>
                    <div class="col-8">
                        <div class="d-flex gap-1" id="${id}ElementContent">
                            ${requiredCheckbox ? '<span></span> <!-- Placeholder for checkbox -->' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
}
export class HUBRFQTalbeFilterComponent extends TableFilterComponent {
    static ViewState = {
        VISIBLE: 'visible',
        INVISIBLE: 'invisible',
        NO_DISPLAY_BUT_APPLIED: 'noDisplayButApplied'
    };
    constructor(container, title, options) {
        super(container, title, options);
        this.ViewState = this.constructor.ViewState;
        this.state = this.ViewState.VISIBLE;
    }

    setState(newState) {
        this.state = newState;
    }

    getContainer(inputConfig, isTemplate) {
        return this.container.find(`.filter-form .${inputConfig.locatedInContainerByClass}`);
    }

    generateLayout() {
        return `
            <div class="filter-section-wrapper mb-10px d-none filter-section-line-1">
                <!-- Filter result title -->
                <div class="d-flex justify-content-between">
                    <h5 class="d-flex m-0 align-items-center filter-title"></h5>
                </div>
                <div class="line my-2"></div>

                <!-- Filter result form -->
                <div class="row common-form filter-form">
                <div class="col-6 px-0 filter-column-1 ">
                </div>

                <div class="col-6 px-0 filter-column-2">
                </div>
                </div>

                <div class="line my-2"></div>

                <!-- Filter result buttons -->

                <span class="d-flex gap-2 align-items-center">
                    <button class="btn btn-danger off-table-filter">
                        <img src="/img/icons/slash.svg" alt="off" width="18" height="18">
                        <span class="lh-base">${commonLocalize.Off}</span>
                    </button>
                    <button class="btn btn-primary hide-table-filter" >
                        <span class="lh-base">${commonLocalize.Hide}</span>
                    </button>
                    <button class="btn btn-primary reset-table-filter" >
                        <img src="/img/icons/reset.svg" alt="reset" width="18" height="18">
                        <span class="lh-base">${commonLocalize.Reset}</span>
                    </button>
                    <button class="btn btn-primary apply-table-filter">
                        <img src="/img/icons/check.svg" alt="apply" width="18" height="18">
                        <span class="lh-base">${commonLocalize.Apply}</span>
                    </button>
                    <button class="btn btn-danger cancel-table-filter" style="display: none;">
                        <img src="/img/icons/slash.svg" alt="cancel" width="18" height="18">
                        <span class="lh-base">${commonLocalize.Cancel}</span>
                    </button>
                </span>
            </div>
            <div class="filter-section-wrapper mb-10px filter-section-line-2" style="display: none;">
                <div class="d-flex gap-2 align-items-center">
                    <span class="d-flex gap-2 align-items-center">
                    <button class="btn btn-outline-primary show-table-filter">
                        <span class="lh-base">${commonLocalize.Show}</span>
                    </button>
                    <p class="m-0" name="count-filter-applied">
                    </p>
                    <span>
                </div>
              </div>
        `
    }

    // override: new view state apply show filter button and get filterValues
    async setupEventListeners() {
        this.container.find(".apply-table-filter").on("click", (e) => {
            e.preventDefault();
            this.setState(this.ViewState.NO_DISPLAY_BUT_APPLIED);
            this.toggleFilterButtons(true);
        });

        //hide button event
        this.container.find(".hide-table-filter").on("click", (e) => {
            e.preventDefault();
            this.hide();
            this.setState(this.ViewState.NO_DISPLAY_BUT_APPLIED);
            this.toggleFilterButtons(true);
        });

        // Off button event
        this.container.find(".off-table-filter").on("click", (e) => {
            e.preventDefault();
            this.setState(this.ViewState.INVISIBLE);
            this.toggleFilterButtons(true);
        });

        super.setupEventListeners();
    }

    // new: toggle show filter button
    toggleFilterButtons(show) {
        this.container.find('.show-table-filter').toggle(show);
    }

    // override: remove inputs condition to get data
    getAppliedValues() {
        let filterValues = {
        };

        if (!this.isVisible()) {
            return filterValues;
        }
        this.inputs.forEach(instance => {
            if (instance.isChecked()) {

                filterValues[instance.name] = {
                    ...instance.getValue(),
                    isOn: instance.isChecked(),
                    isShown: instance.isChecked()
                };
            }
        })

        return filterValues;
    }

    // override: apply new check visible with view state
    isVisible() {
        return this.state === this.ViewState.VISIBLE || this.state === this.ViewState.NO_DISPLAY_BUT_APPLIED;
    }

    // override: change display for new layout
    display(show) {
        if (show) {
            this.container.find('.filter-section-line-1').show();
            this.container.find('.filter-section-line-2').hide();
            this.setState(this.ViewState.VISIBLE);
        } else {
            this.container.find('.filter-section-line-2').show();
            this.container.find('.filter-section-line-1').hide();
            this.setState(this.ViewState.INVISIBLE);
        }
    }

    // override: change display for new layout
    show() {
        this.container.find('.filter-section-line-1').show();
        this.container.find('.filter-section-line-2').hide();
        this.setState(this.ViewState.VISIBLE);
    }

    // override: change display for new layout
    hide() {
        this.container.find('.filter-section-line-2').show();
        this.container.find('.filter-section-line-1').hide();
        this.setState(this.ViewState.NO_DISPLAY_BUT_APPLIED);
    }
}