@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject SessionManager _sessionManager

@{
    var clientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty;
}

<!-- Magic Box Dialog -->
<div id="dialogContainer" class="fixed-dialog fs-12" style="display: none">
    <div class="modal-content">
        <div class="modal-header">
            <div class="d-flex justify-content-center flex-grow-1">
                <h5 class="modal-title" id="exampleModalLabel">@_localizer["Part Number"]: <span type="readonlyText"
                        data-bind-name="partNoMagicBox"></span></h5>
            </div>
            <button id="closeDialogBtn" type="button" class="btn-close"
                    aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <table class="w-100">
                <tr>
                    <th class="w-50"></th>
                    <th class="w-50"></th>
                </tr>
                <tr>
                    <td>@_localizer["Sold to Customer"]: <span type="readonlyText"
                            data-bind-name="lastSoldtoCustomer"></span></td>
                    <td class="bg-white">@_localizer["Current Customer"]: <span type="readonlyText"
                            data-bind-name="companyName"></span></td>
                </tr>
                <tr>
                    <td>@_localizer["Sale Price"]: <span type="readonlyText"
                            data-bind-name="lastPricePaidByCustDisplay"></span></td>
                    <td class="bg-white">@_localizer["Sale Price"]: <span type="readonlyText"
                            data-bind-name="custLastAvgReboundPriceSoldDisplay"></span></td>
                </tr>
                <tr>
                    <td>@_localizer["Sale Date"]: <span type="readonlyText" data-bind-name="lastSoldOn"></span></td>
                    <td class="bg-white">@_localizer["Sale Date"]: <span type="readonlyText"
                                                        data-bind-name="custLastSoldOn"></span>
                    </td>
                </tr>
                <tr>
                    <td>@_localizer["Quantity"]: <span type="readonlyText" data-bind-name="lastQuantity"></span></td>
                    <td class="bg-white">@_localizer["Quantity"]: <span type="readonlyText"
                            data-bind-name="custQuantity"></span></td>
                </tr>
                <tr>
                    <td>@_localizer["Supplier Type"]: <span type="readonlyText"
                            data-bind-name="lastSupplierType"></span></td>
                    <td class="bg-white">@_localizer["Supplier Type"]: <span type="readonlyText"
                            data-bind-name="custSupplierType"></span></td>
                </tr>
                <tr>
                    <td>@_localizer["Date Code"]: <span type="readonlyText" data-bind-name="lastDatecode"></span></td>
                    <td class="bg-white">@_localizer["Date Code"]: <span type="readonlyText"
                            data-bind-name="custDatecode"></span></td>
                </tr>
                <tr>
                    <td>@_localizer["Date Purchased"]: <span type="readonlyText"
                            data-bind-name="lastDatePurchased"></span></td>
                    <td class="bg-white">@_localizer["Date Purchased"]: <span type="readonlyText"
                            data-bind-name="custDatePurchased"></span></td>
                </tr>
                <tr>
                    <td>@_localizer["Customer Region"]: <span type="readonlyText"
                            data-bind-name="lastCustomerRegion"></span></td>
                    <td class="bg-white">@_localizer["Customer Region"]: <span type="readonlyText"
                            data-bind-name="customerRegion"></span></td>
                </tr>
            </table>
        </div>
        <div class="modal-footer d-block">
            <p>@_localizer["12 months Average Rebound Re-Sale Price"]: <span type="readonlyText"
                    data-bind-name="lastAverageReboundPriceSold"></span> @clientCurrencyCode</p>
            <p>@_localizer["Lowest Sale Price In Last 12 Months"]: <span type="readonlyText"
                    data-bind-name="bestLastPricePaid12"></span> @clientCurrencyCode</p>
            <p>@_localizer["Best Buy Price In Last 12 Months"]: <span type="readonlyText"
                    data-bind-name="clientBestPricePaid12"></span> @clientCurrencyCode</p>
        </div>
    </div>
</div>


