﻿using GlobalTrader2.Dto.MailGroupMembers;

namespace GlobalTrader2.Settings.UseCases.CompanySettings.MailGroupMembers.Queries;
public class GetMailGroupMembersHandler : IRequestHandler<GetMailGroupMembersQuery, BaseResponse<List<MailGroupMemberDto>>>
{
    private readonly IBaseRepository<MailGroupMember> _repo;

    public GetMailGroupMembersHandler(IBaseRepository<MailGroupMember> repo)
    {
        _repo = repo;
    }

    public async Task<BaseResponse<List<MailGroupMemberDto>>> Handle(GetMailGroupMembersQuery request, CancellationToken cancellationToken)
    {
        var response = new BaseResponse<List<MailGroupMemberDto>>();

        var members = await _repo.ListAsync(
            filter: mm => mm.MailGroupNo == request.MailGroupId && !mm.Login!.Inactive,
            includeProperties: q => q.Login,
            orderBy: q => q.OrderBy(mm => mm.Login!.FirstName).ThenBy(mm=> mm.Login!.LastName)
        );

        response.Data = members.Select(mm => new MailGroupMemberDto
        {
            MailGroupMemberId = mm.MailGroupMemberId,
            LoginNo = mm.LoginNo,
            EmployeeName = mm.Login!.EmployeeName,
        }).ToList();
        response.Success = true;

        return response;
    }
}
