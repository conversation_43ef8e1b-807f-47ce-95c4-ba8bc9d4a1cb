﻿@page

@using GlobalTrader2.Core.Enums
@using GlobalTrader2.Core.Helpers
@using GlobalTrader2.Dto.DataListNugget
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.DocumentsSection
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LeftNugget
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.CompanyQuickBrowse
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.QuickJump
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SelectionMenu
@using GlobalTrader2.SharedUI.Constants
@using GlobalTrader2.SharedUI.Enums
@using GlobalTrader2.SharedUI.Helper
@using GlobalTrader2.SharedUI.Services
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@using System.Text.Json

@model GlobalTrader2.Contacts.UI.Areas.Contact.Pages.AllCompanies.Details.IndexModel
@inject Microsoft.AspNetCore.Antiforgery.IAntiforgery Antiforgery
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject SessionManager _sessionManager
@inject SecurityManager _securityManager
@inject SettingManager _settingManager

@{
    ViewData["TodoDialogTittle"] = _commonLocalizer["Prospects Qualification"];
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
    <link type="text/css" rel="stylesheet" href="~/css/star-rating.css" asp-append-version="true" />
}

@section LeftSidebar {
    @await Component.InvokeAsync(nameof(LeftNugget), new LeftNuggetProps()
    {
        IsInitiallyExpanded = true,
        Item = SideBar.QuickJump,
        ChildComponent = nameof(QuickJump),
        ChildComponentParameters = new { options = QuickJumpOptions.ContactQuickJumpOptions }
    })

    @await Component.InvokeAsync(nameof(LeftNugget),
                          new LeftNuggetProps()
                  {
                      IsInitiallyExpanded = false,
                      Item = SideBar.QuickBrowse,
                      ChildComponent = nameof(CompanyQuickBrowse),
                              ChildComponentParameters = new { 
                                  companyListType = Model.CompanyListTypeParams
                              }
                  })

    @await Component.InvokeAsync(nameof(LeftNugget), new LeftNuggetProps()
    {
        IsInitiallyExpanded = false,
        Item = SideBar.Selection,
        ChildComponent = nameof(SelectionMenu),
        ChildComponentParameters = new { selectionType = SelectionType.Contact }
    })
}

@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();

    var company = Model.CompanyDetails.CompanyInfo;
    var isCompanyInactive = Model.CompanyDetails.CompanyInfo.Inactive;
    var pageTitle = $"{company.CompanyName} ({company.CompanyId}) {company.CreditStatus}";
    var pageTitleColor = "";
    if (company.ERAIReported)
    {
        pageTitleColor = "text-red fw-bold";
    }
    else if (company.Inactive)
    {
        pageTitleColor = "text-gray";
    }

    ViewData["Title"] = pageTitle;

    var CanAddCompany = _securityManager.CheckSectionPermission(GlobalTrader2.SharedUI.Enums.SecurityFunction.Contact_Company_Add);
}

<div class="page-content-container mh-1500">
    <div class="d-flex justify-content-between mt-2">
        <h4 class="page-primary-title @pageTitleColor" id="company-detail-primary-title">
            <span id="company-name-primary-title">@company.CompanyName</span> (@company.CompanyId) @company.CreditStatus
        </h4>
        <div class="d-flex gap-2">
            @if (CanAddCompany)
            {
                <a href="/Contact/AllCompanies/AddCompany" class="btn btn-primary">
                    <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                    <span class="lh-base text-nowrap">@_commonLocalizer["Add New Company"]</span>
                </a>
            }
        </div>
    </div>

    <div id="company-sub-title">
        @{
            var showCompanyDefaultBillingAddress = Model.CompanyDefaultBillingAddress is not null ? "" : "d-none";
        }
        <div class="row m-0 mb-1 @showCompanyDefaultBillingAddress">
            <div class="col-12 p-0 fs-11 text-primary-bold" id="company-default-billing-address">
                @Html.Raw(Model.CompanyDefaultBillingAddress)
            </div>
        </div>

        @{
            var showPartOf = string.IsNullOrEmpty(Model.CompanyDetails.ParentCompanyName) ? "d-none" : "";
        }
        <div id="parent-company-wrapper" class="row m-0 mb-1 @showPartOf">
            <div class="col-12 p-0 fs-11 text-primary-bold">
                <span class="fw-bold">@_commonLocalizer["Part Of"]</span> <a class="dt-hyper-link"
                href="/Contact/AllCompanies/Details?cm=@Model.CompanyDetails.CompanyInfo.ParentCompanyNo&clt=0">@Model.CompanyDetails.ParentCompanyName</a>
            </div>
        </div>

        @{
            var showCompanyTypeName = string.IsNullOrEmpty(Model.CompanyDetails.CompanyTypeName) ? "d-none" : "";
        }
        <div id="company-type-wrapper" class="row m-0 mb-1 @showCompanyTypeName">
            <div class="col-12 p-0 fs-11 text-primary-bold">
                <span class="fw-bold">@_commonLocalizer["Company Type"]</span>
                <span class="type-name">@Model.CompanyDetails.CompanyTypeName</span>
            </div>
        </div>

        @{
            var showInsuranceFileNo = string.IsNullOrEmpty(Model.InsuranceFileNo) ? "d-none" : "";
        }
        <div class="row m-0 mb-1 @showInsuranceFileNo" id="insurance-subtitle">
            <div class="col-12 p-0 fs-11 text-primary-bold">
                <span class="fw-bold">@_commonLocalizer["Insurance File No"]:</span>
                <span id="insurance-file-no-subtitle">@Model.CompanyDetails.CompanyInfo.InsuranceFileNo</span>
                <span class="fw-bold">@_commonLocalizer["Insured Amount"]:</span>
                <span id="insured-amount-subtitle">@Model.FormatedInsuredAmount</span>
            </div>
        </div>

        <div id="star-ratting-wrapper" class="row m-0">
            <div class="col-12 p-0 fs-11 d-flex gap-1">
                @{
                    var showApprovedCustomer = Model.CompanyDetails.CompanyInfo.SOApproved ? "" : "d-none";
                }
                <div id="approved-customer-wrapper" class="d-flex gap-1 mb-1 align-items-center @showApprovedCustomer">
                    <div class="fw-bold text-primary-bold">@_commonLocalizer["Approved Customer"]:</div>
                    <div class="d-flex star-rating" id="so-rating-title">
                        @for (int i = 0; i < Model.CompanyDetails.CompanyInfo.SORating; i++)
                        {
                            <span class="star fs-12">&#9733;</span>
                        }
                        @for (int i = 0; i < 5 - Model.CompanyDetails.CompanyInfo.SORating; i++)
                        {
                            <span class="star fs-12 empty">&#9733;</span>
                        }
                    </div>
                </div>

                @{
                    var showApprovedSupplier = Model.CompanyDetails.CompanyInfo.POApproved ? "" : "d-none";
                }
                <div id="approved-supplier-wrapper" class="d-flex gap-1 mb-1 align-items-center @showApprovedSupplier">
                    <div class="fw-bold text-primary-bold">@_commonLocalizer["Approved Supplier"]:</div>
                    <div class="d-flex star-rating" id="po-rating-title">
                        @for (int i = 0; i < Model.CompanyDetails.CompanyInfo.PORating; i++)
                        {
                            <span class="star fs-12">&#9733;</span>
                        }
                        @for (int i = 0; i < 5 - Model.CompanyDetails.CompanyInfo.PORating; i++)
                        {
                            <span class="star fs-12 empty">&#9733;</span>
                        }
                    </div>
                </div>
            </div>
        </div>

        @{
            var showImportantNotes = string.IsNullOrEmpty(Model.CompanyDetails.CompanyInfo.ImportantNotes) ? "d-none" : "";
        }
        <div id="important-notes-wrapper" class="row m-0 mb-1 @showImportantNotes">
            <div class="col-12 p-0 fs-11 text-primary-bold text-break">
                @{
                    var importantNotes = Functions.ReplaceLineBreaks(Model.CompanyDetails.CompanyInfo.ImportantNotes ?? "");
                    importantNotes = Functions.SetCleanTextValue(importantNotes, isReplaceLineBreaks: false);
                }
                @Html.Raw(importantNotes)
            </div>
        </div>

        @{
            var showIsInactive = Model.CompanyDetails.CompanyInfo.Inactive ? "" : "d-none";
        }
        <div class="row m-0 mb-1 mt-1">
            <div class="col-12 p-0 fs-11 text-primary-bold">
                <div id="company-inactive" class="d-inline-block text-white bg-gray p-1 fw-bold border ml-1 @showIsInactive">
                    @_commonLocalizer["This Company is inactive"]
                </div>
            </div>
        </div>

        <div class="row m-0 mb-1 ">
            <div class="col-12 p-0 fs-11 text-primary-bold">
                @{
                    var showOnStop = Model.CompanyDetails.CompanyInfo.OnStop ? "" : "d-none";
                }
                <div id="company-onStop" class="d-inline-block text-white bg-red p-1 fw-bold border ml-1 @showOnStop">
                    @_commonLocalizer["This Company is On Stop"]
                </div>

                @{
                    var showIsSanctioned = Model.CompanyDetails.CompanyInfo.IsSanctioned ? "" : "d-none";
                }
                <div id="isSanctioned" class="d-inline-block text-white bg-red p-1 fw-bold border @showIsSanctioned">
                    @_commonLocalizer["This Company is Sanctioned"]
                </div>
            </div>
        </div>

        @{
            var showClientName = Model.CompanyDetails.CompanyInfo.ClientNo != _sessionManager.ClientID ? "" : "d-none";
        }
        <div class="row m-0 mb-1 mt-1 @showClientName">
            <div class="col-12 p-0">
                <span class="fs-12 bg-yellow fw-bold">@Model.ClientName</span>
            </div>
        </div>

        @{
            var showIsPremierCustomer = Model.CompanyDetails.CompanyInfo.IsPremierCustomer ? "" : "d-none";
        }
        <div id="is-premier-customer" class="row m-0 mb-1 @showIsPremierCustomer">
            <div class="col-12 p-0 fs-11 text-primary-bold">
                <img src="~/img/hazardous/premier_icon.png" alt="Premier Customer" />
            </div>
        </div>

        @{
            var showIsTier2PremierCustomer = Model.CompanyDetails.CompanyInfo.IsTier2PremierCustomer ? "" : "d-none";
        }
        <div id="is-tier-2-premier-customer" class="row m-0 mb-1 @showIsTier2PremierCustomer">
            <div class="col-12 p-0 fs-11 text-primary-bold">
                <img src="~/img/hazardous/tier_customer_icon.png" alt="Premier Customer" />
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-between align-items-end border-bottom">
        <div class="d-flex justify-content-center align-items-center" style="height:40px;">
        </div>
        <div id="nav-tabs-wrapper" role="tablist">
            <ul class="nav nav-tabs border-0 justify-content-end" id="company-details-tabs">
                @{
                    void RenderTab(CompanyDetailTab tab, int currentTab)
                    {
                        var tabName = tab.ToString().ToLower();
                        bool ariaSelectedValue = currentTab == (int)tab;
                        <li class="nav-item">
                            @if (ariaSelectedValue)
                            {
                                <button class="nav-link @(currentTab == (int)tab ? "active" : "")" id="@($"{tabName}-tab")" data-bs-toggle="tab" data-bs-target="@($"#{tabName}")" type="button" role="tab" aria-controls="@($"{tabName}")" aria-selected="true" data-view-level="@((int)tab)">@_localizer[$"{tabName}-tab"]</button>
                            }
                            else{
                                <button class="nav-link @(currentTab == (int)tab ? "active" : "")" id="@($"{tabName}-tab")" data-bs-toggle="tab" data-bs-target="@($"#{tabName}")" type="button" role="tab" aria-controls="@($"{tabName}")" aria-selected="false" data-view-level="@((int)tab)">@_localizer[$"{tabName}-tab"]</button>
                            }
                        </li>
                    }
                    if (Model.AllowedTabs != null)
                    {
                        foreach (var tab in Model.AllowedTabs)
                        {
                            RenderTab(tab, Model.TabQueryParam);
                        }
                    }
                }
            </ul>
        </div>
    </div>
    <div class="tab-content mt-3">
        <div class="tab-pane fade @(Model.TabQueryParam == (int)CompanyDetailTab.Main ? "show active" : "")" id="main" role="tabpanel" aria-labelledby="main-tab">
            @{
                Model.CompanyDetails.InActive = isCompanyInactive;
                Model.CompanyDetails.IsReadOnly = Model.IsReadOnly;
            }
            @await Html.PartialAsync("MainInfo/_CompanyMainInfo.cshtml", Model.CompanyDetails)

            @{
                Model.ManufacturersSuppliedSectionViewModel.IsReadOnly = Model.IsReadOnly;
            }
            @await Html.PartialAsync("ManufacturersSupplied/_ManufacturersSupplied.cshtml", Model.ManufacturersSuppliedSectionViewModel)

            <div id="addresses-box" class="@sectionBoxClasses mb-3">
                <div class="@headerClasses">
                    <span class="section-box-title">
                        @_commonLocalizer["Addresses"]
                    </span>
                    <div class="section-box-button-group" style="display: none">
                        @if (!Model.IsReadOnly)
                        {
                            <div class="d-flex gap-2">
                                @if (Model.AddressViewModel.CanAdd.GetValueOrDefault())
                                {
                                    <button class="btn btn-primary" id="add-address-button" disabled="@isCompanyInactive">
                                        <img src="~/img/icons/plus.svg" alt="@_commonLocalizer["Add"]" />
                                        <span>@_commonLocalizer["Add"]</span>
                                    </button>
                                }
                                @if (Model.AddressViewModel.CanEdit.GetValueOrDefault())
                                {
                                    <button class="btn btn-primary" id="edit-address-button" disabled="@isCompanyInactive">
                                        <img src="~/img/icons/edit-3.svg" alt="Edit icon" width="18" height="18" />
                                        <span class="lh-base">@_commonLocalizer["Edit"]</span>
                                    </button>
                                }
                                @if (Model.AddressViewModel.CanCease.GetValueOrDefault())
                                {
                                    <button class="btn btn-danger" id="delete-address-btn" data-title-dialog="@_localizer["Cease Address"]" disabled="@isCompanyInactive"
                                    data-antiforgerytoken="@Antiforgery.GetAndStoreTokens(HttpContext).RequestToken">
                                        <img src="~/img/icons/x-circle.svg" alt="Delete icon" width="18" height="18" />
                                        <span class="lh-base">@_commonLocalizer["Delete"]</span>
                                    </button>
                                }
                                @if (Model.AddressViewModel.CanMakeDefault.GetValueOrDefault())
                                {
                                    <button class="btn btn-warning" id="make-default-billing-btn" data-title-dialog="@_localizer["Make Default Billing Address"]" disabled="@isCompanyInactive"
                                    data-antiforgerytoken="@Antiforgery.GetAndStoreTokens(HttpContext).RequestToken">
                                        <img src="~/img/icons/star.svg" alt="star icon" />
                                        <span>@_commonLocalizer["Make Default Billing"]</span>
                                    </button>
                                    <button class="btn btn-warning" id="make-default-shipping-btn" data-title-dialog="@_localizer["Make Default Shipping Address"]" disabled="@isCompanyInactive"
                                    data-antiforgerytoken="@Antiforgery.GetAndStoreTokens(HttpContext).RequestToken">
                                        <img src="~/img/icons/star.svg" alt="star icon" />
                                        <span>@_commonLocalizer["Make Default Shipping"]</span>
                                    </button>
                                }
                            </div>
                        }
                    </div>
                </div>
                <div class="@contentClasses" style="display: none">
                    <table id="company-address-table" class="table simple-table display responsive">
                        <tr>
                            <th></th>
                        </tr>
                    </table>
                    @await Html.PartialAsync("_CompanyAddressDetail", Model.AddressViewModel)
                </div>
            </div>  

            <div id="prospects-qualification-box" class="@sectionBoxClasses mb-3" data-is-inactive="@Model.CompanyDetails.CompanyInfo.Inactive">
                <h3 class="@headerClasses">
                    <span class="section-box-title">
                        @_commonLocalizer["Prospects Qualification"]
                    </span>
                    <span class="section-box-button-group" style="display: none">
                        @if (!Model.IsReadOnly)
                        {
                            <span class="d-flex gap-2">
                                <button class="btn btn-primary" id="add-todo-task" disabled="@isCompanyInactive">
                                    <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                                    <span class="lh-base">@_commonLocalizer["AddTask"]</span>
                                </button>
                                <button class="btn btn-primary" id="view-todo-task" disabled="@isCompanyInactive">
                                    <img src="~/img/icons/edit-3.svg" alt="Edit icon" width="18" height="18" />
                                    <span class="lh-base">@_commonLocalizer["ViewTask"]</span>
                                </button>
                                <button class="btn btn-primary" id="edit-prospects" disabled="@isCompanyInactive">
                                    <img src="~/img/icons/edit-3.svg" alt="Edit icon" width="18" height="18" />
                                    <span class="lh-base">@_commonLocalizer["Edit"]</span>
                                </button>
                            </span>
                        }
                    </span>
                </h3>
                <div class="@contentClasses" style="display: none">
                    @await Html.PartialAsync("_ProspectsQualification.cshtml")
                    @await Html.PartialAsync("_EditProspectsQualification.cshtml")
                </div>
            </div>

            @{
                Model.ContactLogSectionViewModel.IsReadOnly = Model.IsReadOnly;
            }
            @await Html.PartialAsync("Partials/ContactLog/_ContactLog", Model.ContactLogSectionViewModel)

            @await Component.InvokeAsync(nameof(DocumentsSection), Model.PdfDocumentViewModel)

            @{
                Model.GlobalSalesAccessViewModel.IsCompanyInactive = isCompanyInactive;
                Model.GlobalSalesAccessViewModel.IsReadOnly = Model.IsReadOnly;
            }
            @await Html.PartialAsync("_GlobalSalesAccess.cshtml", Model.GlobalSalesAccessViewModel)
        </div>

        <div class="tab-pane fade @(Model.TabQueryParam == (int)CompanyDetailTab.Detail ? "show active" : "")" id="detail" role="tabpanel" aria-labelledby="detail-tab">
            @await Html.PartialAsync("Detail/_Transactions.cshtml")
            @await Html.PartialAsync("Detail/_SalesInformationSection", Model.SalesInformationViewModel)
            @await Html.PartialAsync("Detail/_PurchasingInformationSection", Model.PurchaseInfomationSectionViewModel)
            @await Html.PartialAsync("Detail/_FinanceSection.cshtml", Model.FinanceViewModel)
        </div>
        <div class="tab-pane fade @(Model.TabQueryParam == (int)CompanyDetailTab.Certificate ? "show active" : "")" id="certificate" role="tabpanel" aria-labelledby="certificate-tab">
            @await Html.PartialAsync("Certificates/_CertificatesSection", Model.CertificateTabViewModel)
            @await Component.InvokeAsync(nameof(DocumentsSection), Model.CertificateTabViewModel.PDFSection)
        </div>
    </div>
</div>
@await Html.PartialAsync("_activeInactiveCompanyConfirmDialog")
@await Html.PartialAsync("_CompanyAddressDeleteConfirmDialog")
@await Html.PartialAsync("_CompanyAddressDefaultBillingConfirmDialog")
@await Html.PartialAsync("_CompanyAddressDefaultShippingConfirmDialog")
@await Html.PartialAsync("Detail/_EditSalesInformationDialog", Model.SalesInformationViewModel)
@await Html.PartialAsync("Partials/_AddTodoItem")
<script>
    var companyDetailLocalizedStrings = {
        newCAF: "@_localizer["New CAF"]",
        caf: "@_localizer["CAF"]",
        log: "@_localizer["Log"]",
        name: "@_localizer["Name"]",
        franchised: "@_localizer["Franchised?"]",
        poLineCount: "@_localizer["POLineCount"]",
        rating: "@_localizer["Rating"]",
        noManufacturersSupplied: "@_localizer["NoManufacturersSupplied"]",
        activeCompany: '@_localizer["Active Company"]',
        inactiveCompany: '@_localizer["Inactive Company"]',
        activeCompanyConfirmMessage: '@_localizer["activeCompanyConfirmMessage"]',
        inactiveCompanyConfirmMessage: '@_localizer["inactiveCompanyConfirmMessage"]',
        ceaseCompanyAddressMessage: '@_localizer["ceaseCompanyAddressMessage"]',
        makeCompanyAddressDefaultShippingMessage: '@_localizer["makeCompanyAddressDefaultShippingMessage"]',
        makeCompanyAddressDefaultBillingMessage: '@_localizer["makeCompanyAddressDefaultBillingMessage"]',
    };

    const documentPdfSectionComponent = {
        sectionId: "@Model.PdfDocumentViewModel.SectionId",
        documentTypeId: "@((int)Model.PdfDocumentViewModel.DocumentType)",
        documentTypeName: "@Model.PdfDocumentViewModel.DocumentType.ToString().Trim()",
        sectionName: "@Model.PdfDocumentViewModel.SectionName",
        canAdd: "@(!isCompanyInactive && Model.PdfDocumentViewModel.CanAdd)",
        canDelete: "@(!isCompanyInactive && Model.PdfDocumentViewModel.CanDelete)",
        permissionAdd: "@Model.PdfDocumentViewModel.CanAdd",
        permissionDelete: "@Model.PdfDocumentViewModel.CanDelete",
        isReadOnly: "@Model.PdfDocumentViewModel.IsReadOnly"
    }

    const documentPdfUploadComponent = {
        dialogId: "@Model.PdfDocumentViewModel.UploadDialog.DialogId",
        formId: "@Model.PdfDocumentViewModel.UploadDialog.FormId",
        allowedFileExtensions: "@Model.PdfDocumentViewModel.UploadDialog.AllowedFileExtensions",
    }

    const documentPdfRemoveComponent = {
        dialogId: "@Model.PdfDocumentViewModel.RemoveFileDialog.DialogId",
        sectionName: "@Model.PdfDocumentViewModel.RemoveFileDialog.SectionName",
    }

    const companyDetailPageInfo = {
        title: @Html.Raw(Json.Serialize(pageTitle.Trim()))
    }

    const certificatePdfSectionComponent = {
        sectionId: "@Model.CertificateTabViewModel.PDFSection.SectionId",
        documentTypeId: "@((int)Model.CertificateTabViewModel.PDFSection.DocumentType)",
        documentTypeName: "@Model.CertificateTabViewModel.PDFSection.DocumentType.ToString().Trim()",
        sectionName: "@Model.CertificateTabViewModel.PDFSection.SectionName",
        canAdd: "@(!isCompanyInactive && Model.CertificateTabViewModel.PDFSection.CanAdd)",
        canDelete: "@(!isCompanyInactive && Model.CertificateTabViewModel.PDFSection.CanDelete)",
    }

    const certificatePdfUploadComponent = {
        dialogId: "@Model.CertificateTabViewModel.PDFSection.UploadDialog.DialogId",
        formId: "@Model.CertificateTabViewModel.PDFSection.UploadDialog.FormId",
        allowedFileExtensions: "@Model.CertificateTabViewModel.PDFSection.UploadDialog.AllowedFileExtensions",
    }

    const certificatePdfRemoveComponent = {
        dialogId: "@Model.CertificateTabViewModel.PDFSection.RemoveFileDialog.DialogId",
        sectionName: "@Model.CertificateTabViewModel.PDFSection.RemoveFileDialog.SectionName",
    }

    const companyDetailsInfoFromPage = @Html.Raw(JsonSerializer.Serialize(
        Model.CompanyDetails.CompanyInfo, 
        new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            }
    ))
   
</script>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/html-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/sort-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/resize-data-table-extensions.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/form-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datetime-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/number-validation.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/search-select.js")" asp-append-version="true"></script>
    <environment include="Development">
        <script type="module" src="/js/modules/contact/all-companies/details/company-details.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script type="module" src="/dist/js/contact-company-details.bundle.js" asp-append-version="true"></script>
    </environment>
}
