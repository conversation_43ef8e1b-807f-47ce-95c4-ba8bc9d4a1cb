﻿import { DocumentsManager } from '../../../../../../components/documents-list/document-list.js'

export class EuuPdfManager extends DocumentsManager {
    constructor(soId) {
        super({
            documentSectionComponent: {
                documentTypeId: 1, //PDF
                documentTypeName: "PDF",
                sectionName: "EUU",
                canAdd: false,
                canDelete: false,
                sectionId: "euu" 
            },
            id: soId,
        });
        this.salesOrderId = soId;
        this._selectedRow = null;
        this._$loading = $("#loading");
    }

    async initialize() {
        this.$DocumentsSectionBox = $("#pdf-section-wrapper")
        let createDocumentInstanceFunction = DocumentsManager.DocumentTypeFactory[this.documentTypeId];
        this.documentInstance = createDocumentInstanceFunction(this.id);
        await this.initDocumentSectionAsync();

        const maxPDFFileSizeData = await this.getMaxDocumentFileSize();
        $("#file-size").html(`<div>${euuLocalized.maxFileSize} (${maxPDFFileSizeData.documentMB} MB)</div>`)
    }

    async initDocumentInstance() {
        let createDocumentInstanceFunction = DocumentsManager.DocumentTypeFactory[this.documentTypeId];
        this.documentInstance = createDocumentInstanceFunction(this.id);
    }

    async updateSelectedRow(selectedRow) {
        this._selectedRow = selectedRow;
        if (!this._selectedRow?.isECCNWarning) {
            this.$DocumentsSectionBox.hide();
        } else {
            this.$DocumentsSectionBox.show();
            await this.refreshPdfSection();
        }
    }

    async getData() {
        if (this._selectedRow == null) return [];
        const lineId = this._selectedRow.salesOrderLineId;
        const response = await GlobalTrader.ApiClient.getAsync(`orders/sales-order/so-lines/${lineId}/euu-pdfs`);
        if (!response.success) {
            showToast("danger", "An error occurred.");
            return [];
        }
        return response.data;
    }

    async initDocumentSectionAsync() {
        if (this._selectedRow == null) return;
        this._setLoading(true);
        $("#content").hide();
        const documentsListData = await this.getData();
        this.documentsList = documentsListData;
        this.currentDocumentCount = documentsListData?.length || 0;
        this.renderDocumentsListAsync(documentsListData);
        this._setLoading(false);
        $("#content").show();
    }

    async refreshPdfSection() {
        if (this._selectedRow == null) return;
        await this.initDocumentSectionAsync();
    }

    _setLoading(isLoading) {
        if (isLoading) {
            this._$loading.html(
                `<div class="d-flex">
                    <div class="spinner-loader me-1"></div>
                    <div class="text-loader"></div>
                </div>`
            );
        } else {
            this._$loading.html("");
        }
    }
}