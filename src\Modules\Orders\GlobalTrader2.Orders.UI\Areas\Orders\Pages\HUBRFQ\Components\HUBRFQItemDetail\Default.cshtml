@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer

@{
    var col1Width = 4;
    var col2Width = 4;
    var col3Width = 4;
    var labelWidth = 4;
    var valueWidth = 8;
    var checkboxValueWidth = 8;
    var autoWidth = "auto";
    var checkboxMarginLeft = "12px";
}

<div id="hubrfq-item-detail-loading-indicator"></div>
<div class="container-fluid p-2 d-none" id="hubrfq-item-detail">
    <div class="row align-items-start">
        <!-- Column 1 -->
        <div class="col-@col1Width" id="column-1">
            <!-- Quantity -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Quantity"]</p>
                <span class="col-@valueWidth text-break pe-0" name="quantity"></span>
            </div>
            <!-- Part No -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["PartNo"]</p>
                <span name="partNo" class="col-@autoWidth text-break pe-1"></span>
                <span class="col-@autoWidth text-break ps-0" name="stockAlert" style="display: none">
                    <div class="hover-container">
                        <span class="hover-span">@_localizer["StockAlert"]</span>
                        <div class="hover-content">
                            <strong>@_localizer["StockCrossClient"]</strong>
                            <p data-bs-toggle="tooltip" data-bs-placement="bottom" title="" name="inStock">@_localizer["InStock"] : </p>
                            <p data-bs-toggle="tooltip" data-bs-placement="bottom" title="" name="onOrder">@_localizer["OnOrder"] : </p>
                            <p data-bs-toggle="tooltip" data-bs-placement="bottom" title="" name="allocated">@_localizer["Allocated"] : </p>
                            <p data-bs-toggle="tooltip" data-bs-placement="bottom" title="" name="available">@_localizer["Available"] : </p>
                        </div>
                    </div>
                </span>
                @* Show PDF *@
                <a href="#" name="showPDF" target="_blank" style="display: none;" title="Click to View docs" class="col-@autoWidth">
                    <img src="~/img/icons/pdf.svg" alt="PDF" width="18" height="18" />
                </a>
            </div>
            <!-- Inhouse AS6081 -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["AS6081"]</p>
                <span name="as6081" class="col-@valueWidth text-break"></span>
            </div>
            <!-- ROHS -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["ROHS"]</p>
                <span name="rohs" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Cust Part No -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["CusPartNo"]</p>
                <span name="cusPartNo" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Manufacturer -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Manufacturer"]</p>
                <span name="manufacturer" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Date Code -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["DateCode"]</p>
                <span name="dateCode" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Product -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Product"]</p>
                <span name="product" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Duty Code Rate -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["DutyCodeRate"]</p>
                <span name="dutyCodeRate" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Package -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Package"]</p>
                <span name="package" class="col-@valueWidth text-break"></span>
            </div>
            <!-- PartWatch -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["PartWatch"]</p>
                <input class="mt-0 p-0 pe-none form-control form-check-input check-sm col-@checkboxValueWidth" type="checkbox"
                       title="@_localizer["PartWatch"]" name="partWatch" disabled style="margin-left:@checkboxMarginLeft">
            </div>
            <!-- Factory Sealed -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["FactorySealed"]</p>
                <input class="mt-0 p-0 pe-none form-control form-check-input check-sm col-@checkboxValueWidth" type="checkbox"
                       title="@_localizer["FactorySealed"]" name="factorySealed" disabled style="margin-left:@checkboxMarginLeft">
            </div>
            <!-- MSL -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["MSL"]</p>
                <span name="msl" class="col-@valueWidth text-break"></span>
            </div>
            <!-- No-Bid -->
            <div class="row" style="display: none;">
                <p class="col-@labelWidth form-label fw-bold"></p>
                <span name="noBid" class="col-@valueWidth text-break"></span>
            </div>
            <!-- No-Bid Note -->
            <div class="row" style="display: none;">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["NoBidNote"]</p>
                <span name="noBidNote" class="col-@valueWidth text-break"></span>
            </div>
            <!-- IHS Manufacturer -->
            <!-- <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["IHSManufacturer"]</p>
                <span name="ihsManufacturer" class="col-@valueWidth text-break"></span>
            </div> -->
            <!-- Country Of Origin -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["CountryOfOrigin"]</p>
                <span name="countryOfOrigin" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Part Status -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["PartStatus"]</p>
                <span name="partStatus" class="col-@valueWidth text-break"></span>
            </div>
            <!-- IHS Product -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["IHSProduct"]</p>
                <span name="ihsProduct" class="col-@valueWidth text-break"></span>
            </div>
            <!-- HTS Code -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["HTSCode"]</p>
                <span name="htsCode" class="col-@valueWidth text-break"></span>
            </div>
            <!-- ECCN Code -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["ECCNCode"]</p>
                <span name="eccnCode" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Packaging Size -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["PackagingSize"]</p>
                <span name="packagingSize" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Descriptions -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Descriptions"]</p>
                <span name="descriptions" class="col-@valueWidth text-break"></span>
            </div>
        </div>
        <!-- Column 2 -->
        <div class="col-@col2Width" id="column-2">
            <!-- Customer Target Price -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["CusTargetPrice"]</p>
                <div class="col-@valueWidth d-flex align-items-start gap-2">
                    <span name="cusTargetPrice" class="text-break"></span>
                    <button type="button" class="btn btn-primary h-20 flex-shrink-0" id="magicbox">
                        <img src="~/img/icons/magic-box.svg" alt="magicbox" width="12" height="12" />
                    </button>
                </div>
            </div>
            <!-- Currency -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Currency"]</p>
                <span name="currency" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Customer Date Required -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["CusDateRequired"]</p>
                <span name="cusDateRequired" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Closed -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Closed"]</p>
                <span name="closed" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Reason 1 -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Reason1"]</p>
                <span name="reason1" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Usage -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Usage"]</p>
                <span name="usage" class="col-@valueWidth text-break"></span>
            </div>
            <!-- BOM -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["BOM"]</p>
                <input class="mt-0 p-0 pe-none form-control form-check-input check-sm col-@checkboxValueWidth" type="checkbox"
                       title="@_localizer["BOM"]" name="bom" disabled style="margin-left:@checkboxMarginLeft">
            </div>
            <!-- BOM Name -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["BOMName"]</p>
                <span name="bomName" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Notes To Customer -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["NoteToCus"]</p>
                <span name="noteToCus" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Internal Notes -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["InternalNote"]</p>
                <span name="internalNote" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Partial Quantity Acceptable -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["PartialQuantityAcceptable"]</p>
                <input class="mt-0 p-0 pe-none form-control form-check-input check-sm col-@checkboxValueWidth" type="checkbox"
                       title="@_localizer["PartialQuantityAcceptable"]" name="partialQuantityAcceptable" disabled style="margin-left:@checkboxMarginLeft">
            </div>
            <!-- Price Request -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["PriceRequest"]</p>
                <span name="priceRequest" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Lytica Manufacturer Ref -->
            <div class="row">
                <p class="form-label fw-bold col-4">
                    <span class="mb-0">
                        @_localizer["LyticaManufacturerRef"]
                    </span>
                </p>
                <div class="col-8">
                    <input type="text" class="form-control form-input" data-bind-name="lyticaManufacturerRef" name="lyticaManufacturerRef" id="lyticaManufacturerRefInput">
                    <input type="hidden" name="lyticaManufacturerRefValue" id="lyticaManufacturerRefInputValue" value="" data-bind-name="lyticaManufacturerRefValue">
                </div>
            </div>
            <!-- AVG Price -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["AVGPrice"]</p>
                <span name="avgPrice" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Target Price -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["TargetPrice"]</p>
                <span name="targetPrice" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Market Leading -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["MarketLeading"]</p>
                <span name="marketLeading" class="col-@valueWidth text-break"></span>
            </div>
        </div>
        <!-- Column 3 -->
        <div class="col-@col3Width" id="column-3">
            <!-- Refurbs Acceptable -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["RefurbsAcceptable"]</p>
                <input class="mt-0 p-0 pe-none form-control form-check-input check-sm col-@checkboxValueWidth" type="checkbox"
                       title="@_localizer["RefurbsAcceptable"]" name="refurbsAcceptable" disabled style="margin-left:@checkboxMarginLeft">
            </div>
            <!-- Testing Required -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["TestingRequired"]</p>
                <input class="mt-0 p-0 pe-none form-control form-check-input check-sm col-@checkboxValueWidth" type="checkbox"
                       title="@_localizer["TestingRequired"]" name="testingRequired" disabled style="margin-left:@checkboxMarginLeft">
            </div>
            <!-- Alternatives Accepted -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["AltAccepted"]</p>
                <input class="mt-0 p-0 pe-none form-control form-check-input check-sm col-@checkboxValueWidth" type="checkbox"
                       title="@_localizer["AltAccepted"]" name="altAccepted" disabled style="margin-left:@checkboxMarginLeft">
            </div>
            <!-- Regular/Repeat business -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Bussiness"]</p>
                <input class="mt-0 p-0 pe-none form-control form-check-input check-sm col-@checkboxValueWidth" type="checkbox"
                       title="@_localizer["Bussiness"]" name="bussiness" disabled style="margin-left:@checkboxMarginLeft">
            </div>
            <!-- Competitor Best Offer -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["CompBestOffer"]</p>
                <span name="compBestOffer" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Customer Decision Date -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["CusDecisionDate"]</p>
                <span name="cusDecisionDate" class="col-@valueWidth text-break"></span>
            </div>
            <!-- RFQ Closing Date -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["RFQClosingDate"]</p>
                <span name="rfqClosingDate" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Quote Validity Required -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["QuoteValidityRequired"]</p>
                <span name="quoteValidityRequired" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Type -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["Type"]</p>
                <span name="type" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Order To Place -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["OrdToPlace"]</p>
                <input class="mt-0 p-0 pe-none form-control form-check-input check-sm col-@checkboxValueWidth" type="checkbox"
                       title="@_localizer["OrdToPlace"]" name="ordToPlace" disabled style="margin-left:@checkboxMarginLeft">
            </div>
            <!-- Requirement for Traceability -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["ReqForTrace"]</p>
                <span name="reqForTrace" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Estimated Annual Usage -->
            <div class="row">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["EstAnnualUsage"]</p>
                <span name="estAnnualUsage" class="col-@valueWidth text-break"></span>
            </div>
            <!-- Customer Ref No -->
            <div class="row" style="display: none;">
                <p class="col-@labelWidth form-label fw-bold">@_localizer["CustomerRefNo"]</p>
                <span name="customerRefNo" class="col-@valueWidth text-break"></span>
            </div>
        </div>
    </div>
</div>

<script>
    const localizeStrings = {
        noBid: "@_localizer["NoBid"]"
    }
</script>