﻿

GlobalTrader.PageUrlHelper = (function () {
    const contactDetailUrl = window.url.contactDetailPage;
    const companyDetailUrl = window.url.companyDetailPage;
    const manufacturerUrl = window.url.manufacturerPage;
    const stockDetailUrl = window.url.stockDetaiPage;
    const toDoListUrl = window.url.toDoListPage;
    const bomDetailUrl = window.url.bomDetailPage;
    const salesOrdersDetailUrl = window.url.salesOrdersDetailPage;
    const purchaseRequisitionDetailUrl = window.url.purchaseRequisitionDetailPage;
    const customerRequirementUrl = window.url.customerRequirementDetailPage;
    const purchaseQuotesDetailUrl = window.url.purchaseQuotesDetailPage;
    const HyperLinkTargetConstant = Object.freeze({
        CONTACT_DETAIL: 0,
        COMPANY_DETAIL: 1,
        MANUFACTURER_DETAIL: 2,
        STOCK_DETAIL: 3,
        TODOLIST_DETAIL: 4,
        BOM_DETAIL: 5
    });

    return {
        Get_URL_Contact(contactId, companyListType) {
            let params = new URLSearchParams();
            params.append(window.constants.params.contactId, contactId);

            if (companyListType != null) {
                params.append(window.constants.params.companyListType, companyListType);
            }

            return `${contactDetailUrl}?${params.toString()}`;
        },
        Get_URL_Company(companyId, tabId, companyListType) {
            if(!companyId) return;

            let params = new URLSearchParams();
            params.append(window.constants.params.companyId, companyId);

            if (tabId) params.append(window.constants.params.tabId, tabId);
            if (companyListType != null && companyListType != undefined) params.append(window.constants.params.companyListType, companyListType);
            return `${companyDetailUrl}?${params.toString()}`;
        },
        Get_URL_Manufacturer(manufacturerId) {
            let params = new URLSearchParams();
            params.append(window.constants.params.manufacturerId, manufacturerId);

            return `${manufacturerUrl}?${params.toString()}`;
        },
        GET_URL_BOM(bomId) {
            if (!bomId) return;

            let params = new URLSearchParams();
            params.append(window.constants.params.bomId, bomId);

            return `${bomDetailUrl}?${params.toString()}`;
        },
        GET_URL_PurchaseQuote(purchaseQuoteId) {
            if (!purchaseQuoteId) return;

            let params = new URLSearchParams();
            params.append(window.constants.params.purchaseQuoteId, purchaseQuoteId);

            return `${purchaseQuotesDetailUrl}?${params.toString()}`
        },
        GET_URL_CustomerRequirement(customerRequirementId) {
            if (!customerRequirementId) return;

            let params = new URLSearchParams();
            params.append(window.constants.params.customerRequirementId, customerRequirementId);

            return `${customerRequirementUrl}?${params.toString()}`;
        },
        Get_URL_Stock(stockId)
        {
            let params = new URLSearchParams();
            params.append(window.constants.params.stockId, stockId);

            return `${stockDetailUrl}?${params.toString()}`;
        },
        Get_URL_ToDoList(companyId, companyName) {
            let params = [];
            if (companyId !== null && companyId !== undefined) params.push(`${window.constants.params.companyId}=${encodeURIComponent(companyId)}`);
            if (companyName && companyName.trim().length > 0) params.push(`${window.constants.params.companyName}=${encodeURIComponent(companyName)}`);
            return params.length > 0 ? `${toDoListUrl}?${params.join('&')}` : toDoListUrl;
        },
        Get_URL_By_Target({ targetType, targetId }) {
            const target = {
                [HyperLinkTargetConstant.CONTACT_DETAIL]: (contactId) => {
                    return this.Get_URL_Contact(contactId);
                },
                [HyperLinkTargetConstant.COMPANY_DETAIL]: (companyId) => {
                    return this.Get_URL_Company(companyId);
                },
                [HyperLinkTargetConstant.MANUFACTURER_DETAIL]: (manufacturerId) => {
                    return this.Get_URL_Manufacturer(manufacturerId);
                },
                [HyperLinkTargetConstant.STOCK_DETAIL]: (stockId) => {
                    return this.Get_URL_Stock(stockId);
                },
                [HyperLinkTargetConstant.TODOLIST_DETAIL]: (companyId) => {
                    return this.Get_URL_ToDoList(companyId);
                },
                [HyperLinkTargetConstant.BOM_DETAIL]: (bomId) => {
                    return this.GET_URL_BOM(bomId);
                },
            }
            const targetInstance = target[targetType];
            if (targetInstance) {
                return targetInstance(targetId);
            }
        },
        Get_URL_SalesOrders(salesOrderNo) {
            return `${salesOrdersDetailUrl}?${window.constants.params.salesOrdersNo}=${salesOrderNo}`;
        },
        Get_Url_PurchaseRequisition(purchaseRequisitionId) {
            return `${purchaseRequisitionDetailUrl}?${window.constants.params.purchaseRequisitionId}=${purchaseRequisitionId}`;
        }
    };
})();

function $RGT_openCreditLimitLogWindow(companyId, creditLimitId) {
    const strURL = `/Contact/AllCompanies/CreditLimitLog?cm=${companyId}&CreditLimit=${creditLimitId}`;
    window.open(
        strURL,
        "winCreditLimit",
        "left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes"
    );
}

function $RGT_openCreditLimitWindow(companyId, creditLimitId = null) {
    let strURL = `/Contact/AllCompanies/Details/CreditLimit?cm=${companyId}`;
    if (creditLimitId) {
        strURL = `${strURL}&CreditLimit=${creditLimitId}`
    }
    window.open(strURL, "winCreditLimit", "left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
};
