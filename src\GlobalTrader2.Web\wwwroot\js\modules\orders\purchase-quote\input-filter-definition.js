﻿import { FieldType } from '../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { NumberType } from '../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#';
import { ClientId } from '../constants/orders.constant.js?v=#{BuildVersion}';
export const inputFilterDefinition = [
    {
        fieldType: FieldType.NUMBER,
        label: 'Price Request No',
        name: 'POQuoteNo',
        attributes: {
            "data-input-type": "numeric",
            "data-input-format": "int",
            "data-input-min": 0,
            "data-input-max": 2147483647,
            "data-input-type-allow-empty": true
        },
        extraPros: {
            numberType: NumberType.INT
        },
        value: '',
        locatedInContainerByClass: 'filter-column-1'
    },
    {
        fieldType: FieldType.TEXT,
        label: 'Part No',
        name: 'Part',
        value: '',
        locatedInContainerByClass: 'filter-column-1'
    },
    {
        fieldType: FieldType.CHECKBOX,
        label: 'Recent Only?',
        name: 'RecentOn<PERSON>',
        value: '',
        locatedInContainerByClass: 'filter-column-1'
    },
    {
        fieldType: FieldType.TEXT,
        label: 'Company',
        name: 'CMName',
        value: '',
        locatedInContainerByClass: 'filter-column-2'
    },
    {
        fieldType: FieldType.DATE,
        label: 'Date From',
        name: 'DatePOQuotedFrom',
        value: '',
        locatedInContainerByClass: 'filter-column-2',
        pairWith: "DatePOQuotedTo"
    },
    {
        fieldType: FieldType.DATE,
        label: 'Date To',
        name: 'DatePOQuotedTo',
        value: '',
        locatedInContainerByClass: 'filter-column-2'
    },
    {
        fieldType: FieldType.SELECT,
        label: 'Buyer',
        name: 'Salesman',
        value: '',
        options: {
            serverside: false,
            endpoint: `/lists/employee/purchase-hub/${ClientId.DMCC}`,
            valueKey: 'loginId',
            textKey: 'employeeName',
            isHideRefresButton: false,
            isCacheApplied: true
        },
        locatedInContainerByClass: 'filter-column-2'
    },   
]