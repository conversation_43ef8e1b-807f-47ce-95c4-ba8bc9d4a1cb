import { LinesTabService } from '../lines.service.js?v=#{BuildVersion}#'
import { LinesResizeDatatable } from './lines-table.component.js?v=#{BuildVersion}#'
import { PromiseLogResizeDatatable } from './promise-log-table.component.js?v=#{BuildVersion}#'
import { CollapseAreaButton } from '../../../../../../../components/collapse-area-button.js?v=#{BuildVersion}#'
import { ButtonHelper } from '../../../../../../../helper/button-helper.js?v=#{BuildVersion}#'
import { ROHSHelper } from '../../../../../../../helper/rohs-helper.js?v=#{BuildVersion}#'
import { ResizeDatatableEvents } from '../../../../../../../components/base/resize-lite-datatable/resize-lite-datatable-events.constanst.js?v=#{BuildVersion}#'
import { EventEmitter } from '../../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#'


export class LinesTabManager extends EventEmitter {
    constructor(salesOrderId, level) {
        super();
        this.linesResizeDatatable = null;
        this.isMessageAreasVisible = false;

        this.$editButton = $('#lines-edit-btn');

        this._level = level;
        this._$subtotal = $(`#lines-${this._level}-subtotal`);
        this._$freight = $(`#lines-${this._level}-freight`);
        this._$tax = $(`#lines-${this._level}-tax`);
        this._$total = $(`#lines-${this._level}-total`);
        this._$detailWrapper = $(`#${this._level}-so-line-details-wrapper`);
        this._$loading = $(`#${this._level}-so-line-details-loading-indicator`);
        this._$collapseButton = $(`#${this._level}-so-line-details-collapse-button`);
        this._$collapsedArea = $(`#${this._level}-so-line-details`);
        this._$indexSpan = $(`#${this._level}-so-line-details-description`);
        this._$previousButton = $(`#${this._level}-so-line-details-previous-button`);
        this._$nextButton = $(`#${this._level}-so-line-details-next-button`);
        this._$inactiveSpan = $(`#${this._level}-so-line-details-inactive`);
        this._$productSourceName = this._$detailWrapper.find('span[data-dto-name="productSourceName"]');
        this._$linesMessage = $(`#lines-${this._level}-message`);
        this._serviceLineDetails = {
            $service: this._$detailWrapper.find(`#${this._level}-so-line-details-service`),
            $serviceDescription: this._$detailWrapper.find(`#${this._level}-so-line-details-service-descriptions`),
        };
        this._lineDetails = {
            $partNo: this._$detailWrapper.find(`#${this._level}-so-line-details-part-no`),
            $customerPartNo: this._$detailWrapper.find(`#${this._level}-so-line-details-customer-part-no`),
            $manufacturer: this._$detailWrapper.find(`#${this._level}-so-line-details-manufacturer`),
            $rohs: this._$detailWrapper.find(`#${this._level}-so-line-details-rohs`),
            $quantityAllocated: this._$detailWrapper.find(`#${this._level}-so-line-details-quantity-allocated`),
            $quantityBackOrder: this._$detailWrapper.find(`#${this._level}-so-line-details-quantity-backorder`),
            $dateCode: this._$detailWrapper.find(`#${this._level}-so-line-details-date-code`),
            $product: this._$detailWrapper.find(`#${this._level}-so-line-details-product-dis`),
            $package: this._$detailWrapper.find(`#${this._level}-so-line-details-package`),
            $shipAsap: this._$detailWrapper.find('input[data-dto-name="shipASAP"]'),
            $contractNo: this._$detailWrapper.find('span[data-dto-name="contractNo"]'),
            $shipping: this._$detailWrapper.find(`#${this._level}-so-line-details-shipping`),
            $eccn: this._$detailWrapper.find(`#${this._level}-so-line-details-eccn`),
            $as6081: this._$detailWrapper.find('span[data-dto-name="aS6081"]'),
            $msl: this._$detailWrapper.find('span[data-dto-name="mslLevel"]'),
            $countryOfOrigin: this._$detailWrapper.find('span[data-dto-name="countryOfOrigin"]'),
            $dateConfirmed: this._$detailWrapper.find(`#${this._level}-so-line-details-date-confirmed`),
            $lifeCycleStage: this._$detailWrapper.find('span[data-dto-name="lifeCycleStage"]'),
            $ihsProduct: this._$detailWrapper.find('span[data-dto-name="ihsProduct"]'),
            $htsCode: this._$detailWrapper.find('span[data-dto-name="htsCode"]'),
            $packagingSize: this._$detailWrapper.find('span[data-dto-name="packagingSize"]'),
            $descriptions: this._$detailWrapper.find('span[data-dto-name="descriptions"]'),
            $dutyCodeAndRate: this._$detailWrapper.find('span[data-dto-name="dutyCodeAndRate"]'),
        };
        this._collapseAreaButton = new CollapseAreaButton(this._$collapseButton[0].id, this._$detailWrapper[0].id);

        this._salesOrderId = salesOrderId;
        this._linesResizeDatatableId = `lines-${this._level}-table`;
        this._promiseLogResizeDatatableId = `${this._level}-so-line-details-promise-reason-table`;
        this._promiseLogResizeDatatable = null;
        this._data = null; // Data for the lines table
        this.detailsData = null; // Data for the line details
    }

    async init() {
        this._data = await this.getData();
        this.linesResizeDatatable = new LinesResizeDatatable(this._linesResizeDatatableId, this._data?.items ?? []);
        
        this._bindSaleOrderTotal();
        this._setupMessageEventListener();
    }

    initTable() {
        this.linesResizeDatatable.init();
        this._setupDetailsArrowEvents();
    }

    renderTable(data) {
        let selectedRowId = null;
        const selectedRow = this.linesResizeDatatable.datatable.row({ selected: true }).data();
        if (selectedRow) {
            selectedRowId = selectedRow.lineId;
        }
        this.linesResizeDatatable.renderDatatable(data, selectedRowId);
    }

    async getData() {
        const response = await LinesTabService.getLinesTabDatatableAsync(this._salesOrderId, this._level);
        if (response?.success) return response.data;
        else {
            showToast("danger", response?.title);
            return undefined;
        }
    }

    _bindSaleOrderTotal() {
        this._$subtotal.text(this._data.subTotal);
        this._$freight.text(this._data.freight);
        this._$tax.text(this._data.tax);
        this._$total.text(this._data.total);
    }

    async _bindDetailsData(data) {
        this._$detailWrapper.find(`span[data-dto-name]`).each((index, element) => {
            if (element.dataset.dtoName === 'aS6081') {
                $(element).text(data[element.dataset.dtoName] ? 'Yes' : 'No');
            }
            else if (element.dataset.dtoName === 'lineNotes') {
                $(element).html(GlobalTrader.StringHelper.setCleanTextValue(data[element.dataset.dtoName]), true);
            } else {
                $(element).text(data[element.dataset.dtoName]);
            }
        })
        this._lineDetails.$shipAsap.prop('checked', data["shipASAP"]);

        if (data['serviceNo'] > 0) {
            this._serviceLineDetails.$service.html(ButtonHelper.nubButton_Service(data.serviceNo, data.part));
            this._serviceLineDetails.$serviceDescription.text(GlobalTrader.StringHelper.setCleanTextValue(data.customerPart));
            Object.entries(this._serviceLineDetails).forEach(([key, $element]) => {
                $element.parent().parent().show();
            });
            Object.entries(this._lineDetails).forEach(([key, $element]) => {
                $element.parent().parent().hide();
            });
        }
        else {
            Object.entries(this._serviceLineDetails).forEach(([key, $element]) => {
                $element.parent().parent().hide();
            });
            Object.entries(this._lineDetails).forEach(([key, $element]) => {
                $element.parent().parent().show();
            });

            if (data.stockAvailableDetail != null && data.stockAvailableDetail != '') {
                const stockDetailArray = data.stockAvailableDetail.split('-');
                const QuantityInStock = stockDetailArray[0];
                const QuantityOnOrder = stockDetailArray[1];
                const QuantityAllocated = stockDetailArray[2];
                const QuantityAvailable = stockDetailArray[3];

                this._lineDetails.$partNo.html(
                    GlobalTrader.HtmlHelper.showStockAvailableNew(ROHSHelper.writePartNo(data.part, data.rohs),
                        QuantityInStock, QuantityOnOrder, QuantityAllocated, QuantityAvailable, "")
                );
            }
            else {
                this._lineDetails.$partNo.html(ROHSHelper.writePartNo(data.part, data.rohs));
            }

            this._lineDetails.$customerPartNo.text(GlobalTrader.StringHelper.setCleanTextValue(data.customerPart));
            this._lineDetails.$manufacturer.html(ButtonHelper.nubButton_Manufacturer(data.mfrNo, data.mfr, data.mfrAdvisoryNotes));
            this._lineDetails.$rohs.html(ROHSHelper.createRohsHtml(data.rohs));
            this._lineDetails.$quantityAllocated.text(data.allocated);
            this._lineDetails.$quantityBackOrder.text(data.backOrder);
            this._lineDetails.$dateCode.text(GlobalTrader.StringHelper.setCleanTextValue(data.dateCd));
            this._lineDetails.$product.html(
                GlobalTrader.HtmlHelper.showProductWarningIndividual(data.product, data.isProdHaz,
                    data.isOrderViaIpoOnly, data.isRestrictedProduct, GlobalTrader.StringHelper.setCleanTextValue(data.msgHazardous),
                    GlobalTrader.StringHelper.setCleanTextValue(data.msgIpo), GlobalTrader.StringHelper.setCleanTextValue(data.msgRestricted)));
            this._lineDetails.$package.text(GlobalTrader.StringHelper.setCleanTextValue(data.package));
            this._lineDetails.$shipping.html(GlobalTrader.StringHelper.setCleanTextValue(data.instructions), true);
            this._lineDetails.$dateConfirmed.text(data.dateConfirmed);

            if (data.ihsEccnsCodeDefination != null && data.ihsEccnsCodeDefination !== '') {
                this._lineDetails.$eccn.html(GlobalTrader.HtmlHelper.showIHSECCNCodeDefinition(data.eccnCode, data.ihsEccnsCodeDefination));
            }
            else {
                this._lineDetails.$eccn.html(`<span>${data.eccnCode}</span>`);
            }
        }

        // Hide the parent
        this._$detailWrapper.find('span[data-dto-name="cloneSerialNo"]')
            .parent().parent().toggleClass('d-none', data.cloneSerialNo <= 0);

        this._$detailWrapper.find(`#${this._level}-so-line-details-quote`)
            .html(ButtonHelper.nubButton_SoQuote(data.quoteId, data.quoteNumber, data.quoteLineNo))
            .parent().parent().toggleClass('d-none', data.quoteLineNo <= 0);

        this._$detailWrapper.find(`#${this._level}-so-line-details-customer-requirement`)
            .html(ButtonHelper.nubButton_CustomerRequirement(data.customerRequirementId, data.customerRequirementNumber))
            .parent().parent().toggleClass('d-none', data.customerRequirementId <= 0);

        if (data.aS6081) {
            const alertMessage = await this._getAS6081BannerMessageAsync();
            this.isMessageAreasVisible = true;
            this.trigger('addMessage', alertMessage);
            this.trigger('showMessage');
            this._lineDetails.$as6081.css('background-color', 'yellow');
        }
        else {
            this.trigger('clearMessage');
            this.trigger('hideMessage');
            this._lineDetails.$as6081.css('background-color', 'transparent');
        }

        const aS9120 = $('#sales-orders-main-info-wrapper').find('input[data-field="aS9120"]').prop('checked') ?? false;
        this._$productSourceName
            .parent().parent().toggleClass('d-none',
                !(data.productSourceName?.length > 0 && aS9120));
        this._$inactiveSpan.toggleClass('d-none', !data.inactive);
    }

    _setupDetailsArrowEvents() {
        GlobalTrader.DataTablesDetailHelper.setUpDetailPrevButton(this.linesResizeDatatable.datatable, this._$previousButton);
        GlobalTrader.DataTablesDetailHelper.setUpDetailNextButton(this.linesResizeDatatable.datatable, this._$nextButton);
    }

    setupRowSelectionEvent() {
        this.linesResizeDatatable.on(ResizeDatatableEvents.SELECT, async (e, dt, type, indexes) => {
            const rowData = dt.row(indexes).data();
            this._setDetailLoading(true);

            this.$editButton.prop('disabled', true);
            this.detailsData = await this._getSoLineDetailsAsync(rowData.lineId);
            if (this.detailsData != null) {
                await this._bindDetailsData(this.detailsData);
            }

            this._$indexSpan.text(`Line ${parseInt(indexes) + 1} of ${dt.rows().count()}`);

            const promiseLogUrl = LinesTabService.getPromiseLogUrl(rowData.lineId);
            if (this._promiseLogResizeDatatable == null) {
                this._promiseLogResizeDatatable = new PromiseLogResizeDatatable(this._promiseLogResizeDatatableId, promiseLogUrl);
                this._setupPromiseLogTableEvents();
                this._promiseLogResizeDatatable.init();
            }
            else {
                this._promiseLogResizeDatatable.datatable.ajax.url(promiseLogUrl).load(() => {
                    this._setDetailLoading(false);
                    this._promiseLogResizeDatatable.datatable.resizeTable(2);
                });
            }
        });
    }

    _setupPromiseLogTableEvents() {
        this._promiseLogResizeDatatable.on(ResizeDatatableEvents.DRAW, () => {
            this._setDetailLoading(false);
        });
    }

    _setDetailLoading(isLoading) {
        if (isLoading) {
            this._$loading.html(
                `<div class="d-flex">
                    <div class="spinner-loader me-1"></div>
                    <div class="text-loader"></div>
                </div>`);
        } else {
            this._$loading.html("");
        }
        this._$collapsedArea.toggleClass('d-none', isLoading);
    }

    _setupMessageEventListener() {
        this.on('addMessage', (message) => {
            this._$linesMessage.html(
                `<div class="nugget-message-warning">
                    ${message}
                </div>`
            );
        });

        this.on('showMessage', () => {
            if (this._$linesMessage.html().length > 0) { //Check
                this._$linesMessage.removeClass('d-none');
            }
        });

        this.on('hideMessage', () => {
            this._$linesMessage.addClass('d-none');
        });

        this.on('clearMessage', () => {
            this._$linesMessage.html("");
        });
    }

    async _getSoLineDetailsAsync(salesOrderLineId) {
        const response = await LinesTabService.getLineDetailsAsync(salesOrderLineId);
        if (response?.success && response.data !== null) {
            return response.data;
        }
        else {
            showToast('danger', response?.title ?? 'Bad Request')
            return null;
        }
    }

    async _getAS6081BannerMessageAsync() {
        const response = await LinesTabService.getAS6081BannerMessageAsync();
        if (response?.success && response.data !== null) {
            return response.data;
        } else {
            return "";
        }
    }
}