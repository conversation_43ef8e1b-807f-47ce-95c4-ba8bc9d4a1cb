﻿using GlobalTrader2.Dto.DataListNugget;

namespace GlobalTrader2.Orders.UI.ViewModel.CustomerRequirement
{
    public static class DataSourceListNugetStateModel
    {
        public static readonly IReadOnlyDictionary<string, DataListNuggetFilterStateModel> CustomerRequirements = new Dictionary<string, DataListNuggetFilterStateModel>()
        {
            { "Part", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "Part",
                IsOn = false,
                IsShown = false,
            }
            },
            { "Contact", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "Contact",
                IsOn = false,
                IsShown = false,
            }
            },
            { "CMName", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "CMName",
                IsOn = false,
                IsShown = false,
            }
            },
            { "Salesman", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DropDown,
                Comparison = 0,
                Name = "Salesman",
                IsOn = false,
                IsShown = false,
            }
            },
            { "RecentOnly", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.CheckBox,
                Comparison = 0,
                Name = "RecentOnly",
                Value = "true",
                IsOn = true,
                IsShown = true,
            }
            },
            { "IncludeClosed", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.CheckBox,
                Comparison = 0,
                Name = "IncludeClosed",
                         IsOn = false,
                IsShown = false,
            }
            },
            { "CReqNo", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.Numerical,
                Comparison = 0,
                Name = "CReqNo",
                IsOn = false,
                IsShown = false,
            }
            },
            { "ReceivedDateFrom", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DateSelect,
                Comparison = 0,
                Name = "ReceivedDateFrom",
                IsOn = false,
                IsShown = false,
            }
            },
            { "ReceivedDateTo", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DateSelect,
                Comparison = 0,
                Name = "ReceivedDateTo",
                IsOn = false,
                IsShown = false,
            }
            },
            { "DatePromisedFrom", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DateSelect,
                Comparison = 0,
                Name = "DatePromisedFrom",
                IsOn = false,
                IsShown = false,
            }
            },
            { "DatePromisedTo", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DateSelect,
                Comparison = 0,
                Name = "DatePromisedTo",
                IsOn = false,
                IsShown = false,
            }
            },
            { "BOMName", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "BOMName",
                IsOn = false,
                IsShown = false,
            }
            },
            { "PartWatch", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.CheckBox,
                Comparison = 0,
                Name = "PartWatch",
                IsOn = false,
                IsShown = false,
            }
            },
            { "StatusREQ", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DropDown,
                Comparison = 0,
                Name = "StatusREQ",
                IsOn = false,
                IsShown = false,
            }
            },
            { "AS6081", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DropDown,
                Comparison = 0,
                Name = "AS6081",
                IsOn = false,
                IsShown = false,
            }
            },
            { "IndustryType", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DropDown,
                Comparison = 0,
                Name = "IndustryType",
                IsOn = false,
                IsShown = false,
            }
            },
            { "HUBRFQ", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DateSelect,
                Comparison = 0,
                Name = "HUBRFQ",
                IsOn = false,
                IsShown = false,
            }
            },
            { "TotalValue", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.Numerical,
                Comparison = 0,
                Name = "TotalValue",
                IsOn = false,
                IsShown = false,
            }
            },
            { "ClientName", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DropDown,
                Comparison = 0,
                Name = "ClientName",
                IsOn = false,
                IsShown = false,
            }
            }
        };

        public static readonly Func<KeyValuePair<string, DataListNuggetFilterStateModel>, IReadOnlyDictionary<string, DataListNuggetFilterStateModel>, DataListNuggetFilterStateModel> CustomerRequirementMapper = (x, requestedList) =>
        {
            var existKey = requestedList.TryGetValue(x.Key, out DataListNuggetFilterStateModel value);

            return new DataListNuggetFilterStateModel()
            {
                Comparison = existKey ? value!.Comparison : x.Value.Comparison,
                Value = x.Key == "RecentOnly" ? "true" : (existKey ? value!.Value : x.Value.Value),
                FieldType = x.Value.FieldType,
                IsOn = x.Key == "RecentOnly" || (existKey ? value!.IsOn : x.Value.IsOn),
                IsShown = x.Key == "RecentOnly" || (existKey ? value!.IsShown : x.Value.IsShown),
                Name = x.Value.Name
            };
        };

        //HURFQ
        public static readonly IReadOnlyDictionary<string, DataListNuggetFilterStateModel> BOMLines = new Dictionary<string, DataListNuggetFilterStateModel>()
        {
            { "Code", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "Code",
                IsOn = false,
                IsShown = false,
            }
            },
            { "Name", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "Name",
                IsOn = false,
                IsShown = false,
            }
            },
            { "Manufacturer", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "Manufacturer",
                IsOn = false,
                IsShown = false,
            }
            },
            { "Part", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "Part",
                IsOn = false,
                IsShown = false,
            }
            },
            { "AS6081Required", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DropDown,
                Comparison = 0,
                Name = "AS6081Required",
                IsOn = true,
                IsShown = true,
            }
            }
        };

        public static readonly Func<KeyValuePair<string, DataListNuggetFilterStateModel>, IReadOnlyDictionary<string, DataListNuggetFilterStateModel>, DataListNuggetFilterStateModel> BOMLinesMapper = (x, requestedList) =>
        {
            var existKey = requestedList.TryGetValue(x.Key, out DataListNuggetFilterStateModel value);

            return new DataListNuggetFilterStateModel()
            {
                Comparison = existKey ? value!.Comparison : x.Value.Comparison,
                Value = existKey ? value!.Value : x.Value.Value,
                FieldType = x.Value.FieldType,
                IsOn = existKey ? value!.IsOn : x.Value.IsOn,
                IsShown = existKey ? value!.IsShown : x.Value.IsShown,
                Name = x.Value.Name
            };
        };

        public static readonly IReadOnlyDictionary<string, DataListNuggetFilterStateModel> POQuote = new Dictionary<string, DataListNuggetFilterStateModel>()

        {

            { "Part", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "Part",
                IsOn = false,
                IsShown = false,
            }
            },
            { "Contact", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "Contact",
                IsOn = false,
                IsShown = false,
            }
            },
            { "CMName", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.TextBox,
                Comparison = 0,
                Name = "CMName",
                IsOn = false,
                IsShown = false,
            }
            },
            { "Salesman", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DropDown,
                Comparison = 0,
                Name = "Salesman",
                IsOn = false,
                IsShown = false,
            }
            },
            { "IncludeClosed", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.CheckBox,
                Comparison = 0,
                Name = "IncludeClosed",
                IsOn = false,
                IsShown = false,
            }
            },
            { "POQuoteNo", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.Numerical,
                Comparison = 0,
                Name = "POQuoteNo",
                IsOn = false,
                IsShown = false,
            }
            },
            { "DatePOQuotedFrom", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DateSelect,
                Comparison = 0,
                Name = "DatePOQuotedFrom",
                IsOn = false,
                IsShown = false,
            }
            },
            { "DatePOQuotedTo", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.DateSelect,
                Comparison = 0,
                Name = "DatePOQuotedTo",
                IsOn = false,
                IsShown = false,
            }
            },          
            { "RecentOnly", new DataListNuggetFilterStateModel()
            {
                FieldType = FieldType.CheckBox,
                Comparison = 0,
                Name = "RecentOnly",
                Value = "true",
                IsOn = true,
                IsShown = true,
            }
            }
        };

        public static readonly Func<KeyValuePair<string, DataListNuggetFilterStateModel>, IReadOnlyDictionary<string, DataListNuggetFilterStateModel>, DataListNuggetFilterStateModel> POQuoteMapper = (x, requestedList) =>
        {
            var existKey = requestedList.TryGetValue(x.Key, out DataListNuggetFilterStateModel value);

            return new DataListNuggetFilterStateModel()
            {
                Comparison = existKey ? value!.Comparison : x.Value.Comparison,
                Value = x.Key == "RecentOnly" ? "true" : (existKey ? value!.Value : x.Value.Value),
                FieldType = x.Value.FieldType,
                IsOn = x.Key == "RecentOnly" || (existKey ? value!.IsOn : x.Value.IsOn),
                IsShown = x.Key == "RecentOnly" || (existKey ? value!.IsShown : x.Value.IsShown),
                Name = x.Value.Name
            };
        };

        public static (IReadOnlyDictionary<string, DataListNuggetFilterStateModel>, Func<KeyValuePair<string, DataListNuggetFilterStateModel>, IReadOnlyDictionary<string, DataListNuggetFilterStateModel>, DataListNuggetFilterStateModel>) GetDefinedDataListNugetState(DataListNuggets type)
        {
            return type switch
            {
                DataListNuggets.Todo => throw new NotImplementedException(),
                DataListNuggets.AllStock => throw new NotImplementedException(),
                DataListNuggets.CommunicationLog => throw new NotImplementedException(),
                DataListNuggets.Companies => throw new NotImplementedException(),
                DataListNuggets.Contacts => throw new NotImplementedException(),
                DataListNuggets.Credits => throw new NotImplementedException(),
                DataListNuggets.CRMAs => throw new NotImplementedException(),
                DataListNuggets.CRMAsReceive => throw new NotImplementedException(),
                DataListNuggets.CustomerRequirements => (CustomerRequirements, CustomerRequirementMapper),
                DataListNuggets.Debits => throw new NotImplementedException(),
                DataListNuggets.GoodsIn => throw new NotImplementedException(),
                DataListNuggets.InvoiceLines => throw new NotImplementedException(),
                DataListNuggets.Invoices => throw new NotImplementedException(),
                DataListNuggets.Lots => throw new NotImplementedException(),
                DataListNuggets.Manufacturers => throw new NotImplementedException(),
                DataListNuggets.PurchaseOrders => throw new NotImplementedException(),
                DataListNuggets.PurchaseOrdersReceive => throw new NotImplementedException(),
                DataListNuggets.PurchaseRequisitions => throw new NotImplementedException(),
                DataListNuggets.Quotes => throw new NotImplementedException(),
                DataListNuggets.ReceivedPurchaseOrders => throw new NotImplementedException(),
                DataListNuggets.SalesOrders => throw new NotImplementedException(),
                DataListNuggets.SalesOrdersShip => throw new NotImplementedException(),
                DataListNuggets.Services => throw new NotImplementedException(),
                DataListNuggets.SRMAs => throw new NotImplementedException(),
                DataListNuggets.SRMAsShip => throw new NotImplementedException(),
                DataListNuggets.SupplierInvoice => throw new NotImplementedException(),
                DataListNuggets.NPR => throw new NotImplementedException(),
                DataListNuggets.CustomerRequirementsPrint => throw new NotImplementedException(),
                DataListNuggets.BOM => throw new NotImplementedException(),
                DataListNuggets.InternalPurchaseOrders => throw new NotImplementedException(),
                DataListNuggets.PurchaseQuotes => (POQuote, POQuoteMapper),
                DataListNuggets.ClientInvoice => throw new NotImplementedException(),
                DataListNuggets.BOMLines => (BOMLines, BOMLinesMapper),
                DataListNuggets.CustomerRequirementsBOMImport => throw new NotImplementedException(),
                DataListNuggets.IHSCatalogue => throw new NotImplementedException(),
                DataListNuggets.ShortShipment => throw new NotImplementedException(),
                DataListNuggets.OGELLines => throw new NotImplementedException(),
                DataListNuggets.BOMManagerLines => throw new NotImplementedException(),
                _ => throw new NotImplementedException(),
            };
        }
    }
}
