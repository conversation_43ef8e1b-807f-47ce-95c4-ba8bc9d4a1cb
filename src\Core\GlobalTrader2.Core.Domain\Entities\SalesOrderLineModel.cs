﻿namespace GlobalTrader2.Core.Domain.Entities
{
    public class SalesOrderLineModel
    {
        public int SalesOrderLineId { get; set; } = 0;
        public int SalesOrderNo { get; set; } = 0;
        public int? SalesOrderNumber { get; set; }
        public string? FullPart { get; set; }
        public string? Part { get; set; }
        public int? ManufacturerNo { get; set; }
        public string? ManufacturerName { get; set; }
        public int? RestrictedMfrNo { get; set; }
        public bool? RestrictedMfrInactive { get; set; }
        public bool Inactive { get; set; }
        public bool IsOrderViaIPOonly { get; set; }
        public bool IsRestrictedProduct { get; set; }
        public string? DateCode { get; set; }
        public int? PackageNo { get; set; }
        public int ClientNo { get; set; }
        public string? PackageName { get; set; }
        public string? PackageDescription { get; set; }
        public string? Packing { get; set; }
        public string? PackagingSize { get; set; }
        public string? Descriptions { get; set; }
        public string? IHSProduct { get; set; }
        public string? ECCNCode { get; set; }
        public int Quantity { get; set; } = 0;
        public double? Price { get; set; } = 0;
        public double? AveragePrice { get; set; }
        public double? ProductDutyRate { get; set; }
        public double? Cost { get; set; }
        public DateTime? DatePromised { get; set; }
        public DateTime? RequiredDate { get; set; }
        public string? CurrencyCode { get; set; }
        public string? ProductDutyCode { get; set; }
        public string? MSLLevel { get; set; }
        public string? ContractNo { get; set; }
        public string IHSECCNCodeDefination { get; set; } = string.Empty;
        public string StockAvailableDetail { get; set; } = string.Empty;
        public string Taxable { get; set; } = string.Empty;
        public int QuantityAllocated { get; set; }
        public int QuantityShipped { get; set; }
        public int BackOrderQuantity { get; set; }
        public string? Instructions { get; set; }
        public int? ProductNo { get; set; }
        public string? ProductName { get; set; }
        public string? ProductDescription { get; set; }
        public string? CustomerPart { get; set; }
        public bool Posted { get; set; }
        public bool AS6081 { get; set; }
        public bool IsProdHazardous { get; set; }
        public bool? PrintHazardous { get; set; }
        public bool ShipASAP { get; set; }
        public bool SourcingResultUsedByOther { get; set; }
        public bool? IsIPO { get; set; }
        public int? StockNo { get; set; }
        public int? SOSerialNo { get; set; }
        public int? QuoteLineNo { get; set; }
        public DateTime? DLUP { get; set; }
        public DateTime? PODeliveryDate { get; set; }
        public DateTime? DateConfirmed { get; set; }
        public int? UpdatedBy { get; set; }
        public byte? ROHS { get; set; }
        public byte? ProductSource { get; set; }
        public int? CustomerRequirementId { get; set; }
        public int? CustomerRequirementNumber { get; set; }
        public int IsIPOCreated { get; set; }
        public int IsIPOAndPOOpen { get; set; }
        public int CompanyNo { get; set; }
        public string? CompanyName { get; set; }
        public string? ProductSourceName { get; set; }
        public string? IHSCountryOfOrigin { get; set; }
        public string? LifeCycleStage { get; set; }
        public string? HTSCode { get; set; }
        public bool? OnStop { get; set; }
        public bool ProductInactive { get; set; }
        public string? LineNotes { get; set; }
        public int? ServiceNo { get; set; }
        public int? SOSerialNumber { get; set; }
        public bool ServiceShipped { get; set; }
        public bool Closed { get; set; }
        public string? WarningMessage { get; set; }
        public string? ECCNMessage { get; set; }
        public string? ECCNSubject { get; set; }
        public bool? ECCNClientNotify { get; set; }
    }
}
