@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.CustomerRequirement.Components.AdvancedPartNoSearch
@using Microsoft.AspNetCore.Http
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer

@inject IViewLocalizer _localizer
@inject SessionManager _sessionManager

@{
    var colControlSize = 4;
    var colLabelSize = 6;
    var colInputSize = 12 - colLabelSize;
    var clientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty;
    var maxInt32 = Int32.MaxValue;
}
<div class="mb-2">
    <span id="add-edit-form-message">@_localizer["Enter the detail of the new Requirement"]</span>
    <strong> @_commonLocalizer["Save"]</strong>
</div>

<form method="post" id="requirement-select-company-form">
    <div class="form-error-summary" style="display: none;">
        <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
        <div>
            <p>@_messageLocalizer["There were some problems with your form."]</p>
            <p>@_messageLocalizer["Please check below and try again."]</p>
        </div>
    </div>
    <div class="row flex-column border-bottom">
        <div class="form-control-wrapper align-items-center col-@colControlSize">
            <div class="row align-items-center">
                <label for="customer" class="form-label fw-bold col-@colLabelSize">
                    <p class="mb-0">@_localizer["Customer"]</p>
                </label>
                <div id="customer" class="col-@colInputSize"></div>
            </div>
        </div>
        <div class="form-control-wrapper align-items-center col-@colControlSize">
            <div class="row align-items-center">
                <label for="contact" class="form-label fw-bold col-@colLabelSize">
                    <p class="mb-0">@_localizer["Contact"]<span class="required"> *</span></p>
                </label>
                <div id="contactLbl" class="col-@colInputSize" style="display: none"></div>
                <div class="col-@colInputSize" id="contact-container">
                    <select class="form-select" aria-label="agencySO" id="contact-dropdown" data-bind-name="contact">
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-@colControlSize">
            <div class="row align-items-center">
                <label for="requirement-for-traceability" class="form-label fw-bold col-6" style="padding-right:0px">
                    <p class="mb-0">
                        @_localizer["Requirement for Traceability"]<span class="required"> *</span>
                    </p>
                </label>
                <div class="col-6">
                    <select class="form-select" aria-label="agencySO" data-bind-name="requirementForTraceability" name="requirementForTraceability"
                            id="requirement-for-traceability-dropdown">
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-@colControlSize">
            <div class="row align-items-center">
                <label for="salesperson" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["Salesperson"]<span class="required"> *</span>
                    </p>
                </label>
                <div class="col-6">
                    <select class="form-select" aria-label="agencySO" data-bind-name="salesman" name="salesman" id="salesperson-dropdown">
                    </select>
                </div>

            </div>
        </div>
    </div>
    <div class="row align-items-center border-bottom">
        <div class="col-8 border-end">
            <div class="row border-bottom align-items-center">
                <div class="form-control-wrapper align-items-center col-6">
                    <div class="row align-items-center">
                        <label for="quantity" class="form-label fw-bold col-6">
                            <p class="mb-0">
                                @_localizer["Quantity"]<span class="required"> *</span>
                            </p>
                        </label>
                        <div class="col-6">
                            <input type="text" class="form-control form-input" name="quantity" id="quantity" data-input-type="numeric" data-input-format="int" data-bind-name="quantity" data-input-min="0" data-input-max="@maxInt32" data-input-type-allow-empty="true">
                        </div>
                    </div>
                </div>
                <div class="form-control-wrapper align-items-center col-6">
                    <div class="row align-items-center">
                        <label for="usage" class="form-label fw-bold col-6">
                            <p class="mb-0">
                                @_localizer["Usage"]
                            </p>
                        </label>
                        <div class="col-6">
                            <select class="form-select" aria-label="agencySO" id="usage-dropdown" data-bind-name="usage">
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row border-bottom align-items-center">
                <div class="form-control-wrapper align-items-center col-6">
                    <div class="row align-items-center">
                        <label for="type" class="form-label fw-bold col-6">
                            @_localizer["Type"]<span class="required"> *</span>
                        </label>
                        <div class="col-6">
                            <select class="form-select" aria-label="agencySO" id="type-dropdown" data-bind-name="type" name="type">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-control-wrapper align-items-center col-6">
                    <div class="row align-items-center">
                        <label for="estimated-annual-usage" class="form-label fw-bold col-6">
                            @_localizer["Estimated Annual Usage"]
                        </label>
                        <div class="col-6">
                            <input type="text" class="form-control form-input" name="estimated-annual-usage" id="estimated-annual-usage" maxlength="20" data-bind-name="estimatedAnnualUsage">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row border-bottom align-items-center">
                <div class="form-control-wrapper align-items-center col-6">
                    <div class="row align-items-center">
                        <label for="partial-quantity-acceptable" class="form-label fw-bold col-6">
                            @_localizer["Partial Quantity Acceptable"]
                        </label>
                        <div class="col-6">
                            <input type="checkbox" class="form-control form-check-input mt-0 p-0" name="partial-quantity-acceptable" id="partial-quantity-acceptable" data-bind-name="partialQuantityAcceptable">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row border-bottom align-items-center">
                <div class="form-control-wrapper align-items-center col-12">
                    <div class="row align-items-top">
                        <label for="partNo" class="form-label fw-bold col-3">
                            <div class="d-flex gap-1">
                                <p class="mb-0 h-20" id="lblPartNo">
                                    @_localizer["Part No"]<span class="required"> *</span>
                                </p>
                                <button type="button" id="partNoTextFilter" class="btn btn-primary toggle-btn w-40 justify-content-center ms-1"></button>
                            </div>
                        </label>
                        <div id="partNo-text-lbl" class="col-5" style="display: none"></div>

                        <div class="col-5 d-flex gap-2 align-items-top" id="partNo-container">
                            <span class="w-100">
                                <input type="text" class="form-control form-input" name="partNo" data-bind-name="partNo" id="partNumber">
                                <input type="hidden" name="partNoFilterTextValue" id="partNoFilterTextValue" value="">
                                <input type="hidden" name="partNoSelectValue" id="partNumberSelectValue" value="">
                                <div id="partNoSelectValue-error" class="invalid-feedback text-break"></div>
                            </span>
                            <button type="button" class="btn btn-primary h-20" id="advanced-part-no-search" onclick="openAdvancedPartNoSearch()">
                                <img src="~/img/icons/search-glass.svg" alt="search" width="18" height="18" />
                            </button>
                            <button type="button" class="btn btn-danger h-20" id="clear-part-no-value" onclick="clearIHSData()">
                                <span class="lh-base">Clear</span>
                            </button>
                        </div>
                        <div class="col-4">
                            <span class="badge bg-warning text-dark information-badge" data-bs-toggle="tooltip" data-bs-html="true" title="@_localizer["IHSInformation"]">IHS Information</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row border-bottom">
                <div class="form-control-wrapper align-items-center col-6">
                    <div class="row align-items-center">
                        <label for="inhouseAS6081" class="form-label fw-bold col-6">
                            @_localizer["inhouseAS6081"]<span class="required"> *</span>
                        </label>
                        <div class="col-6">
                            <select class="form-select" aria-label="inhouseAS6081" id="inhouse-as6081-dropdown" data-bind-name="as6081" name="as6081">
                                <option selected value="2">No</option>
                                <option value="1">Yes</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-control-wrapper align-items-center col-6">
                    <div class="row align-items-center">
                        <label for="suggested-Rebound-alternative" class="form-label fw-bold col-6">
                            @_localizer["suggestedReboundAlternative"]
                        </label>
                        <div class="col-6">
                            <input type="checkbox" class="form-control form-check-input mt-0 p-0" name="suggested-Rebound-alternative" id="suggested-Rebound-alternative" data-bind-name="suggestedReboundAlternative">
                        </div>
                    </div>
                </div>
                <div class="form-control-wrapper align-items-center col-6">
                    <strong id="ihsUnavaiableMessage" class="d-none" style="color: #FF0000">@_messageLocalizer["IHSServiceUnavailable"]</strong>
                </div>
            </div>
        </div>
        <!-- Cust Part No (Not complete) -->
        <div class="col-3">
            @{
                var checkboxes = new Dictionary<string, string>
            {
            { "alternativesAccepted", "Alternatives Accepted" },
            { "testingRequired", "Testing Required" },
            { "refurbsAcceptable", "Refurbs Acceptable" },
            { "factorySealed", "Factory Sealed" },
            { "orderToPlace", "Order To Place" },
            { "regularRepeatBusiness", "Regular/Repeat business" }
            };
            }

            @foreach (var item in checkboxes)
            {
                <div class="row align-items-center">
                    <div class="form-control-wrapper align-items-center col-12">
                        <div class="align-items-center d-flex justify-content-end gap-2">
                            <label for="@item.Key" class="form-label fw-bold">
                                <p class="mb-0">@_localizer[item.Value]</p>
                            </label>
                            <input type="checkbox" value="true" class="form-control form-check-input mt-0 p-0"
                                   name="@item.Key"
                                   id="@item.Key"
                                   data-bind-name="@item.Key">
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="manufacturer" class="form-label fw-bold col-6">
                    <span class="mb-0">
                        @_localizer["Manufacturer"]<span class="required"> *</span>
                    </span>
                </label>
                <div class="col-6">
                    <input type="text" class="form-control form-input" data-bind-name="manufacture" name="manufacture" id="manufacture" maxlength="300">
                    <input type="hidden" name="manufactureValue" id="manufactureValue" value=""
                           data-bind-name="manufactureValue">
                </div>
            </div>

        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="custPartNo" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["custPartNo"]
                    </p>
                </label>
                <div class="col-6">
                    <input type="text" class="form-control form-input" name="custPartNo" id="custPartNo" data-bind-name="custPartNo" data-auto-upper="true" maxlength="30">
                </div>
            </div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="targetPrice" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["targetPrice"]<span class="required"> *</span>
                    </p>
                </label>
                <div class="col-6 d-flex gap-2">
                    <span class="w-100">
                        <input type="text" class="form-control form-input" name="targetPrice" id="targetPrice"
                               data-number-force-positive="true"
                               data-number-force-positive-round-decimal-place="true"
                               data-number-value-default="0"
                               data-number-force-positive-decimal-place="5"
                               data-bind-name="targetPrice"
                               data-decimal-number-force-in-range="true"
                               min="0" max="2000000000">
                    </span>
                    <button type="button" class="btn btn-primary h-20" id="magicbox" onclick="openMagicBoxDialog()">
                        <img src="~/img/icons/magic-box.svg" alt="magicbox" width="12" height="12" />
                    </button>
                </div>
            </div>
        </div>
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="bestOffer" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["bestOffer"]
                    </p>
                </label>
                <div class="col-6">
                    <input type="text" class="form-control form-input" name="bestOffer" id="bestOffer"
                           data-number-force-positive="true"
                           data-number-force-positive-round-decimal-place="true"
                           data-number-value-default="0"
                           data-number-force-positive-decimal-place="5"
                           data-bind-name="bestOffer"
                           data-decimal-number-force-in-range="true"
                           min="0" max="2000000000">
                </div>
            </div>

        </div>
        <div class="col-4"></div>

        <!-- Currency -->
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="currency" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["currency"]<span class="required"> *</span>
                    </p>
                </label>
                <div class="col-6">
                    <select class="form-select" aria-label="agencySO" name="currency" data-bind-name="currency" id="currency-dropdown">
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="rohs" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["rohs"]
                    </p>
                </label>
                <div class="col-6">
                    <select class="form-select" aria-label="agencySO" data-bind-name="rohs" id="rohs-dropdown">
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="partStatus" class="form-label fw-bold col-6">
                    <span class="mb-0">
                        @_localizer["partStatus"]
                    </span>
                </label>
                <div class="col-6">
                    <div id="partStatus" data-bind-name="partStatus"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="dateCode" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["dateCode"]
                    </p>
                </label>
                <div class="col-6">
                    <input type="text" class="form-control form-input" name="dateCode" id="dateCode" data-bind-name="dateCode" maxlength="5" data-auto-upper="true">
                </div>
            </div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="product" class="form-label fw-bold col-6">
                    <span class="mb-0">
                        @_localizer["product"]<span class="required"> *</span>
                    </span>
                </label>
                <div class="col-6">
                    <input type="text" class="form-control form-input" data-bind-name="product" name="product" id="productInput">
                    <input type="hidden" name="productValue" id="productInputValue" value="" data-bind-name="productValue">
                </div>
            </div>
        </div>
        <div class="form-control-wrapper align-items-center col-2" id="ihsProduct" style="display: none;">
            <label for="lblIhsProduct" class="form-label fw-bold col-auto">
                <span class="me-2">
                    @_localizer["IHSProduct"]
                </span>
            </label>
            <div id="lblIhsProduct" class="d-inline" data-bind-name="lblIhsProduct"></div>
        </div>
        <div class="form-control-wrapper align-items-center col-2" id="htsCode" style="display: none;">
            <label for="lblHtsCode" class="form-label fw-bold col-auto">
                <span class="me-2">
                    @_localizer["HTSCode"]
                </span>
            </label>
            <div id="lblHtsCode" class="d-inline" data-bind-name="lblHtsCode"></div>
        </div>
        <div class="form-control-wrapper align-items-center col-2" id="duty" style="display: none;">
            <label for="lblDuty" class="form-label fw-bold col-auto">
                <span class="me-2">
                    @_localizer["Duty"]
                </span>
            </label>
            <div id="lblDuty" class="d-inline" data-bind-name="lblDuty"></div>
        </div>
        <div class="form-control-wrapper align-items-center col-2" id="coo" style="display: none;">
            <label for="lblCoo" class="form-label fw-bold col-auto">
                <span class="me-2">
                    @_localizer[" CountryOfOrigin"]
                </span>
            </label>
            <div id="lblCoo" class="d-inline" data-bind-name="lblCoo"></div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-6">
            <div class="row align-items-center d-flex">
                <label for="package" class="form-label fw-bold col-4">
                    <span class="mb-0">
                        @_localizer["package"]
                    </span>
                </label>
                <div class="col-4">
                    <input type="text" class="form-control form-input" name="package" id="packageInput" data-bind-name="package">
                    <input type="hidden" name="packageValue" id="packageInputValue" value="" data-bind-name="packageValue">
                </div>
                <div class="col-4" id="lblPackage" data-bind-name="lblPackage"></div>
            </div>
        </div>
        <!-- ECCN -->
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="eccnCode" class="form-label fw-bold col-4">
                    <p class="mb-0 highlight">
                        @_localizer["ECCN Code"]
                    </p>
                </label>
                <div class="col-6">
                    <input type="text" class="form-control form-input" data-bind-name="eccnCode" name="eccnCode" id="eccnCode">
                    <input type="hidden" name="eccnCodeValue" id="eccnCodeValue" value="" data-bind-name="eccnCodeValue">
                </div>
                <div class="col-2" id="lblEccnCode" data-bind-name="lblEccnCode"></div>
            </div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center d-flex">
                <label for="msl" class="form-label fw-bold col-6">
                    <span class="mb-0">
                        @_localizer["msl"]
                    </span>
                </label>
                <div class="col-4">
                    <select class="form-select" aria-label="msl" id="msl-dropdown" data-bind-name="msl"></select>
                </div>
                <div class="col-2" id="lblMsl" data-bind-name="lblMsl"></div>
            </div>
        </div><div class="col-8"></div>
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="deliDateRequired" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["deliDateRequired"]<span class="required"> *</span>
                    </p>
                </label>
                <div class="col-6">
                    <div class="ui-datepicker-custom d-flex gap-1">
                        <input type="text" data-type="datetime" class="form-control form-input" name="deliDateRequired" id="deliDateRequired" title="@_localizer["deliDateRequired"]" data-bind-name="deliDateRequired" readonly />
                    </div>
                </div>
            </div>
        </div>
        <div class="form-control-wrapper align-items-center col-8">
            <div class="col-4 lblDeliDateRequired" data-bind-name="lblDeliDateRequired" style="display: none"></div>
        </div>
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="rfqClosingDate" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["rfqClosingDate"]
                    </p>
                </label>
                <div class="col-6">
                    <div class="ui-datepicker-custom d-flex gap-1">
                        <input type="text" data-type="datetime" class="form-control form-input" name="rfqClosingDate" id="rfqClosingDate" title="@_localizer["rfqClosingDate"]" data-bind-name="rfqClosingDate" readonly />
                    </div>
                </div>
            </div>
        </div>
        <div class="form-control-wrapper align-items-center col-8">
            <div class="col-4 lblRfqClosingDate" data-bind-name="lblRfqClosingDate" style="display: none"></div>
        </div>
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="customerDecisionDate" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["customerDecisionDate"]
                    </p>
                </label>
                <div class="col-6">
                    <div class="ui-datepicker-custom d-flex gap-1">
                        <input type="text" data-type="datetime" class="form-control form-input" name="customerDecisionDate" id="customerDecisionDate" title="@_localizer["customerDecisionDate"]" data-bind-name="customerDecisionDate" readonly />
                    </div>
                </div>
            </div>
        </div>
         <div class="form-control-wrapper align-items-center col-8">
            <div class="col-4 lblCustomerDecisionDate" data-bind-name="lblCustomerDecisionDate" style="display: none"></div>
        </div>
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="quoteValidityRequired" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["quoteValidityRequired"]
                    </p>
                </label>
                <div class="col-6">
                    <select class="form-select" aria-label="agencySO" data-bind-name="quoteValidityRequired" name="quoteValidityRequired" id="quoteValidityRequired-dropdown">
                    </select>
                </div>
            </div>
        </div><div class="col-8"></div>
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="quoteValidityRequired" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["bom"]
                    </p>
                </label>
                <div class="col-6">
                    <input type="checkbox" class="form-control form-check-input mt-0 p-0"
                           name="bom" id="bom"
                           data-bind-name="bom">
                </div>
            </div>
        </div>
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="quoteValidityRequired" class="form-label fw-bold col-auto">
                    <p class="mb-0">
                        @_localizer["bomName"]
                    </p>
                </label>
                <div class="col-6">
                    <input type="text" class="form-control form-input" name="bomName" id="bomName" data-bind-name="bomName" maxlength="128">
                </div>
            </div>
        </div>
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="quoteValidityRequired" class="form-label fw-bold col-auto">
                    <p class="mb-0">
                        @_localizer["partWatch"]
                    </p>
                </label>
                <div class="col-auto">
                    <input type="checkbox" class="form-control form-check-input mt-0 p-0"
                           name="partWatch" id="partWatch"
                           data-bind-name="partWatch">
                </div>
            </div>
        </div>
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="hubrfq" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["hubrfq"]<span class="required" style="display: none"> *</span>
                    </p>
                </label>
                <div class="col-6">
                    <select class="form-select" aria-label="agencySO" name="hubrfq" data-bind-name="hubrfq" id="hubrfq-dropdown">
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-6">
            <div class="row">
                <label for="internalNotes" class="form-label fw-bold col-4">
                    <p class="mb-0">
                        @_localizer["internalNotes"]
                    </p>
                </label>
                <div class="col-8">
                    <textarea class="form-control form-textarea height-auto" style="height:auto" id="internalNotes" name="internalNotes" rows="2" maxlength="2000" data-directive="maxLength" data-bind-name="internalNotes"></textarea>
                </div>
            </div>
        </div>
        <div class="form-control-wrapper align-items-center col-6">
            <div class="row">
                <label for="internalNotes" class="form-label fw-bold col-4">
                    <p class="mb-0">
                        @_localizer["noteToCustomer"]
                    </p>
                </label>
                <div class="col-8">
                    <textarea class="form-control form-textarea height-auto" style="height:auto" id="noteToCustomer" name="noteToCustomer" rows="2" maxlength="2000" data-directive="maxLength" data-bind-name="noteToCustomer"></textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="row border-bottom">
        <div class="form-control-wrapper align-items-center col-4">
            <div class="row align-items-center">
                <label for="supportTeamMember" class="form-label fw-bold col-6">
                    <p class="mb-0">
                        @_localizer["supportTeamMember"]
                    </p>
                </label>
                <div class="col-6">
                    <input type="text" class="form-control form-input" name="supportTeamMember" id="supportTeamMember" data-bind-name="supportTeamMember">
                    <input type="hidden" name="supportTeamMemberValue" id="supportTeamMemberValue" value="" data-bind-name="supportTeamMemberValue">
                </div>
            </div>
        </div>
    </div>
    <div id="sendHubDialog" style="display:none">
        <div class="row border-bottom">
            <div class="fw-bold" style="font-size: 15px;padding-bottom: 12px; padding-top: 12px;background-color:#ffe9b0">
                @_localizer["Send this to Purchase Hub"]
            </div>
        </div>
        <div class="row border-bottom">
            <div class="form-control-wrapper align-items-center col-4">
                <div class="row align-items-center">
                    <label for="cloneAndSendHubAssignUserNo" class="form-label fw-bold col-6">
                        <p class="mb-0">
                            @_localizer["Buyer"]<span class="required"> *</span>
                        </p>
                    </label>
                    <div class="col-6">
                        <select class="form-select" aria-label="cloneAndSendHubAssignUserNo" name="cloneAndSendHubAssignUserNo" data-bind-name="cloneAndSendHubAssignUserNo">
                        </select>
                    </div>
                </div>
            </div><div class="col-8"></div>
        </div>
        <div class="row border-bottom">
            <div class="form-control-wrapper align-items-center col-4">
                <div class="row align-items-center">
                    <label for="cloneAndSendHubAryRecipientLoginLabel" class="form-label fw-bold col-6">
                        <span class="mb-0">
                            @_localizer["CC"]
                        </span>
                    </label>
                    <div class="col-6">
                        <input type="text" id="clone-and-send-purchase-hub-to-input-search" class="form-control form-input" />
                        <input type="text" id="clone-and-send-purchase-hub-to-input" data-bind-name="cloneAndSendHubAryRecipientLogin" name="cloneAndSendHubAryRecipientLogin" aria-label="To" hidden />
                    </div>
                </div>
            </div><div class="col-8"></div>
            <div class="form-control-wrapper align-items-center col-4">
                <div class="row align-items-center">
                    <label for="cloneAndSendHubDateRequired" class="form-label fw-bold col-6">
                        <p class="mb-0">
                            @_localizer["Quote Required"]<span class="required"> *</span>
                        </p>
                    </label>
                    <div class="col-6">
                        <div class="ui-datepicker-custom d-flex gap-1">
                            <input type="text" data-type="datetime" class="form-control form-input" name="cloneAndSendHubDateRequired" id="cloneAndSendHubDateRequired" title="@_localizer["dateRequired"]" data-bind-name="cloneAndSendHubDateRequired" readonly />
                        </div>
                    </div>
                </div>
            </div><div class="col-8"></div>
            <div class="form-control-wrapper align-items-center col-4">
                <div class="row align-items-center">
                    <label for="cloneAndSendHubContact2Id" class="form-label fw-bold col-6">
                        <p class="mb-0">
                            @_localizer["CC communication notes to"]
                        </p>
                    </label>
                    <div class="col-6">
                        <select class="form-select" aria-label="cloneAndSendHubContact2Id" name="cloneAndSendHubContact2Id" data-bind-name="cloneAndSendHubContact2Id">
                        </select>
                    </div>
                </div>
            </div><div class="col-8"></div>
        </div>

    </div>

</form>

<!-- Magic Box Dialog -->
@await Html.PartialAsync("~/Areas/Orders/Pages/HUBRFQ/Partials/_MagicBoxDialog.cshtml")

@await Component.InvokeAsync(nameof(AdvancedPartNoSearch))