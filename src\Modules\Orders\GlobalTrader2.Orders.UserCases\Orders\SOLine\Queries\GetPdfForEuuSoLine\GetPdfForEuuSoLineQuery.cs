﻿using GlobalTrader2.Core.Bases;
using GlobalTrader2.Dto.Manufacturers.Document;
using MediatR;

namespace GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetPdfForEuuSoLine
{
    public class GetPdfForEuuSoLineQuery : IRequest<BaseResponse<IEnumerable<DocumentDto>>>
    {
        public int? SalesOrderLineId { get; set; }
        public string? FileType { get; set; }
        public string? Culture { get; set; }
    }
}
