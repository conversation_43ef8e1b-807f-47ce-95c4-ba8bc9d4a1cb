@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SelectionMenu
@using GlobalTrader2.SharedUI.Constants
@using GlobalTrader2.SharedUI.Enums
@using GlobalTrader2.SharedUI.Helper
@using GlobalTrader2.SharedUI.Services
@using GlobalTrader2.SharedUI.ViewModels.Bases
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@using GlobalTrader2.Dto.SalesOrderLine

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IViewLocalizer _localizer
@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
}

<div id="end-user-undertaking-box" class="@sectionBoxClasses mb-3">
    <h3 class="@headerClasses">
        <span class="section-box-title">
            @_commonLocalizer["End User Undertaking Form"]
        </span>
        <span class="section-box-button-group" style="display: none">
            <span class="d-flex gap-2">
                <button class="btn btn-primary">
                    <span class="lh-base">
                        <span> @_localizer["Download EUU Form Template"]</span>
                    </span>
                </button>
            </span>
        </span>
    </h3>
    <div class="@contentClasses">
        <div id="end-user-undertaking-wrapper">
            <div class="table-container">
                <table id="end-user-undertaking-table" class="table simple-table display responsive nowrap">
                    <tr>
                        <th></th>
                    </tr>
                </table>
            </div>
            <div id="pdf-section-wrapper" style="display: none">
                <div id="pdf-document" class="mb-2 gap-2 d-flex">
                    <span class="fw-bold">@_commonLocalizer["PDF Document"]</span>
                    <span id="file-size"></span>
                 </div>
                <div id="loading"></div>
                <div id="content">
                    <div id="euu-no-data-message" class="text-muted" style="display: none">
                        @_messageLocalizer["This Sales Order Line does not have any End User Undertaking document"]
                    </div>
                    <div id="euu-list"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const euuLocalized = {
        maxFileSize :'@_commonLocalizer["Max Document Size"]'
    }
</script>