﻿using System.Reflection;
using System.Web;

namespace GlobalTrader2.SharedUI.Models
{
    public class NavigationItem
    {
        public int? ID { get; set; }
        public string Title { get; set; } = string.Empty;
        public string PageTitle { get; set; } = string.Empty;
        public string CtaUri { get; set; } = string.Empty;
        public string IconUri { get; set; } = string.Empty;
    }

    public class NavigationCompanyDetailParams
    {
        public int? cm { get; set; } //CompanyId
        public int? tab { get; set; } //TabPermission
        public int? clt { get; set; } //CompanyListType
        public int? con { get; set; } //ContactId
        public int? mfr { get; set; } //ManufacturerId
        public int? stk { get; set; } //StockId
        public string? cmn { get; set; } //CompanyName
        public string? ctn { get; set; } //ContactName
        public int? gi { get; set; } //GoodsInId
        public int? BOM { get; set; } //BOM
        public int? req { get; set; } //CustomerRequirement
        public int? so { get; set; } //SalesOrderId
        public int? prq { get; set; } //PurchaseRequisitionId (SalesOrderLineId actually)
        public string? scn { get; set; } //SearchCompanyName
        public string? pqt { get; set; } //PriceRequest
        public string ToQueryString()
        {
            var query = HttpUtility.ParseQueryString(string.Empty);

            foreach (PropertyInfo prop in typeof(NavigationCompanyDetailParams).GetProperties())
            {
                var value = prop.GetValue(this);

                if (value is string strValue && !string.IsNullOrWhiteSpace(strValue))
                {
                    query[prop.Name] = strValue;
                }
                else if (value is int intValue)
                {
                    query[prop.Name] = intValue.ToString();
                }
            }

            return query.Count > 0 ? "?" + query.ToString() : string.Empty;
        }
    }
}
