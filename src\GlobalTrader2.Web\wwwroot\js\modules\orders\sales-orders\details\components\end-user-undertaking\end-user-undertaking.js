﻿import { SectionBox } from '../../../../../../components/base/section-box.component.js?v=#{BuildVersion}#';
import { EventEmitter } from '../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#';
import { EuuPdfManager } from './euu-so-line-pdf-section.js?v=#{BuildVersion}#';

export class EndUserUndertakingManager extends EventEmitter {

    constructor(salesOrderId) {
        super()
        this._salesOrderId = salesOrderId;
        this.selectedSOLine = null;
        this.dataTable = null;
        this.sectionBox = null;
        this.pdfManager = new EuuPdfManager(salesOrderId);
    }

    async initialize() {
        this._setUpSectionBox();
        this._setUpDataTable();
        await this.pdfManager.initialize()

    }

    _setUpDataTable() {
        this.dataTable = $("#end-user-undertaking-table")
            .on('preXhr.dt', () => {
                this.sectionBox.loading(false)
            })
            .on('draw.dt', () => {
                this.sectionBox.stopLoading(false);
            })
            .ResizeDataTable({
                ajax: {
                    url: `/api/orders/sales-order-detail/${this._salesOrderId}/euu-lines`,
                    type: 'GET'
                },
                dataSrc: 'data',
                resizeConfig: {
                    numberOfRowToShow: 4
                },
                info: false,
                responsive: true,
                select: {
                    toggleable: false,
                    info: false,
                },
                language: {
                    emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                },
                paging: false,
                ordering: false,
                searching: false,
                scrollCollapse: true,
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                    $(thead).find('th').eq(0).addClass('text-start');
                },
                columnDefs: [
                    { targets: '_all', className: 'text-wrap text-break' },
                ],
                columns: [
                    {
                        data: 'soSerialNo',
                        title: 'Line No',
                        width: '5%',
                        render: (data, row) => {
                            return `
                                  <div class="text-center">
                                    <a class="text-decoration-none m-0" style="min-height: 15px;">${data}</a>
                                  </div>
                                `;
                        }
                    },
                    {
                       data: (row) => (
                            {
                                part: row.part,
                                customerPart: row.customerPart,
                            }
                        ),
                        name: 'partNo_customerPart',
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Part No</div>Customer Part`,
                        width: '30%',
                        render: (data) => {
                            let partNoText = "";
                            let customerPartText = "";
                            if (data.part) {
                                const escapedPartNoText = DataTable.render.text().display(data.part);
                                partNoText = GlobalTrader.StringHelper.setCleanTextValue(escapedPartNoText);
                            }
                            if (data.customerPart) {
                                const escapedCustomerPartText = DataTable.render.text().display(data.customerPart);
                                customerPartText = GlobalTrader.StringHelper.setCleanTextValue(escapedCustomerPartText);
                            }
                            return `<a>${partNoText}</a><div class="m-0" style="min-height: 15px;">${customerPartText}</div>`;
                        }
                    },
                    {
                        data: 'isECCNWarning',
                        title: 'EUU Form Required?',
                        width: '15%',
                        render: (data) => { 
                            const requiredText = data ? 'Yes' : 'No';
                            return `<a class="text-decoration-none" data-ogel-required="${data}">${requiredText}</a>`;
                        }
                    },
                    {
                        data: 'uploadDate',
                        title: 'Date Uploaded',
                        width: '10%',
                        render: (data) => {
                            return data;
                        }
                    },
                    {
                        data: 'euuupdFploadName',
                        title: 'Uploaded By',
                        width: '10%',
                        render: (data) => {
                            return GlobalTrader.StringHelper.setCleanTextValue(data);
                        }
                    },
                    {
                        data: (row) => ({
                            available: row.isEUUPDFAvailable,
                            required : row.isECCNWarning,

                        }),
                        title: 'Uploaded?',
                        width: '10%',
                        render: (data) => {
                            let image = null

                            if (!data.required) image = ``;
                            else if (!data.available) image = `<img src='/img/icons/upload-file.svg' width="22" height="22" style="filter: brightness(0.4);" data-bs-toggle="tooltip" data-bs-placement="top" title="Click to add doc">`
                            else image = `<img src='/img/icons/file-pdf.png' width="22" height="22" data-bs-toggle="tooltip" data-bs-placement="top" title="Click to view and add doc">`
                            return `<div style="display: flex; align-items: center;">${image}</div>`
                        }
                    }
                ],
                createdRow: (row, data, dataIndex) => {
                    if (data.inactive) {
                        $(row).addClass('text-light-gray');
                    }
                },
            }).on('select', async (e, dt, type, indexes) => {
                if (type === 'row') {
                    const selectedRow = dt.rows({ selected: true }).data()[0];
                    this.selectedSOLine = selectedRow;
                    this.pdfManager.updateSelectedRow(selectedRow)
                }
            });
    }

    _setUpSectionBox() {
        this.sectionBox = new SectionBox("#end-user-undertaking-box", {
            loadingContentId: 'end-user-undertaking-wrapper',
            loading: true
        });

        this.sectionBox.init();
        this.sectionBox.on('onRefreshed.msb', async () => {
            await this.refreshSectionBox();
        });
    }

    async refreshDataTable() {
        await GlobalTrader.Helper.reloadResizeDatatable(this.dataTable, null);
    }

    async refreshSectionBox() {
        if (this.dataTable) {
            this.sectionBox.loading(false);
            await this.refreshDataTable()
            this.pdfManager.updateSelectedRow(null)
            await this.pdfManager.refreshPdfSection()
            this.sectionBox.stopLoading(false);
        }
    }
}