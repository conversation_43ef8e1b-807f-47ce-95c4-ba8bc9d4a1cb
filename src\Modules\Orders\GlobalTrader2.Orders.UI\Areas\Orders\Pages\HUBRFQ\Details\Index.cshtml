﻿@page
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.CustomerRequirement.Components.AddNewHUBRFQComponent
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.Components.HUBRFQSourcingResults
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.Components.AutoSourcingHUBRFQItem
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.Components.HUBRFQItemDetail
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.Details
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.LiteDatatable;
@using GlobalTrader2.SharedUI.Areas.Containers.Pages.Shared.Components.SourcingSectionBox
@using GlobalTrader2.SharedUI.Helper
@using Microsoft.Extensions.Localization
@model IndexModel

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject SessionManager _sessionManager
@inject SettingManager _settingManager
<style>
    i[name="last-updated"] {
        font-size: 11px;
    }
    .highlight {
        background-color: yellow;
    }
    .client-name {
        font-size: 12px;
    }
    .status-text {
        font-size: 12px;
        color: #459E00;
    }
</style>
@{

    ViewData["Title"] = Model.BomDetails?.BOMName ?? "";
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
    var accordionHeaderIconClass = HtmlHelperExtensions.GetAccordionHeaderIconClass();
    var isPoHub = @_sessionManager.IsPOHub;
    var deletePartWatchSourcingResultTitle = isPoHub ? _commonLocalizer["Delete HUB PartWatch"] : _commonLocalizer["Delete PartWatch"];
    var sourcingResultsPartWatchMatchColumnTitle = isPoHub ? _commonLocalizer["HUB PartWatch Match"] : _commonLocalizer["PartWatch Match"];
    ViewData["TodoDialogTittle"] = isPoHub ? _commonLocalizer["Quote to Client"] : _commonLocalizer["Sourcing Results"];
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
}

<div id="HUBRFQ-details-container" class="mb-3 page-content-container hubrfq-page">
    <div class="d-flex justify-content-between align-items-start mt-2">
        <div>
            <span class="page-sub-title">@_commonLocalizer["HUBRFQ"]</span>
            @if (Model.BomDetails is not null)
            {
                <h2 id="hubrfq-primary-title" class="page-primary-title m-0">@Model.BomDetails.BOMName</h2>
                <span class="status-text">
                    <b class="me-2">Status</b> <span id="bom-details-status">@Model.BomDetails.Status</span>
                </span>
                @if (Model.Gsa.IsDifferentClient && !Model.Gsa.IsDMCC)
                {
                    <p><b class="client-name highlight">@Model.Gsa.ClientNameForDisplay</b></p>
                }
            }
        </div>
        <span class="flex-shrink-0">
            <a class="btn btn-primary" id="add-new-hub" href="/Orders/HUBRFQ/AddNewHUBRFQ">
                <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                <span class="lh-base">@_localizer["AddNewHUBRFQ"]</span>
            </a>
        </span>
    </div>
    <div class="mb-3" id="main-information-wrapper">
        <div id="main-information-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_localizer["Main Information"]</span>
                <span class="section-box-button-group gap-2 flex-wrap">
                    @if (Model.ShowEditBomButton)
                    {
                        <button class="btn btn-primary" id="main-information-edit-btn" title="Edit">
                            <img src="~/img/icons/plus.svg" alt="Edit" width="18" height="18" />
                            <span class="lh-base">@_commonLocalizer["Edit"]</span>
                        </button>
                    }
                    @if (Model.ShowDeleteBomButton)
                    {
                        <button class="btn btn-primary" id="delete-btn" style="display:none" title="Delete">
                            <img src="~/img/icons/plus.svg" alt="Delete" width="18" height="18" />
                            <span class="lh-base">@_commonLocalizer["Delete"]</span>
                        </button>
                    }
                    @if (Model.ShowExportCsvButton)
                    {
                        <button class="btn btn-primary" id="export-csv-btn" title="Export CSV">
                            <img src="~/img/icons/csv.svg" alt="Export" width="18" height="18" />
                            <span class="lh-base">@_commonLocalizer["Export"]</span>
                        </button>
                    }
                    @if (Model.ShowExportPurchaseHubButton)
                    {
                        <button class="btn btn-primary" id="send-purchase-hub-btn" type="button" title="Send to Purchase Hub" disabled>
                            <img src="~/img/icons/folder-open.png" alt="ExportToPurchaseHUB" width="14" height="12" style="height: 14px;" />
                            <span class="lh-base">@_localizer["ExportToPurchaseHUB"]</span>
                        </button>
                    }
                    @if (Model.ShowNotifyButton)
                    {
                        <button class="btn btn-primary" id="notify-btn" title="Notify" disabled>
                            <img src="~/img/icons/folder-open.png" alt="SendToSupplier" width="18" height="18" />
                            <span class="lh-base">@_localizer["SendToSupplier"]</span>
                        </button>
                    }
                    @if (Model.ShowReleaseBomButton)
                    {
                        <button class="btn btn-warning" id="release-btn" disabled title="Release All">
                            <img src="~/img/icons/release.svg" alt="ReleaseAll" width="18" height="18" style="height: 12px;"/>
                            <span class="lh-base">@_localizer["ReleaseAll"]</span>
                        </button>
                    }
                    @if (Model.ShowCloseBomButton)
                    {
                        <button class="btn btn-danger" id="close-btn" disabled title="Close">
                            <img src="~/img/icons/slash.svg" alt="Close" width="18" height="18" />
                            <span class="lh-base">@_localizer["Close"]</span>
                        </button>
                    }
                    @if (Model.ShowNoBidBomButton)
                    {
                        <button class="btn btn-primary" id="no-bid-btn" disabled title="@_localizer["NoBidAll"]">
                            <img src="~/img/icons/nobid.svg" alt="NoBidAll" width="18" height="18" />
                            <span class="lh-base">@_localizer["NoBidAll"]</span>
                        </button>
                    }
                    @if (Model.ShowAddNoteButton)
                    {
                        <button class="btn btn-primary" id="note-btn" title="Add New HUBRFQ Note">
                            <img src="~/img/icons/plus.svg" alt="Add New Communication Note" width="18" height="18" />
                            <span class="lh-base">@_localizer["Add New Communication Note"]</span>
                        </button>
                    }
                    @if (Model.ShowViewTreeButton)
                    {
                        <button class="btn btn-primary" id="view-tree-btn" title="View Tree">
                            <img src="~/img/icons/plus.svg" alt="View Tree" width="18" height="18" />
                            <span class="lh-base">@_localizer["View Tree"]</span>
                        </button>
                    }
                    @if (Model.ShowCrossMatchButton)
                    {
                        <button class="btn btn-primary" id="cross-match-btn" style="display:none;" title="Cross Match">
                            <img src="~/img/icons/plus.svg" alt="Cross Match" width="18" height="18" />
                            <span class="lh-base">@_localizer["Cross Match"]</span>
                        </button>
                    }
                </span>
            </h3>

            <div id="main-information-content" class="row @contentClasses">
                <div class="form-error-summary d-none mb-2 align-items-center">
                    <img src="/img/icons/x-octagon.svg" alt="X-icon">
                    <p class="m-0"></p>
                </div>
                <div class="col-4">
                    <div class="row">
                        <label for="code" class="col-3 form-label fw-bold">@_localizer["Code"]</label>
                        <span name="code" class="col-9 text-break"></span>
                    </div>
                    <div class="row">
                        <label for="name" class="col-3 form-label fw-bold">@_localizer["Name"]</label>
                        <span name="name" class="col-9 text-break"></span>
                    </div>
                    <div class="row">
                        <label for="quote-required" class="col-3 form-label fw-bold">@_localizer["Quote Required"]</label>
                        <span name="quote-required" class="col-9 text-break"></span>
                    </div>
                    <div class="row">
                        <label for="requested-by" class="col-3 form-label fw-bold">@_localizer["Requested by"]</label>
                        <span name="requested-by" class="col-9 text-break"></span>
                    </div>
                    <div class="row">
                        <label for="cc-communication-notes-to" class="col-3 form-label fw-bold">@_localizer["CC communication notes to"]</label>
                        <span name="cc-communication-notes-to" class="col-9 text-break"></span>
                    </div>
                </div>
                <div class="col-4">
                    <div class="row">
                        <label for="company" class="col-3 form-label fw-bold">@_localizer["Company"]</label>
                        <span name="company" class="col-9 text-break"></span>
                    </div>
                    <div class="row">
                        <label for="currency" class="col-3 form-label fw-bold">@_localizer["Currency"]</label>
                        <span name="currency" class="col-9 text-break"></span>
                    </div>
                    <div class="row">
                        <label for="contact" class="col-3 form-label fw-bold">@_localizer["Contact"]</label>
                        <span name="contact" class="col-9 text-break"></span>
                    </div>
                    <div class="row">
                        <label for="current-supplier" class="col-3 form-label fw-bold">@_localizer["Current Supplier"]</label>
                        <span name="current-supplier" class="col-9 text-break"></span>
                    </div>
                    <div class="mb-2 d-flex align-items-center">
                        <label for="source-of-supply" class="col-3 form-label fw-bold mb-0">@_localizer["Source of Supply Required"]</label>
                        <input type="checkbox" name="source-of-supply" class="form-check-input check-sm col-auto ms-2" disabled >
                    </div>
                    <div class="mb-2 align-items-center d-none">
                        <label for="is-from-pr-offer" class="col-3 form-label fw-bold mb-0">@_localizer["Is From PR Offer"]</label>
                        <input type="checkbox" name="is-from-pr-offer" class="form-check-input check-sm col-auto ms-2" disabled >
                    </div>
                    <div style="display: none;">
                        <label for="uploaded-by" class="col-3 form-label fw-bold">@_localizer["Uploaded By"]</label>
                        <span name="uploaded-by" class="col-9 text-break"></span>
                    </div>
                </div>
                <div class="col-4">
                    <div class="mb-2 d-flex align-items-center">
                        <label for="inactive" class="col-3 form-label fw-bold mb-0">@_localizer["Inactive?"]</label>
                        <input id="is-inactive" type="checkbox" name="inactive" class="form-check-input check-sm col-auto ms-2" disabled>
                    </div>
                    <div class="row">
                        <label for="released-by" class="col-3 form-label fw-bold">@_localizer["Released by"]</label>
                        <span name="released-by" class="col-9 text-break"></span>
                    </div>
                    <div class="row">
                        <label for="assign-to" class="col-3 form-label fw-bold">@_localizer["Assign To"]</label>
                        <span name="assign-to" class="col-9 text-break"></span>
                    </div>
                    <div class="row">
                        <label for="notes" class="col-3 form-label fw-bold">@_localizer["Notes"]</label>
                        <span name="notes" class="col-9 text-break"></span>
                    </div>
                    <div class="mb-2 d-flex align-items-center">
                        <label for="inhouse-as6081" class="col-3 form-label fw-bold mb-0">@_localizer["AS6081"]</label>
                        <span name="inhouse-as6081" class="col-auto ms-2 text-break"></span>
                    </div>
                    <div class="row">
                        <label for="purchasing-notes" class="col-3 form-label fw-bold">@_localizer["Purchasing Notes"]</label>
                        <span name="purchasing-notes" class="col-9 text-break"></span>
                    </div>
                </div>
                <div class="text-end">
                    <i name="last-updated"></i>
                </div>

            </div>
        </div>
    </div>


    <div class="mb-3" id="communication-note-wrapper">
        <div id="communication-note-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_localizer["Communication Note"]</span>
            </h3>

            <div id="communication-note-content" class="row @contentClasses">
                <table id="communication-note-table" class="table simple-table display responsive">
                    <tr>
                        <th></th>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    @if (Model.CanViewPPVQualification)
    {
        <div class="mb-3" id="bom-quality-wrapper">
            <div id="bom-quality-box" class="@sectionBoxClasses">
                <h3 class="@headerClasses">
                    <span class="@accordionHeaderIconClass"></span>
                    <span class="section-box-title">@_localizer["PPV/ BOM Qualification"]</span>
                        <span class="section-box-button-group gap-2 flex-wrap">
                        @if (Model.IsShowPPVQualificationButton)
                        {
                            <button class="btn btn-primary" id="bom-qualification-edit-btn" disabled>
                                <img src="~/img/icons/plus.svg" alt="Edit" width="18" height="18" />
                                <span class="lh-base">@_localizer["Add/Edit"]</span>
                            </button>

                            <span ID="lblPVVBOMIHS" runat="server" class="ihspartstatusdoc" title="@_localizer["NoQuestionPPVData"]" style="display: none;"></span>

                            <button class="btn btn-primary" id="bom-qualification-delete-btn" disabled>
                                <img src="~/img/icons/plus.svg" alt="Delete" width="18" height="18" />
                                <span class="lh-base">@_commonLocalizer["Delete"]</span>
                            </button>
                        }

                            <button class="btn btn-primary" id="bom-qualification-view-btn">
                                <img src="~/img/icons/plus.svg" alt="View" width="18" height="18" />
                                <span class="lh-base">@_commonLocalizer["View"]</span>
                            </button>
                        </span>
                </h3>

                <div id="bom-quality-content" class="row @contentClasses">
                    @await Component.InvokeAsync(nameof(LiteDatatable), new LiteDatatableModel("bom-qualification"))
                </div>
            </div>
        </div>
    }

    <div class="mb-3" id="hub-rfq-item-wrapper">
        <div id="hub-rfq-item-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_localizer["HUBRFQ Items"]</span>
                <span class="section-box-button-group gap-2 flex-wrap">
                    <button class="btn btn-primary" id="release-btn" disabled>
                        <img src="~/img/icons/plus.svg" alt="Release" width="18" height="18" />
                        <span class="lh-base">@_commonLocalizer["Release"]</span>
                    </button>

                    <button class="btn btn-primary" id="unrelease-btn" disabled>
                        <img src="~/img/icons/recall.svg" alt="ReCall" />
                        <span class="lh-base">@_commonLocalizer["ReCall"]</span>
                    </button>

                    <button class="btn btn-primary" id="add-btn">
                        <img src="~/img/icons/plus.svg" alt="Add" width="18" height="18" />
                        <span class="lh-base">@_commonLocalizer["Add"]</span>
                    </button>
                    
                    @if (Model.ShowDeleteBomButton)
                    {
                        <button class="btn btn-danger" id="hub-rfq-delete-btn" disabled>
                            <img src="~/img/icons/slash.svg" alt="Delete" width="18" height="18" />
                            <span class="lh-base">@_commonLocalizer["Delete"]</span>
                        </button>
                    }

                    <button class="btn btn-primary" id="nobid-btn">
                        <img src="~/img/icons/plus.svg" alt="NoBid" width="18" height="18" />
                        <span class="lh-base">@_localizer["No-Bid"]</span>
                    </button>

                    @if (Model.ShowRecallNoBidBomButton)
                    {
                        <button class="btn btn-primary" id="recallnobid-btn" disabled>
                            <img src="~/img/icons/plus.svg" alt="Recall No-Bid" width="18" height="18" />
                            <span class="lh-base">@_localizer["Recall No-Bid"]</span>
                        </button>
                    }

                    <button class="btn btn-primary" id="note-btn" disabled>
                        <img src="~/img/icons/plus.svg" alt="Add New Communication Note" width="18" height="18" />
                        <span class="lh-base">@_localizer["Add New Communication Note"]</span>
                    </button>

                    @if (Model.ShowApplyPartWatchButton)
                    {
                        <button class="btn btn-primary" id="applypartwatch-btn">
                            <img src="~/img/icons/plus.svg" alt="Apply Partwatch" width="18" height="18" />
                            <span class="lh-base">@_localizer["Apply Partwatch"]</span>
                        </button>
                    }
                    <button class="btn btn-primary" id="removepartwatch-btn">
                        <img src="~/img/icons/plus.svg" alt="Remove Partwatch" width="18" height="18" />
                        <span class="lh-base">@_localizer["Remove Partwatch"]</span>
                    </button>
                    @if (Model.CanImportExportSourcingResult && isPoHub)
                    {
                        <button class="btn btn-primary" id="hubimportsr-btn">
                            <img src="~/img/icons/upload-file.svg" alt="Import Sourcing Result" width="18" height="18" />
                            <span class="lh-base">@_localizer["Import Sourcing Result"]</span>
                        </button>
                        <button class="btn btn-primary" id="exporttoexcel-btn">
                            <img src="~/img/icons/excel.svg" alt="Export To Excel" width="18" height="18" />
                            <span class="lh-base">@_localizer["Export To Excel"]</span>
                        </button>
                    }
                </span>
            </h3>

            <div id="hub-rfq-items-content" class="@contentClasses">
                <div class="table-responsive">
                    <table id="hub-rfq-items-table" class="table simple-table display responsive nowrap">
                        <thead>
                        </thead>
                        <tbody>
                            <!-- Table data will be populated by DataTables -->
                        </tbody>
                    </table>
                </div>
                <div class="resize-handle d-none" data-resize-table="hub-rfq-items-table" id="customer-requirements-resize"></div>
                <!-- Render HUBRFQ item detail here -->
                @await Component.InvokeAsync(nameof(HUBRFQItemDetail), new HUBRFQItemDetail())
            </div>
        </div>

        <!-- Magic Box Dialog -->
        @await Html.PartialAsync("Partials/_MagicBoxDialog")
    </div>

    <!-- Customer Requirements Tab Template (Hidden, outside of content area) -->
    <div id="customer-requirements-tab-template" class="d-none">
        <div class="d-flex flex-column">
            <!-- Error summary above everything -->
            <div class="form-error-summary d-none mb-2 align-items-center text-danger bg-light border border-danger rounded p-2">
                <img src="/img/icons/x-octagon.svg" alt="Warning" class="me-2" style="width: 16px; height: 16px;">
                <p class="m-0 fw-bold"></p>
            </div>
            
            <!-- Tab and horizontal line container -->
            <div class="d-flex align-items-end mb-0">
                <div class="customer-req-tabs-wrapper" role="tablist">
                    <ul class="nav nav-tabs border-0 mb-0" id="customer-req-tab">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active px-3 py-2"
                                    id="customer-req-tab-btn"
                                    data-bs-toggle="tab"
                                    data-bs-target="#customer-req-content"
                                    type="button"
                                    role="tab"
                                    aria-controls="customer-req-content"
                                    aria-selected="true"
                                    style="cursor: default; border-radius: 0.25rem 0.25rem 0 0; white-space: nowrap;">
                                Customer Requirements
                            </button>
                        </li>
                    </ul>
                </div>
                <!-- Horizontal line extending to full width -->
                <div class="flex-grow-1" style="border-bottom: 1px solid #dee2e6; height: 1px; margin-bottom: 1px;"></div>
            </div>
        </div>
        
        <div class="tab-content border-0" id="customer-req-tab-content">
            <div class="tab-pane fade show active" id="customer-req-content" role="tabpanel" aria-labelledby="customer-req-tab-btn">
            </div>
        </div>
    </div>

    <div id="customer-requrement-sourcing-results-box" class="@sectionBoxClasses mb-3 d-none" data-loading="false">
        <h3 class="@headerClasses">
            <span class="section-box-title">
                @(isPoHub ? _localizer["Quote to Client"] : _localizer["Sourcing Results"])
            </span>
            <span id="sourcing-results-btn-group" class="section-box-button-group w-100">
                <span class="gap-2 button-line">
                    <span class="d-flex gap-2">
                        <button type="button" class="btn btn-primary" id="selectAllSourcingResultsButton">
                            <span class="badge bg-white text-primary top-0">0</span> @_localizer["Select All"]
                        </button>

                        @if (Model.CanAddSourcingResult)
                        {
                            <button class="btn btn-primary" id="addSourcingResult">
                                <img src="~/img/icons/plus.svg" alt="Add Sourcing Result" width="18" height="18" />
                                <span class="lh-base">@_commonLocalizer["Add"]</span>
                            </button>
                        }

                        @if (Model.CanEditSourcingResult)
                        {
                            <button class="btn btn-primary" id="editSourcingResult" onclick="" disabled="">
                                <img src="~/img/icons/edit-3.svg" alt="Edit" width="18" height="18" />
                                <span class="lh-base">@_commonLocalizer["Edit"]</span>
                            </button>
                        }
                        @if (Model.CanQuoteSourcingResult)
                        {
                            <button class="btn btn-primary" id="addSourcingResultQuote" disabled>
                                <img src="~/img/icons/plus.svg" alt="Quote" width="18" height="18" />
                                <span class="lh-base">@_commonLocalizer["Quote"]</span>
                            </button>
                        }
                        @if (Model.CanDeleteSourcingResult)
                        {
                            <button class="btn btn-primary" id="addSourcingResultQuote" disabled>
                                <img src="~/img/icons/plus.svg" alt="Quote" width="18" height="18" />
                                <span class="lh-base">@_commonLocalizer["Quote"]</span>
                            </button>
                        }
                        <button class="btn btn-danger" id="deleteSourcingResult" disabled>
                            <img src="~/img/icons/x-circle.svg" alt="Delete" width="18" height="18" />
                            <span class="lh-base">@_commonLocalizer["Delete"]</span>
                        </button>
                        @if (isPoHub)
                        {
                            <button class="btn btn-primary" id="releaseSourcingResult" disabled>
                                <img src="~/img/icons/confirmation.svg" alt="Release" />
                                <span class="lh-base">@_commonLocalizer["Release"]</span>
                            </button>
                        }
                        <button class="btn btn-danger" id="deleteSourcingResultPartWatch" disabled>
                            <img src="~/img/icons/x-circle.svg" alt="DeletePartWatch" width="18" height="18" />
                            <span class="lh-base">@deletePartWatchSourcingResultTitle</span>
                        </button>
                    </span>
                    <span>
                        <span id="part-detail-sourcing-results-header-message" class="section-error-summary fs-11 mt-2" style="display: none;">
                            <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
                            <span>
                                <span type="readonlyText" data-bind-name="error"></span>
                            </span>
                        </span>
                    </span>
                </span>
            </span>
        </h3>
        <div class="communication-log-type-table-content @contentClasses">
            <div id="customer-requirement-sourcing-results-table-message"></div>
            <div id="customer-requirement-sourcing-results-table-container">
                @await Component.InvokeAsync(nameof(HUBRFQSourcingResults), new HUBRFQSourcingResults())
            </div>
            @await Html.PartialAsync("Components/HUBRFQSourcingResults/_SourcingResultDetails")
        </div>
    </div>

    <span id="isPoHub" hidden>@isPoHub</span>
    <div id="auto-sourcing-box" class="@sectionBoxClasses mb-3" data-loading="false" style="display: none;">
        <h3 class="@headerClasses">
            <span class="section-box-title">
                @_localizer["Auto Sourcing"]
            </span>
        </h3>
        <div class="auto-sourcing-table-content @contentClasses">
            <div id="auto-sourcing-content-wrapper">
                <div class="row align-items-start">
                    <div class="col-6 fw-bold pt-4">
                        @_commonLocalizer["** Auto‑Sourcing is done on the basis of Manufacturer Match and Lowest Price"]
                    </div>
                    <div class="col-6">
                        <div class="row g-1 pt-2">
                            <span class="fw-bold col-3">
                                @_commonLocalizer["Global Marketplace:"]
                            </span>
                            <span class="col" id="lytica-data">
                                AV Price: 0   M/Leading: 0   Target: 0   P/Status: N/A
                            </span>
                        </div>
                        <div class="row g-1 pt-2">
                            <span class="fw-bold col-3">
                                @_commonLocalizer["IHS Data:"]
                            </span>
                            <span class="col" id="ihs-data">
                                AV Price: 0.00   P/Status: N/A
                            </span>
                        </div>
                        <div class="pt-2 pb-2 fw-bold pe-3 text-end">
                            Stock/Offer is within 12 months
                        </div>
                    </div>
                </div>
                <div id="auto-sourcing-table-container">
                    @await Component.InvokeAsync(nameof(AutoSourcingHUBRFQItem), new AutoSourcingHUBRFQItem())
                </div>
            </div>
        </div>
    </div>


    <div id="sourcing-box-wrapper" class="mb-3">
        <div id="sourcing-box" class="@sectionBoxClasses mb-3 d-none" data-loading="false">
            <h3 class="@headerClasses">
                <span class="section-box-title">
                    @_localizer["Sourcing"]
                </span>
            </h3>
            @await Component.InvokeAsync(nameof(SourcingSectionBox), new SourcingSectionBoxModel(pageType: SourcingPageType.HUBRFQDetails))
        </div>
    </div>



    <div class="mb-3" id="uploaded-documents-wrapper">
        <div id="uploaded-documents-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_localizer["Uploaded Documents"]</span>
            </h3>


            <div id="uploaded-documents-content" class="row @contentClasses">
            </div>
        </div>
    </div>


    <div class="mb-3" id="log-wrapper">
        <div id="log-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_localizer["Log"]</span>
            </h3>


            <div id="log-content" class="row @contentClasses">
            </div>
        </div>
    </div>


    <div class="mb-3" id="assignment-history-wrapper">
        <div id="assignment-history-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_localizer["Assignment History"]</span>
            </h3>
            <div id="assignment-history-content" class="@contentClasses">
                <table id="assignment-history-table" class="table simple-table display responsive">
                    <thead>
                        <tr>
                            <th></th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="dialog-container p-2 mb-3" id="send-to-suplier-dialog" title="@_localizer["Notification"]" style="display: none;">
    <div class="dialog-description">
        <span>@_localizer["Notify others of this HUBRFQ?"]</span>
    </div>
    <form method="post" id="send-to-suplier-hub-form" class="row common-form mt-2">
        <div class="form-error-summary" style="display: none;">
            <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
            <div>
                <p>@_messageLocalizer["There were some problems with your form."]</p>
                <p>@_messageLocalizer["Please check below and try again."]</p>
            </div>
        </div>
        <div class="form-control-wrapper m-0 py-0 px-1 mb-1">
            <div class="form-label fw-bold d-inline-block">@_localizer["To"]</div><span class="required">*</span>
            <input type="text" id="send-to-suplier-input-search" class="form-control form-input" placeholder="@_localizer["Add more suppliers"]" />
            <input type="text" id="send-to-suplier-input" name="To" aria-label="To" hidden />
        </div>
        <div class="form-control-wrapper m-0 py-0 px-1 mb-1">
            <div for="Subject" class="form-label fw-bold d-inline-block">@_localizer["Subject"]</div><span class="required">*</span>
            <div class="position-relative cc-send-purchase-hub">
                <input type="text" id="Subject" name="Subject" class="form-control form-input" data-bind-name="subject" maxlength="256" />
            </div>
        </div>
        <div class="form-control-wrapper m-0 py-0 px-1 mb-1">
            <div for="Message" class="form-label fw-bold d-inline-block">@_localizer["Message"]</div><span class="required">*</span>
            <div class="ui-datepicker-custom d-flex gap-1">
                <textarea type="text" class="form-control form-textarea height-auto" style="height:auto" name="Message" data-bind-name="message" rows="10" maxlength="300"></textarea>
            </div>
        </div>
    </form>
</div>
<div id="bom-qualification-dialog" class="dialog-container mb-3" title="@_localizer["Add_BOMQualification"]" style="overflow-x: hidden;display: none;">
    <div class="dialog-description">
        <div class="mb-3">
            <span id="bom-qualification-message">@_localizer["Add_BOMQualification_Msg"]</span>
            <strong> @_commonLocalizer["Save"]</strong>
        </div>
        <div class="form-error-summary" style="display: none;">
            <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
            <div>
                <p>@_messageLocalizer["There were some problems with your form."]</p>
                <p>@_messageLocalizer["Please check below and try again."]</p>
            </div>
        </div>
    </div>
    <form id="bom-qualification-form">
    </form>
</div>
<div id="bom-qualification-view-dialog" class="dialog-container mb-3" title="@_localizer["View_BOMQualification"]" style="overflow-x: hidden;display: none;">
    <form id="bom-qualification-view-form">
    </form>
</div>

<div id="bom-qualification-delete-dialog" class="dialog-container hidden" title="@_localizer["BomQualificationDelete"]">
    <div class="dialog-description">
        <span>@_localizer["BomQualificationDeleteDescription"]</span>
    </div>
    <form method="post" id="bom-qualification-delete-form">
    </form>
</div>

<div id="hubrfq-close-dialog" class="dialog-container" title="@_localizer["CloseTitle"]" style="display: none;">
    <div class="dialog-description">
        <span>@_localizer["CloseMessage"]</span><strong> @_localizer["HUBRFQ"]</strong>?
    </div>
    <form id="close-hubrfq-form" class="row common-form mt-2">
        <input type="hidden" name="id" value="@Model.BomId" />    
        </form>
</div>

<div id="bom-item-delete-dialog" class="dialog-container" title="@_localizer["DeleteTitle"]" style="display: none;">
    <div class="dialog-description">
        <span>@_localizer["DeleteMessage"]</span>
    </div>
    <form id="bom-item-delete-form" class="row common-form mt-2">
        <input type="hidden" name="bomId" />
        <input type="hidden" name="requirementId" />
    </form>
</div>

<div id="unrelease-confirm-dialog" class="dialog-container" title="@_localizer["UnReleaseConfirmationTitle"]" style="display: none;">
    <div class="dialog-description">
        <span>@_localizer["UnReleaseConfirmationMessage"]</span>
    </div>
    <form id="unrelease-confirm-form" class="row common-form mt-2">
        <input type="hidden" name="bomId" />
        <input type="hidden" name="requirementId" />
    </form>
</div>

<!-- PartWatch Confirmation Dialog -->
<div id="apply-partwatch-dialog" class="dialog-container" title="@_localizer["ApplyPartWatchForHUBIPO"]" style="display: none;">
    <div class="dialog-description">
        <div class="mb-2">
            <span id="apply-partwatch-form-message">@_localizer["ApplyPartWatchConfirmMessage"]</span><strong> @_localizer["ApplyPartWatch"]</strong>?
        </div>
        <div class="form-error-summary" style="display: none;">
            <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
            <div>
                <p>@_messageLocalizer["There were some problems with your form."]</p>
                <p>@_messageLocalizer["Please check below and try again."]</p>
            </div>
        </div>
    </div>
    <form id="apply-partwatch-form" class="row common-form mt-2">
        <input type="hidden" name="BomId" value="@Model.BomId" />
    </form>
</div>

<!-- edit Hubrfq form Confirmation Dialog -->
<div id="edit-hubrfq-dialog" class="dialog-container" title="@_localizer["EditHUBRFQtitle"]" style="display: none;  overflow-x: hidden;">
    <div class="mb-2 mt-2">
        <span id="add-edit-form-message">@_localizer["EditHUBRFQMessage"]</span>
        <strong>@_commonLocalizer["Save"]</strong>
    </div>

    <form method="post" id="add-hubrfq-form">
        <div class="form-error-summary" style="display: none;">
            <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
            <div>
                <p>@_commonLocalizer["There were some problems with your form."]</p>
                <p>@_commonLocalizer["Please check below and try again."]</p>
                <p id="hubrfq-validation-message" class='d-none'></p>
            </div>
        </div>
        @await Component.InvokeAsync(nameof(AddNewHubrfqForm))
    </form>
</div>
@await Html.PartialAsync("Partials/_HUBRFQDetail")
@await Html.PartialAsync("Partials/_ImportSourcing")
@await Html.PartialAsync("Partials/_RemoveOfferConfirmAutoSourcing")
@await Html.PartialAsync("Partials/_AddOfferConfirmAutoSourcing")
@await Html.PartialAsync("Partials/_SendToPurchaseHub")
@await Html.PartialAsync("Partials/_AddTodoItem")
@await Html.PartialAsync("Partials/_ReleaseAll")
@await Html.PartialAsync("Partials/_DeleteSourcingResult")
@await Html.PartialAsync("Partials/_DeleteHUBPartWatch")
@await Html.PartialAsync("Partials/_NoBidAll")
@await Html.PartialAsync("Partials/_RecallNoBid")
@await Html.PartialAsync("Partials/_KubAssistance")
@await Html.PartialAsync("Partials/_ReleaseSourcingResult")

<script>
    var stateValue = {
        id: '@Model.BomId',
        selectedRequirementDetails: {
            number:  null,
            id : null,
            closed: null,
        },
        customerRequirementDetailsId: "hub-rfq-items-table",
        sourcingResultsBox: "customer-requrement-sourcing-results-box"
    }
    const localizedTitles = {
        noData: "@_localizer["NoData"]",
        expediteNotes: "@_localizer["ExpediteNotes"]",
        reqNo: "@_localizer["ReqNo"]",
        dlup: "@_localizer["DLUP"]",
        employeeName: "@_localizer["EmployeeName"]",
        assignTo: "@_localizer["AssignTo"]",
        ccUserId: "@_localizer["CCUserId"]",
        sendToGroup: "@_localizer["SendToGroup"]",
        partNo: "@_localizer["Part No"]",
        mfr: "@_localizer["Mfr"]",
        dC: "@_localizer["DC"]",
        product: "@_localizer["Product"]",
        package: "@_localizer["Package"]",
        quantity: "@_localizer["Quantity"]",
        deliveryDate: "@_localizer["Delivery Date"]",
        buyPrice: "@_localizer["Buy Price"]",
        buyPriceInBase: "@_localizer["Buy Price in Base"]",
        unitSellPrice: "@_localizer["Unit Sell Price"]",
        priceRequest: "@_localizer["Price Request"]",
        required: "@_localizer["Required"]",
        targetPrice: "@_localizer["Target Price"]",
        salesperson: "@_localizer["Salesperson"]",
        alternative: "@_localizer["Alternative"]",
        possibleAlternative: "@_localizer["Possible Alternative"]",
        firmAlternative: "@_localizer["Firm Alternative"]",
        kindlyUpdateMsl: "@_localizer["Kindly update MSL"]",
        as6081ComplianceRequired: "@_localizer["This part requires AS6081 compliance, please ensure the appropriate supplier is chosen to fulfil this requirement."]",
        editMessage: "@_localizer["Edit Message"]",
        supplier: "@_localizer["Supplier"]",
        errorDelDateAndProduct: "@_localizer["errorDelDateAndProduct"]",
        documentNumber: "@_localizer["Document No"]",
        assignedTo: "@_localizer["Assigned To"]",
        assignedBy: "@_localizer["Assigned By"]",
        logDate: "@_localizer["Date"]",
        assignmentType: "@_localizer["Assignment Type"]",
        relatedQuotes: "@_localizer["Related Quotes"]",
        partNotes: "@_localizer["Notes"]",
        notes: "@_localizer["Notes"]",
        offered: "@_localizer["Offered"]",
        offeredBy: "@_localizer["By"]",
        status: "@_localizer["Status"]",
        unitPrice: "@_localizer["Unit Price"]",
        baseCurrency: "@_localizer["Base Currency"]",
        region: "@_localizer["Region"]",
        terms: "@_localizer["Terms"]",
        estShippingCost: "@_localizer["EST Shipping Cost"]",
        partWatchMatch: "@sourcingResultsPartWatchMatchColumnTitle",
        addAlternateMessage: "@_localizer["Add Alternate Message"]",
        editTitle: "@_localizer["Edit Title"]",
        addTitle: "@_localizer["Add Title"]",
        hubMessage: "@_localizer["Hub Message"]",
        hubRFQMessage: "@_localizer["HubRFQ Message"]",
        hubTitle: "@_localizer["Hub Title"]",
        hubRFQTitle: "@_localizer["HubRFQ Title"]",
        sourcingResultsEmptyMsg: "@_localizer["This Customer Requirement has no Sourcing Results"]",
        requirementTitleInQuickBrowse: "@_localizer["Req"]",
        req: "@_localizer["Req"]",
        sourceClient: "@_localizer["Source Client"]",
        toDoList: "@_localizer["To Do List"]",
        lastUpdated: "@_localizer["LastUpdated"]",
        selectRequirementsFirst: "@_localizer["Please select requirements first"]",
        partwatchAppliedSuccessfully: "@_localizer["PartWatch applied successfully"]",
        partwatchRemovedSuccessfully: "@_localizer["PartWatch removed successfully"]",
        errorApplyingPartwatch: "@_localizer["Error applying PartWatch"]",
        errorRemovingPartwatch: "@_localizer["Error removing PartWatch"]",
        noQuestionPPVData: "@_localizer["NoQuestionPPVData"]",
        customerPart: "@_localizer["Customer Part"]",
        customer: "@_localizer["Customer"]",
        msl: "@_localizer["MSL"]",
        factorySealed: "@_localizer["Factory Sealed"]",
        inhouseAs6081TestingRequired: "@_localizer["Inhouse AS6081 testing required"]",
        assignedTo: "@_localizer["Assigned To"]",
        hubPartWatch: "@_localizer["HUB PartWatch"]",
        pleaseSelectRowToDelete: "@_localizer["Please select a row to delete."]",
        pleaseSelectRowToUnrelease: "@_localizer["Please select a row to unrelease."]",
        noRowSelected: "@_localizer["No row selected"]",
        changesSavedSuccessfully: "@_localizer["Changes saved successfully"]",
        customerRequirements: "@_localizer["Customer Requirements"]",
        hubrfqItems: "@_localizer["HUBRFQ Items"]",
        customerRequirementsTabTemplateNotFound: "@_localizer["Customer Requirements tab template not found in HTML"]",
        sourcingResultsPriceIssue: "@_localizer["Sourcing Results having price issue kindly check and verify."]",
        failedToLoadHubrfqItemsData: "@_localizer["Failed to load HUBRFQ Items data"]",
        subject: "@_localizer["Subject"]",
        message: "@_localizer["Message"]",
        noDataHUBRFQItems: "@_localizer["NoDataHUBRFQItems"]",
        yes: "@_commonLocalizer["Yes"]",
        no: "@_commonLocalizer["No"]",
        cancel: "@_commonLocalizer["Cancel"]",
        errorEditMessage : "@_localizer["Error Edit Message"]",
        hubrfqDuplicateName : "@_localizer["hubrfqDuplicateName"]",
        save : "@_localizer["Save"]",
        addToDoItem : "@_localizer["addToDoItemTitle"]",
    };

    const localizedSourcingResultDetails = {
        yes: "@_localizer["Yes"]",
        no: "@_localizer["No"]",
        days: "@_localizer["days"]",
    };

    const userContext = {
        isGSAUser: @(Model.Gsa.IsGSAUser.ToString().ToLower()),
        isDifferentClient: @(Model.Gsa.IsDifferentClient.ToString().ToLower()),
        gsaUserHasEditPermission: @(Model.Gsa.HasEditPermission.ToString().ToLower()),
        isDMCC: @(Model.Gsa.IsDMCC.ToString().ToLower()),
        belongToGSA: @(Model.Gsa.BelongToGSA.ToString().ToLower())
    };
    var localizedKubAssistance = {
        readMore: "@_localizer["Read more"]",
        readLess: "@_localizer["Read less"]",
        noQuoteFound: "@_localizer["No Quote found"]",
        quoteNumber: "@_localizer["Quote Number"]",
        quoteQuantity: "@_localizer["Quantity"]",
        quotePrice: "@_localizer["KubQuotePrice"]",
        quoteIsConvertedToSO: "@_localizer["Converted to SO"]",
        noPurchaseFound: "@_localizer["No purchase found"]",
        topBuyPricePOIPO: "@_localizer["PO/IPO"]",
        topBuyPriceDate: "@_localizer["Date"]",
        topBuyPriceDate: "@_localizer["Price"]",
        cusReqNumber: "@_localizer["HUBRFQ Number"]",
        cusReqQuantity: "@_localizer["Quantity"]",
        cusReqPrice: "@_localizer["Customer Target Price"]",
        cusReqQuote: "@_localizer["kubRequestQuote"]",
        noOfferfound: "@_localizer["No Offer found"]",
        stockDateAdded: "@_localizer["Date Added"]",
        stockQuantity: "@_localizer["Quantity"]",
        stockPrice: "@_localizer["Price"]",
        stockManufacturer: "@_localizer["Manufacturer"]",
        stockStockId: "@_localizer["Stock ID"]",
        noStockFound: "@_localizer["No Stock found"]",
        stockClient: "@_localizer["Client"]",
    };

    const localizedErrorMessage = {
        deliveryDateAndProduct: "@_localizer["Validate_Release_Message"]",
    };

    
    const sendToSupplierLocalized = {
        body: "@_localizer["SendToSupplierBody"]"
    }
</script>
<script>window.isPoHub = @(isPoHub.ToString().ToLower());</script>

@section Scripts {
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
   
    <script src="@_settingManager.GetCdnUrl("/lib/jquery-validation/dist/jquery.validate.js")"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datetime-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/dropdown-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datatables-detail-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>

    <environment include="Development">
        <script src="/js/modules/orders/sourcing/components/auto-sourcing.js" type="module" asp-append-version="true"></script>
        <script src="/js/modules/orders/hubrfq/details/hubrfq-detail.js" type="module" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script src="/dist/js/orders-auto-sourcing.bundle.js" type="module" asp-append-version="true"></script>
        <script src="/dist/js/orders-hubrfq-detail.bundle.js" type="module" asp-append-version="true"></script>
    </environment>

    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/custom-input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/components/base/search-table-page-base.js")" asp-append-version="true"></script>

    <environment include="Development">
        <script type="module" src="/js/modules/orders/sourcing/sourcing.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script type="module" src="/dist/js/orders-sourcing.bundle.js" asp-append-version="true"></script>
    </environment>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/loading-spinner.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/html-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/form-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/number-validation.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/sort-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/dialog-custom-events.js")" asp-append-version="true"></script>    
    <script src="@_settingManager.GetCdnUrl("/js/widgets/resize-data-table-extensions.js")" asp-append-version="true"></script>
}
