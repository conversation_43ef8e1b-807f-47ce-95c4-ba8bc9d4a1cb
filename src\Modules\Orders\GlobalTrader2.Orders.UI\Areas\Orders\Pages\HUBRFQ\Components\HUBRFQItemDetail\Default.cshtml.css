﻿::deep .dt-layout-table {
  overflow: auto !important;
  height: 500px !important;
}

::deep .dt-layout-row {
  align-items: start !important;
}

span {
  font-size: 12px;
}

.hover-container {
  position: relative;
  display: inline-block;
}

.hover-content {
  display: none;
  position: absolute;
  top: 100%;
  right: 0%;
  padding-top: 10px;
  background-color: #f9f9f9;
  border: 1px solid #ccc;
  z-index: 1;
  white-space: nowrap;
  transform: translateX(50%);
}

.hover-content p {
  color: #0000ff;
  margin: 0;
  padding: 10px;
}

.hover-content strong {
  padding: 10px;
}

.hover-content p:hover {
  background-color: #dddddd;
}

.hover-container:hover .hover-content {
  display: block;
}

.hover-span {
  color: #ff6a00;
  cursor: pointer;
}

.extra-margin {
  margin-top: 15px;
  margin-bottom: 5px;
}

.row {
  margin-top: 0.3rem;
  line-height: 1.25;
}

span[name="noBid"] {
  font-size: 20px;
  color: red;
}
span[name="closed"] {
  font-size: 20px;
}

#magicbox {
  margin-top: -2px;
}
