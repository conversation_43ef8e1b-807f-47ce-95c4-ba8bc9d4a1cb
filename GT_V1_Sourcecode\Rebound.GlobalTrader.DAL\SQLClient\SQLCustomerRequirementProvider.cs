﻿/*

Marker     changed by      date         Remarks

[001]      Abhinav       31/05/2012     ESMS Ref:92 - Requirement Error - Urgent
[002]      <PERSON><PERSON> 23/09/2016     MailTemplate Data for Send to Purchase Hub from HUBRFQ and Requirement Main Info Page 
[003]      <PERSON><PERSON><PERSON>   10/07/2018     [REB-12515]: show the HUBRFQ link below the customer requirement number in the customer requirement nugget of the sourcing results
[004]      <PERSON><PERSON> 11/09/2018     add start date and end date for searching 
[005]      <PERSON><PERSON><PERSON>   15-Oct-2018    Export all result instead of single page on HUBRFQ.
[006]      <PERSON><PERSON><PERSON>   04-Dec-2018    [REB-13584]: Link Requirement to SO Line
[007]      <PERSON><PERSON> 07-Jan-2019    Client BOM Items Details functionality.
[008]      <PERSON><PERSON> 10-Jan-2019    Add Client BOM Items Details functionality.
[009]      <PERSON><PERSON> 11-Jan-2019    Update ClientBOMId Items Details functionality.
[010]      <PERSON><PERSON> 18-Mar-2019    Showing Records Processed and Records Remaining.
[011]      <PERSON><PERSON> 20-Mar-2019    Update BomNo with HUBRFQ BomId
[012]      <PERSON><PERSON><PERSON><PERSON> 14-July-2021  Add new function for the Part watch match
[013]      <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
[014]      <PERSON>     11/01/2022   (GTDP-240)  Add function to add warning message to show on all screens REQ - Quote - SO - PO related to ECCN field
[015]      Soorya Vyas     20/03/2023    RP-1019 Win32Exception excecution TimeOut issue
[016]      Tanbirakhtar    13/06/2023    RP-37  Filter Added for industry type and Also added industry type on grid of UI
[016]      Ravi Bhushan    29-08-2023    RP-2227  AS6081
[017]      Ravi Bhushan    19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages 
[018]      Devendra Singh  11-01-2024      RP-2727 Filter Added for Assign Group 
 */

using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlCustomerRequirementProvider : CustomerRequirementProvider
    {
        /// <summary>
        /// Count CustomerRequirement
        /// Calls [usp_count_CustomerRequirement_for_Client]
        /// </summary>
        public override Int32 CountForClient(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_count_CustomerRequirement_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                return (Int32)ExecuteScalar(cmd);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to count CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Count CustomerRequirement
        /// Calls [usp_count_CustomerRequirement_for_Company]
        /// </summary>
        public override Int32 CountForCompany(System.Int32? companyId, System.Boolean? includeClosed)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_count_CustomerRequirement_for_Company", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = companyId;
                cmd.Parameters.Add("@IncludeClosed", SqlDbType.Bit).Value = includeClosed;
                cn.Open();
                return (Int32)ExecuteScalar(cmd);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to count CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Count CustomerRequirement
        /// Calls [usp_count_CustomerRequirement_open_for_Company]
        /// </summary>
        public override Int32 CountOpenForCompany(System.Int32? companyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_count_CustomerRequirement_open_for_Company", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = companyId;
                cn.Open();
                return (Int32)ExecuteScalar(cmd);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to count CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }




        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_CustomerRequirement]
        /// </summary>
        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_CustomerRequirement]
        /// </summary>
        public override List<CustomerRequirementDetails> DataListNugget(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String contactSearch, System.String cmSearch, System.Int32? salesmanNo, System.Boolean? recentOnly, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.DateTime? datePromisedFrom, System.DateTime? datePromisedTo, System.Boolean? partWatch, System.String bomNameSearch, System.String bomCodeSearch, System.Double? totalLo, System.Double? totalHi, System.Int32? REQStatus, System.Int32? IndustryType, System.Boolean? AS6081, System.Int32? SelectedClientNo = null, System.Int32? SelectedLoginNo = null)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@ContactSearch", SqlDbType.NVarChar).Value = contactSearch;
                cmd.Parameters.Add("@CMSearch", SqlDbType.NVarChar).Value = cmSearch;
                cmd.Parameters.Add("@SalesmanNo", SqlDbType.Int).Value = salesmanNo;
                cmd.Parameters.Add("@RecentOnly", SqlDbType.Bit).Value = recentOnly;
                cmd.Parameters.Add("@IncludeClosed", SqlDbType.Bit).Value = includeClosed;
                cmd.Parameters.Add("@CustomerRequirementNoLo", SqlDbType.Int).Value = customerRequirementNoLo;
                cmd.Parameters.Add("@CustomerRequirementNoHi", SqlDbType.Int).Value = customerRequirementNoHi;
                cmd.Parameters.Add("@ReceivedDateFrom", SqlDbType.DateTime).Value = receivedDateFrom;
                cmd.Parameters.Add("@ReceivedDateTo", SqlDbType.DateTime).Value = receivedDateTo;
                cmd.Parameters.Add("@DatePromisedFrom", SqlDbType.DateTime).Value = datePromisedFrom;
                cmd.Parameters.Add("@DatePromisedTo", SqlDbType.DateTime).Value = datePromisedTo;
                cmd.Parameters.Add("@PartWatch", SqlDbType.Bit).Value = partWatch;
                cmd.Parameters.Add("@BOMNameSearch", SqlDbType.NVarChar).Value = bomNameSearch;
                cmd.Parameters.Add("@BOMCode", SqlDbType.NVarChar).Value = bomCodeSearch;
                cmd.Parameters.Add("@TotalLo", SqlDbType.Decimal).Value = totalLo;
                cmd.Parameters.Add("@TotalHi", SqlDbType.Decimal).Value = totalHi;
                cmd.Parameters.Add("@REQStatus", SqlDbType.Decimal).Value = REQStatus;
                //CodeStart[016]
                cmd.Parameters.Add("@IndustryName", SqlDbType.Int).Value = IndustryType;
                //CodeEnd[016]
                cmd.Parameters.Add("@AS6081", SqlDbType.Bit).Value = AS6081; //[017]
                cmd.Parameters.Add("@SelectedClientNo", SqlDbType.Int).Value = SelectedClientNo;
                cmd.Parameters.Add("@SelectedLoginNo", SqlDbType.Int).Value = SelectedLoginNo;


                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMId", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.TotalInBase = GetReaderValue_NullableDouble(reader, "TotalInBase", 0);
                    obj.TotalValue = GetReaderValue_NullableDouble(reader, "TotalValue", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", 0);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.REQStatusName = GetReaderValue_String(reader, "REQStatus", "");
                    obj.BomStatus = GetReaderValue_String(reader, "Status", "");
                    //CodeStart[016]
                    obj.IndustryName = GetReaderValue_String(reader, "IndustryName", "");
                    //CodeEnd[016]
                    obj.AS6081 = GetReaderValue_Boolean(reader, "AS6081", false); //[017]
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Delete CustomerRequirement
        /// Calls [usp_delete_CustomerRequirement]
        /// </summary>
        public override bool Delete(System.Int32? customerRequirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_CustomerRequirement]
        /// </summary>
        public override Int32 Insert(System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packing, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode,System.Int32? ECCNNo, System.Boolean? AS6081)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@ReceivedDate", SqlDbType.DateTime).Value = receivedDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@DatePromised", SqlDbType.DateTime).Value = datePromised;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@Instructions", SqlDbType.NVarChar).Value = instructions;
                cmd.Parameters.Add("@Shortage", SqlDbType.Bit).Value = shortage;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = contactNo;
                cmd.Parameters.Add("@UsageNo", SqlDbType.Int).Value = usageNo;
                cmd.Parameters.Add("@Alternate", SqlDbType.Bit).Value = alternate;
                cmd.Parameters.Add("@OriginalCustomerRequirementNo", SqlDbType.Int).Value = originalCustomerRequirementNo;
                cmd.Parameters.Add("@ReasonNo", SqlDbType.Int).Value = reasonNo;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@CustomerPart", SqlDbType.NVarChar).Value = customerPart;
                cmd.Parameters.Add("@Closed", SqlDbType.Bit).Value = closed;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@PartWatch", SqlDbType.Bit).Value = partWatch;
                cmd.Parameters.Add("@BOM", SqlDbType.Bit).Value = bom;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = BOMNo;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.Bit).Value = FactorySealed;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;

                cmd.Parameters.Add("@PartialQuantityAcceptable", SqlDbType.Bit).Value = PQA;
                cmd.Parameters.Add("@Obsolete", SqlDbType.Bit).Value = ObsoleteChk;
                cmd.Parameters.Add("@LastTimeBuy", SqlDbType.Bit).Value = LastTimeBuyChk;
                cmd.Parameters.Add("@RefirbsAcceptable", SqlDbType.Bit).Value = RefirbsAcceptableChk;
                cmd.Parameters.Add("@TestingRequired", SqlDbType.Bit).Value = TestingRequiredChk;
                cmd.Parameters.Add("@TargetSellPrice", SqlDbType.Float).Value = TargetSellPrice;
                cmd.Parameters.Add("@CompetitorBestOffer", SqlDbType.Float).Value = CompetitorBestOffer;
                cmd.Parameters.Add("@CustomerDecisionDate", SqlDbType.DateTime).Value = CustomerDecisionDate;
                cmd.Parameters.Add("@RFQClosingDate", SqlDbType.DateTime).Value = RFQClosingDate;
                cmd.Parameters.Add("@QuoteValidityRequired", SqlDbType.Int).Value = QuoteValidityRequired;
                cmd.Parameters.Add("@Type", SqlDbType.Int).Value = Type;
                cmd.Parameters.Add("@OrderToPlace", SqlDbType.Int).Value = OrderToPlace;
                cmd.Parameters.Add("@RequirementForTraceability", SqlDbType.Int).Value = RequirementforTraceability;
                cmd.Parameters.Add("@EAU", SqlDbType.NVarChar).Value = EAU;
                cmd.Parameters.Add("@AlternativesAccepted", SqlDbType.Bit).Value = AlternativesAccepted;
                cmd.Parameters.Add("@RepeatBusiness", SqlDbType.Bit).Value = RepeatBusiness;
                cmd.Parameters.Add("@SupportTeamMemberNo", SqlDbType.Int).Value = SupportTeamMemberNo;
                //IHS code start
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = CountryOfOriginNo;
                cmd.Parameters.Add("@LifeCycleStage", SqlDbType.NVarChar).Value = LifeCycleStage;
                cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = HTSCode;
                cmd.Parameters.Add("@AveragePrice", SqlDbType.Float).Value = AveragePrice;
                cmd.Parameters.Add("@Packing", SqlDbType.NVarChar).Value = Packing;
                cmd.Parameters.Add("@PackagingSize", SqlDbType.NVarChar).Value = PackagingSize;
                cmd.Parameters.Add("@Descriptions", SqlDbType.NVarChar).Value = Descriptions;
                cmd.Parameters.Add("@IHSPartsNo", SqlDbType.Int).Value = IHSPartsId;
                cmd.Parameters.Add("@IHSCurrencyCode", SqlDbType.NVarChar).Value = IHSCurrencyCode;
                cmd.Parameters.Add("@IHSProduct", SqlDbType.NVarChar).Value = IHSProduct;
                cmd.Parameters.Add("@ECCNCode", SqlDbType.NVarChar).Value = ECCNCode;
                cmd.Parameters.Add("@ECCNNo", SqlDbType.Int).Value = ECCNNo;
                cmd.Parameters.Add("@AS6081", SqlDbType.Bit).Value = AS6081; //[016]
                //IHS code end     
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@CustomerRequirementId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        //ihs
        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_APITokenNumber]
        /// </summary>
        public override Int32 InsertTokenNumberFromAPI(System.String TokenNumberInsert)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_APITokenNumber", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@IHSTokenNumber", SqlDbType.NVarChar).Value = TokenNumberInsert;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert API Token Number", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Insert
        /// Calls [usp_insert_InsertFromIHSApi]
        /// </summary>
        public override Int32 InsertFromIHSApi(System.Int32? clientNo, System.Int64? PartID, System.String prtNbr, System.String mfrName, System.String prtStatus, System.String prtDesc, System.String mfrFullName, System.String coo, System.String htsCd, System.String msl, System.String pckMethod, System.String pckCd, System.String source, System.Double price, System.String currency, System.String telephone, System.String email, System.String priceAvailabilityLink)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_InsertFromIHSApi", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@IHSId", SqlDbType.NVarChar).Value = PartID;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = prtNbr;
                cmd.Parameters.Add("@ManufacturerName", SqlDbType.NVarChar).Value = mfrName;
                cmd.Parameters.Add("@PartStatus", SqlDbType.NVarChar).Value = prtStatus;
                cmd.Parameters.Add("@Descriptions", SqlDbType.NVarChar).Value = prtDesc;
                cmd.Parameters.Add("@ManufacturerFullName", SqlDbType.NVarChar).Value = mfrFullName;
                cmd.Parameters.Add("@CountryOfOrigin", SqlDbType.NVarChar).Value = coo;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = msl;
                cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = htsCd;
                cmd.Parameters.Add("@Packaging", SqlDbType.NVarChar).Value = pckMethod;
                cmd.Parameters.Add("@PackageCode", SqlDbType.NVarChar).Value = pckCd;
                cmd.Parameters.Add("@ColPriceSource", SqlDbType.NVarChar).Value = source;
                cmd.Parameters.Add("@ColPrice", SqlDbType.NVarChar).Value = price;
                cmd.Parameters.Add("@ColPriceCurrency", SqlDbType.NVarChar).Value = currency;
                cmd.Parameters.Add("@ColPriceTelephone", SqlDbType.NVarChar).Value = telephone;
                cmd.Parameters.Add("@ColPriceEmail", SqlDbType.NVarChar).Value = email;
                cmd.Parameters.Add("@ColPricepriceAvailabilityLink", SqlDbType.NVarChar).Value = priceAvailabilityLink;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Insert From IHS Api", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Insert
        /// Calls [usp_insert_IHSApiXML]
        /// </summary>
        public override Int32 InsertIHSApiXML(System.Int32? clientNo, System.Int32? updatedBy, System.String strXMLData)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_IHSApiXML", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientId", SqlDbType.NVarChar).Value = clientNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.NVarChar).Value = updatedBy;
                cmd.Parameters.Add("@IHSResults", SqlDbType.Xml).Value = strXMLData;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RecordCount"].Value;

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Insert From IHS Api", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Insert
        /// Calls [usp_insert_IHSApiXML_BOMManager]
        /// </summary>
        public override List<PartDetails> InsertIHSApiXML_BOMManager(System.Int32? clientNo, System.Int32? updatedBy, System.String strXMLData)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                //DataTable dt = new DataTable();
                //dt.Columns.Add("Rowid");
                //dt.Columns.Add("PartName");
                //dt.Columns.Add("ItemFound");
                //dtCheckParts = dt;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_IHSApiXML_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientId", SqlDbType.NVarChar).Value = clientNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.NVarChar).Value = updatedBy;
                cmd.Parameters.Add("@IHSResults", SqlDbType.Xml).Value = strXMLData;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                //int ret = ExecuteNonQuery(cmd);
                //return (Int32)cmd.Parameters["@RecordCount"].Value;
                DbDataReader reader = cmd.ExecuteReader();// ExecuteReader(cmd);
                //IDataReader dr = cmd.ExecuteReader();
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.PartNameWithManufacture = GetReaderValue_String(reader, "PartName", "");
                    obj.PartName = GetReaderValue_String(reader, "Parts", "");
                    obj.ManufacturerNo = GetReaderValue_Int32(reader, "ManufacturerId", 0);
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ROHSNo = GetReaderValue_Int32(reader, "ROHS", 0);
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "CountryOfOrigin", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packaging", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackagingSize", "");
                    obj.IHSPartsId = GetReaderValue_Int32(reader, "IHSPartsId", 0);
                    obj.ResultType = GetReaderValue_String(reader, "ResultType", "");
                    obj.ROHSName = GetReaderValue_String(reader, "ROHSName", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.PartStatus = GetReaderValue_String(reader, "PartStatus", "");
                    obj.IHSCurrencyCode = GetReaderValue_String(reader, "ColPriceCurrency", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", 0);
                    obj.IHSProductDescription = GetReaderValue_String(reader, "IHSProductName", "");
                    obj.IHSDutyCode = GetReaderValue_String(reader, "IHSDutyCode", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    // obj.RowNum = GetReaderValue_NullableInt32(reader, "RowNum", 0);
                    obj.ManufacturerFullName = GetReaderValue_String(reader, "ManufacturerFullName", "");
                    obj.IHSProduct = GetReaderValue_String(reader, "IHSProduct", "");
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.ItemFound = GetReaderValue_String(reader, "ItemFound", "");
                    //[001] end
                    lst.Add(obj);
                    obj = null;



                }
                //if (reader.NextResult())
                //{
                //    while (reader.Read())
                //    {
                //        DataRow dr = dtCheckParts.NewRow();
                //        dr["Rowid"] = GetReaderValue_String(reader, "Rowid", "");
                //        dr["PartName"] = GetReaderValue_String(reader, "PartName", "");
                //        dr["ItemFound"] = GetReaderValue_String(reader, "ItemFound", "");
                //        dtCheckParts.Rows.Add(dr);
                //    }

                //}
                return lst;

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Insert From IHS Api", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //ihs

        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_CustomerRequirement_as_Alternate]
        /// </summary>
        //public override Int32 InsertAsAlternate(System.String customerRequirementName, System.Int32? customerRequirementNumber, System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName,
        //    System.Int32? salesmanno, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Boolean? FactorySealed, System.String MSL, System.Int32? BOMNo, System.Int32? RequirementforTraceability, System.String EAU)
        //{
        //    SqlConnection cn = null;
        //    SqlCommand cmd = null;
        //    try
        //    {
        //        cn = new SqlConnection(this.ConnectionString);
        //        cmd = new SqlCommand("usp_insert_CustomerRequirement_as_Alternate", cn);
        //        cmd.CommandType = CommandType.StoredProcedure;
        //        cmd.Parameters.Add("@CustomerRequirementName", SqlDbType.NVarChar).Value = customerRequirementName;
        //        cmd.Parameters.Add("@CustomerRequirementNumber", SqlDbType.Int).Value = customerRequirementNumber;
        //        cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
        //        cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
        //        cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
        //        cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
        //        cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
        //        cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
        //        cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
        //        cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
        //        cmd.Parameters.Add("@ReceivedDate", SqlDbType.DateTime).Value = receivedDate;
        //        cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
        //        cmd.Parameters.Add("@DatePromised", SqlDbType.DateTime).Value = datePromised;
        //        cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
        //        cmd.Parameters.Add("@Instructions", SqlDbType.NVarChar).Value = instructions;
        //        cmd.Parameters.Add("@Shortage", SqlDbType.Bit).Value = shortage;
        //        cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
        //        cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = contactNo;
        //        cmd.Parameters.Add("@UsageNo", SqlDbType.Int).Value = usageNo;
        //        cmd.Parameters.Add("@OriginalCustomerRequirementNo", SqlDbType.Int).Value = originalCustomerRequirementNo;
        //        cmd.Parameters.Add("@ReasonNo", SqlDbType.Int).Value = reasonNo;
        //        cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
        //        cmd.Parameters.Add("@CustomerPart", SqlDbType.NVarChar).Value = customerPart;
        //        cmd.Parameters.Add("@Closed", SqlDbType.Bit).Value = closed;
        //        cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
        //        cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
        //        cmd.Parameters.Add("@PartWatch", SqlDbType.Bit).Value = partWatch;
        //        cmd.Parameters.Add("@BOM", SqlDbType.Bit).Value = bom;
        //        cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;

        //        //[002] code start
        //        cmd.Parameters.Add("@SalesmanNo", SqlDbType.Int).Value = salesmanno;
        //        cmd.Parameters.Add("@PartialQuantityAcceptable", SqlDbType.Bit).Value = PQA;
        //        cmd.Parameters.Add("@Obsolete", SqlDbType.Bit).Value = ObsoleteChk;
        //        cmd.Parameters.Add("@LastTimeBuy", SqlDbType.Bit).Value = LastTimeBuyChk;
        //        cmd.Parameters.Add("@RefirbsAcceptable", SqlDbType.Bit).Value = RefirbsAcceptableChk;
        //        cmd.Parameters.Add("@TestingRequired", SqlDbType.Bit).Value = TestingRequiredChk;
        //        cmd.Parameters.Add("@AlternativesAccepted", SqlDbType.Bit).Value = AlternativesAccepted;
        //        cmd.Parameters.Add("@RepeatBusiness", SqlDbType.Bit).Value = RepeatBusiness;
        //        cmd.Parameters.Add("@CompetitorBestOffer", SqlDbType.Float).Value = CompetitorBestOffer;
        //        cmd.Parameters.Add("@CustomerDecisionDate", SqlDbType.DateTime).Value = CustomerDecisionDate;
        //        cmd.Parameters.Add("@RFQClosingDate", SqlDbType.DateTime).Value = RFQClosingDate;
        //        cmd.Parameters.Add("@QuoteValidityRequired", SqlDbType.Int).Value = QuoteValidityRequired;
        //        cmd.Parameters.Add("@Type", SqlDbType.Int).Value = Type;
        //        cmd.Parameters.Add("@OrderToPlace", SqlDbType.Bit).Value = OrderToPlace;
        //        cmd.Parameters.Add("@FactorySealed", SqlDbType.Bit).Value = FactorySealed;
        //        cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
        //        cmd.Parameters.Add("@BOMNum", SqlDbType.Int).Value = BOMNo;
        //        cmd.Parameters.Add("@RequirementforTraceability", SqlDbType.Int).Value = RequirementforTraceability;
        //        cmd.Parameters.Add("@EAU", SqlDbType.NVarChar).Value = EAU;

        //        //[002] code end
        //        cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Direction = ParameterDirection.Output;
        //        cn.Open();
        //        int ret = ExecuteNonQuery(cmd);
        //        return (Int32)cmd.Parameters["@CustomerRequirementId"].Value;
        //    }
        //    catch (SqlException sqlex)
        //    {
        //        //LogException(sqlex);
        //        throw new Exception("Failed to insert CustomerRequirement", sqlex);
        //    }
        //    finally
        //    {
        //        cmd.Dispose();
        //        cn.Close();
        //        cn.Dispose();
        //    }
        //}

        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_CustomerRequirement_as_Alternate]
        /// </summary>
        public override Int32 InsertAsAllAlternate(System.Int32? clientNo, System.String part, System.Int32? CustRequirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_CustomerRequirement_as_AllAlternate", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustRequirementId", SqlDbType.Int).Value = CustRequirementId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@Result", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                int ret = (Int32)cmd.Parameters["@Result"].Value;
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert All CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// ItemSearch 
        /// Calls [usp_itemsearch_CustomerRequirement]
        /// </summary>
        public override List<CustomerRequirementDetails> ItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_itemsearch_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@CMSearch", SqlDbType.NVarChar).Value = cmSearch;
                cmd.Parameters.Add("@IncludeClosed", SqlDbType.Bit).Value = includeClosed;
                cmd.Parameters.Add("@CustomerRequirementNoLo", SqlDbType.Int).Value = customerRequirementNoLo;
                cmd.Parameters.Add("@CustomerRequirementNoHi", SqlDbType.Int).Value = customerRequirementNoHi;
                cmd.Parameters.Add("@ReceivedDateFrom", SqlDbType.DateTime).Value = receivedDateFrom;
                cmd.Parameters.Add("@ReceivedDateTo", SqlDbType.DateTime).Value = receivedDateTo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Get 
        /// Calls [usp_select_CustomerRequirement]
        /// </summary>
        public override CustomerRequirementDetails Get(System.Int32? customerRequirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetCustomerRequirementFromReader(reader);
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.Shortage = GetReaderValue_Boolean(reader, "Shortage", false);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.OriginalCustomerRequirementNo = GetReaderValue_NullableInt32(reader, "OriginalCustomerRequirementNo", null);
                    obj.ReasonNo = GetReaderValue_NullableInt32(reader, "ReasonNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Closed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.UsageNo = GetReaderValue_NullableInt32(reader, "UsageNo", null);
                    obj.BOM = GetReaderValue_NullableBoolean(reader, "BOM", null);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.PartWatch = GetReaderValue_NullableBoolean(reader, "PartWatch", null);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DisplayStatus = GetReaderValue_String(reader, "DisplayStatus", "");
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.CompanyOnStop = GetReaderValue_NullableBoolean(reader, "CompanyOnStop", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.UsageName = GetReaderValue_String(reader, "UsageName", "");
                    obj.CustomerRequirementValue = GetReaderValue_Double(reader, "CustomerRequirementValue", 0);
                    obj.ClosedReason = GetReaderValue_String(reader, "ClosedReason", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.Traceability = GetReaderValue_NullableBoolean(reader, "IsTraceability", false);
                    obj.BOMHeader = GetReaderValue_String(reader, "BOMHeader", "");
                    obj.BOMStatus = GetReaderValue_String(reader, "BOMStatus", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMNo", null);
                    obj.RequestToPOHubBy = GetReaderValue_NullableInt32(reader, "RequestToPOHubBy", null);
                    obj.FactorySealed = GetReaderValue_NullableBoolean(reader, "FactorySealed", false);
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SourcingResultNo = GetReaderValue_NullableInt32(reader, "SourcingResultNo", 0);

                    obj.PQA = GetReaderValue_NullableBoolean(reader, "PartialQuantityAcceptable", null);
                    obj.Obsolete = GetReaderValue_NullableBoolean(reader, "Obsolete", null);
                    obj.LastTimeBuy = GetReaderValue_NullableBoolean(reader, "LastTimeBuy", null);
                    obj.RefirbsAcceptable = GetReaderValue_NullableBoolean(reader, "RefirbsAcceptable", null);
                    obj.TestingRequired = GetReaderValue_NullableBoolean(reader, "TestingRequired", null);
                    obj.TargetSellPrice = GetReaderValue_Double(reader, "TargetSellPrice", 0);
                    obj.CompetitorBestOffer = GetReaderValue_Double(reader, "CompetitorBestOffer", 0);
                    obj.CustomerDecisionDate = GetReaderValue_DateTime(reader, "CustomerDecisionDate", DateTime.MinValue);
                    obj.RFQClosingDate = GetReaderValue_DateTime(reader, "RFQClosingDate", DateTime.MinValue);
                    obj.QuoteValidityRequired = GetReaderValue_NullableInt32(reader, "QuoteValidityRequired", null);
                    obj.Type = GetReaderValue_NullableInt32(reader, "ReqType", null);
                    obj.OrderToPlace = GetReaderValue_NullableBoolean(reader, "OrderToPlace", null);
                    obj.RequirementforTraceability = GetReaderValue_NullableInt32(reader, "ReqForTraceability", null);
                    obj.QuoteValidityText = GetReaderValue_String(reader, "QuoteValidityText", "");
                    obj.ReqTypeText = GetReaderValue_String(reader, "ReqTypeText", "");
                    obj.ReqForTraceabilityText = GetReaderValue_String(reader, "ReqForTraceabilityText", "");
                    obj.EAU = GetReaderValue_String(reader, "EAU", "");
                    obj.ReqNotes = GetReaderValue_String(reader, "ReqNotes", "");
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", null);
                    obj.ClientGlobalCurrencyNo = GetReaderValue_NullableInt32(reader, "CustGlobalCurrencyNo", 0);
                    obj.IsCurrencyInSameFaimly = GetReaderValue_NullableBoolean(reader, "IsCurrencyInSameFaimly", false);
                    obj.AlternativesAccepted = GetReaderValue_NullableBoolean(reader, "AlternativesAccepted", false);
                    obj.RepeatBusiness = GetReaderValue_NullableBoolean(reader, "RepeatBusiness", false);
                    obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    obj.DutyCode = GetReaderValue_String(reader, "DutyCode", "");
                    obj.DutyRate = GetReaderValue_NullableDouble(reader, "ProductDutyRate", null);
                    obj.MSLLevelNo = GetReaderValue_NullableInt32(reader, "MSLLevelNo", 0);
                    obj.IsProdHaz = GetReaderValue_NullableBoolean(reader, "IsHazardous", false);
                    obj.AlternateStatus = GetReaderValue_NullableByte(reader, "AlternateStatus", (byte)0);
                    //[006] start
                    obj.SalesOrderNumber = GetReaderValue_String(reader, "SalesOrderNumber", "");
                    //[006] end
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", null);
                    obj.SupportTeamMemberName = GetReaderValue_String(reader, "SupportTeamMemberName", "");
                    //code start for ihs
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "CountryOfOrigin", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.LifeCycleStage = GetReaderValue_String(reader, "LifeCycleStage", "");
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packing", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackagingSize", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.IHSProduct = GetReaderValue_String(reader, "IHSProduct", "");
                    // obj.IHSProductNo = GetReaderValue_NullableInt32(reader, "IHSProductNo", 0);
                    // obj.IHSProductName = GetReaderValue_String(reader, "IHSProductName", "");
                    // obj.IHSHTSCode = GetReaderValue_String(reader, "IHSHTSCode", "");
                    obj.IHSDutyCode = GetReaderValue_String(reader, "IHSDutyCode", "");
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.ParentRequirementId = GetReaderValue_NullableInt32(reader, "ParentRequirementId", null);
                    obj.ParentRequirementNo = GetReaderValue_NullableInt32(reader, "ParentRequirementNo", null);
                    //[013] code start
                    obj.IsOrderViaIPOonly = GetReaderValue_NullableBoolean(reader, "IsOrderViaIPOonly", false);
                    //[013] code end
                    obj.IsRestManufaturer = GetReaderValue_NullableBoolean(reader, "IsRestManufaturer", false);
                    obj.PartEditStatus = GetReaderValue_NullableInt32(reader, "PartEditStatus", 0);

                    obj.SupportTeamMemberName = GetReaderValue_String(reader, "SupportTeamMemberName", "");
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", 0);
                    //[014] code start
                    obj.IHSECCNCodeDefination = GetReaderValue_String(reader, "IHSECCNCodeDefination", "");
                    //code end for ihs
                    //[014] code end
                    obj.IsRestrictedProduct = GetReaderValue_NullableBoolean(reader, "IsRestrictedProduct", false);
                    obj.ECCNNotify = GetReaderValue_NullableBoolean(reader, "ECCNClientNotify", false);
                    obj.EccnSubject = GetReaderValue_String(reader, "ECCNSubject", "");
                    obj.EccnMessage = GetReaderValue_String(reader, "ECCNMessage", "");
                    obj.StockAvailableDetail = GetReaderValue_String(reader, "StockAvailableDetail", "");
                    obj.WarningMessage = GetReaderValue_String(reader, "WarningMessage", "");
                    obj.BlankECCNCode = GetReaderValue_String(reader, "BlankECCNCode", "");
                    obj.AS6081 = GetReaderValue_NullableBoolean(reader, "AS6081", false);
                    obj.PurchasingNotes = GetReaderValue_String(reader, "PurchasingNotes", "");
                    obj.IsPDFAvailable = GetReaderValue_NullableBoolean(reader, "IsPDFAvailable", false);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Get 
        /// Calls [usp_select_CustomerRequirementBOM]
        /// </summary>
        public override CustomerRequirementDetails GetReqBOM(System.Int32? customerRequirementId, System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_CustomerRequirementBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetCustomerRequirementFromReader(reader);
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.Shortage = GetReaderValue_Boolean(reader, "Shortage", false);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.OriginalCustomerRequirementNo = GetReaderValue_NullableInt32(reader, "OriginalCustomerRequirementNo", null);
                    obj.ReasonNo = GetReaderValue_NullableInt32(reader, "ReasonNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Closed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.UsageNo = GetReaderValue_NullableInt32(reader, "UsageNo", null);
                    obj.BOM = GetReaderValue_NullableBoolean(reader, "BOM", null);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.PartWatch = GetReaderValue_NullableBoolean(reader, "PartWatch", null);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DisplayStatus = GetReaderValue_String(reader, "DisplayStatus", "");
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.CompanyOnStop = GetReaderValue_NullableBoolean(reader, "CompanyOnStop", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.UsageName = GetReaderValue_String(reader, "UsageName", "");
                    obj.CustomerRequirementValue = GetReaderValue_Double(reader, "CustomerRequirementValue", 0);
                    obj.ClosedReason = GetReaderValue_String(reader, "ClosedReason", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.Traceability = GetReaderValue_NullableBoolean(reader, "IsTraceability", false);
                    obj.BOMHeader = GetReaderValue_String(reader, "BOMHeader", "");
                    obj.BOMStatus = GetReaderValue_String(reader, "BOMStatus", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMNo", null);
                    obj.RequestToPOHubBy = GetReaderValue_NullableInt32(reader, "RequestToPOHubBy", null);
                    obj.FactorySealed = GetReaderValue_NullableBoolean(reader, "FactorySealed", false);
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SourcingResultNo = GetReaderValue_NullableInt32(reader, "SourcingResultNo", 0);

                    obj.PQA = GetReaderValue_NullableBoolean(reader, "PartialQuantityAcceptable", null);
                    obj.Obsolete = GetReaderValue_NullableBoolean(reader, "Obsolete", null);
                    obj.LastTimeBuy = GetReaderValue_NullableBoolean(reader, "LastTimeBuy", null);
                    obj.RefirbsAcceptable = GetReaderValue_NullableBoolean(reader, "RefirbsAcceptable", null);
                    obj.TestingRequired = GetReaderValue_NullableBoolean(reader, "TestingRequired", null);
                    obj.TargetSellPrice = GetReaderValue_Double(reader, "TargetSellPrice", 0);
                    obj.CompetitorBestOffer = GetReaderValue_Double(reader, "CompetitorBestOffer", 0);
                    obj.CustomerDecisionDate = GetReaderValue_DateTime(reader, "CustomerDecisionDate", DateTime.MinValue);
                    obj.RFQClosingDate = GetReaderValue_DateTime(reader, "RFQClosingDate", DateTime.MinValue);
                    obj.QuoteValidityRequired = GetReaderValue_NullableInt32(reader, "QuoteValidityRequired", null);
                    obj.Type = GetReaderValue_NullableInt32(reader, "ReqType", null);
                    obj.OrderToPlace = GetReaderValue_NullableBoolean(reader, "OrderToPlace", null);
                    obj.RequirementforTraceability = GetReaderValue_NullableInt32(reader, "ReqForTraceability", null);
                    obj.QuoteValidityText = GetReaderValue_String(reader, "QuoteValidityText", "");
                    obj.ReqTypeText = GetReaderValue_String(reader, "ReqTypeText", "");
                    obj.ReqForTraceabilityText = GetReaderValue_String(reader, "ReqForTraceabilityText", "");
                    obj.SourcingResult = GetReaderValue_Int32(reader, "SourcingResult", 0);
                    obj.EAU = GetReaderValue_String(reader, "EAU", "");

                    obj.ClientCurrencyNo = GetReaderValue_NullableInt32(reader, "ClientCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.ReqGlobalCurrencyNo = GetReaderValue_NullableInt32(reader, "ReqGlobalCurrencyNo", 0);
                    obj.ClientGlobalCurrencyNo = GetReaderValue_NullableInt32(reader, "ClientGlobalCurrencyNo", 0);
                    obj.IsNoBid = GetReaderValue_NullableBoolean(reader, "IsNoBid", null);
                    obj.NoBidNotes = GetReaderValue_String(reader, "NoBidNotes", "");
                    obj.AlternativesAccepted = GetReaderValue_NullableBoolean(reader, "AlternativesAccepted", null);
                    obj.RepeatBusiness = GetReaderValue_NullableBoolean(reader, "RepeatBusiness", null);

                    obj.DutyCode = GetReaderValue_String(reader, "DutyCode", "");
                    obj.DutyRate = GetReaderValue_NullableDouble(reader, "ProductDutyRate", null);
                    obj.MSLLevelNo = GetReaderValue_NullableInt32(reader, "MSLLevelNo", 0);
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", 0);

                    //code start for ihs
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "CountryOfOrigin", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.LifeCycleStage = GetReaderValue_String(reader, "LifeCycleStage", "");
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packing", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackagingSize", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.IHSCurrencyCode = GetReaderValue_String(reader, "ihsCurrencyCode", "");
                    obj.IHSProduct = GetReaderValue_String(reader, "IHSProduct", "");
                    obj.IHSProductNo = GetReaderValue_NullableInt32(reader, "IHSProductNo", 0);
                    obj.IHSProductName = GetReaderValue_String(reader, "IHSProductName", "");
                    obj.IHSHTSCode = GetReaderValue_String(reader, "IHSHTSCode", "");
                    obj.IHSDutyCode = GetReaderValue_String(reader, "IHSDutyCode", "");
                    obj.PurchaseRequestId = GetReaderValue_String(reader, "PurchaseRequestId", "");
                    obj.PurchaseRequestNumber = GetReaderValue_String(reader, "PurchaseRequestNumber", "");
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    //[014] code start
                    obj.IHSECCNCodeDefination = GetReaderValue_String(reader, "IHSECCNCodeDefination", "");
                    obj.IsAs6081Required= GetReaderValue_String(reader, "IsAs6081Required", "");
                    //code end for ihs
                    //code start
                    obj.IsPDFAvailable = GetReaderValue_NullableBoolean(reader, "IsPDFAvailable", false);
                    obj.IHSPartsId = GetReaderValue_Int32(reader, "IHSPartsId", 0);
                    obj.StockAvailableDetail = GetReaderValue_String(reader, "StockAvailableDetail", "");
                    obj.LyticaManufacturerRef = GetReaderValue_String(reader, "LyticaManufacturerRef", "");
                    obj.LyticaAveragePrice = GetReaderValue_Double(reader, "LyticaAveragePrice", 0);
                    obj.LyticaTargetPrice = GetReaderValue_Double(reader, "LyticaTargetPrice", 0);
                    obj.LyticaMarketLeading = GetReaderValue_Double(reader, "LyticaMarketLeading", 0);
                    obj.CustomerRefNo = GetReaderValue_String(reader, "CustomerRefNo", "");
                    //code ends
                    //code end for ihs
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetByNumber 
        /// Calls [usp_select_CustomerRequirement_by_Number]
        /// </summary>
        public override CustomerRequirementDetails GetByNumber(System.Int32? customerRequirementNumber, System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_CustomerRequirement_by_Number", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementNumber", SqlDbType.Int).Value = customerRequirementNumber;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetCustomerRequirementFromReader(reader);
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.Shortage = GetReaderValue_Boolean(reader, "Shortage", false);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.OriginalCustomerRequirementNo = GetReaderValue_NullableInt32(reader, "OriginalCustomerRequirementNo", null);
                    obj.ReasonNo = GetReaderValue_NullableInt32(reader, "ReasonNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Closed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.UsageNo = GetReaderValue_NullableInt32(reader, "UsageNo", null);
                    obj.BOM = GetReaderValue_NullableBoolean(reader, "BOM", null);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.PartWatch = GetReaderValue_NullableBoolean(reader, "PartWatch", null);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DisplayStatus = GetReaderValue_String(reader, "DisplayStatus", "");
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.CompanyOnStop = GetReaderValue_NullableBoolean(reader, "CompanyOnStop", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.UsageName = GetReaderValue_String(reader, "UsageName", "");
                    obj.CustomerRequirementValue = GetReaderValue_Double(reader, "CustomerRequirementValue", 0);
                    obj.ClosedReason = GetReaderValue_String(reader, "ClosedReason", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetForPage 
        /// Calls [usp_select_CustomerRequirement_for_Page]
        /// </summary>
        public override CustomerRequirementDetails GetForPage(System.Int32? customerRequirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_CustomerRequirement_for_Page", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetCustomerRequirementFromReader(reader);
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.DisplayStatus = GetReaderValue_String(reader, "DisplayStatus", "");
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", 0);
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", 0);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.AS6081 = GetReaderValue_Boolean(reader, "AS6081", false); //[016]
                    obj.ClientName= GetReaderValue_String(reader, "ClientName", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetIdByNumber 
        /// Calls [usp_select_CustomerRequirement_Id_by_Number]
        /// </summary>
        public override CustomerRequirementDetails GetIdByNumber(System.Int32? customerRequirementNumber, System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_CustomerRequirement_Id_by_Number", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementNumber", SqlDbType.Int).Value = customerRequirementNumber;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetCustomerRequirementFromReader(reader);
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to get CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetNextNumber 
        /// Calls [usp_select_CustomerRequirement_NextNumber]
        /// </summary>
        public override CustomerRequirementDetails GetNextNumber(System.Int32? clientNo, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_CustomerRequirement_NextNumber", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@NewNumber", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetCustomerRequirementFromReader(reader);
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetNumberById 
        /// Calls [usp_select_CustomerRequirement_Number_by_Id]
        /// </summary>
        public override CustomerRequirementDetails GetNumberById(System.Int32? customerRequirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_CustomerRequirement_Number_by_Id", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetCustomerRequirementFromReader(reader);
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForCompany 
        /// Calls [usp_selectAll_CustomerRequirement_for_Company]
        /// </summary>
        public override List<CustomerRequirementDetails> GetListForCompany(System.Int32? companyId, System.Boolean? includeClosed)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CustomerRequirement_for_Company", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = companyId;
                cmd.Parameters.Add("@IncludeClosed", SqlDbType.Bit).Value = includeClosed;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.Shortage = GetReaderValue_Boolean(reader, "Shortage", false);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.OriginalCustomerRequirementNo = GetReaderValue_NullableInt32(reader, "OriginalCustomerRequirementNo", null);
                    obj.ReasonNo = GetReaderValue_NullableInt32(reader, "ReasonNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Closed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.UsageNo = GetReaderValue_NullableInt32(reader, "UsageNo", null);
                    obj.BOM = GetReaderValue_NullableBoolean(reader, "BOM", null);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.PartWatch = GetReaderValue_NullableBoolean(reader, "PartWatch", null);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DisplayStatus = GetReaderValue_String(reader, "DisplayStatus", "");
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.CompanyOnStop = GetReaderValue_NullableBoolean(reader, "CompanyOnStop", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.UsageName = GetReaderValue_String(reader, "UsageName", "");
                    obj.CustomerRequirementValue = GetReaderValue_Double(reader, "CustomerRequirementValue", 0);
                    obj.ClosedReason = GetReaderValue_String(reader, "ClosedReason", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForCustomerRequirementNumber 
        /// Calls [usp_selectAll_CustomerRequirement_for_CustomerRequirementNumber]
        /// </summary>
        public override List<CustomerRequirementDetails> GetListForCustomerRequirementNumber(System.Int32? customerRequirementNumber, System.Int32? ClientID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CustomerRequirement_for_CustomerRequirementNumber", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementNumber", SqlDbType.Int).Value = customerRequirementNumber;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = ClientID;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.Shortage = GetReaderValue_Boolean(reader, "Shortage", false);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.OriginalCustomerRequirementNo = GetReaderValue_NullableInt32(reader, "OriginalCustomerRequirementNo", null);
                    obj.ReasonNo = GetReaderValue_NullableInt32(reader, "ReasonNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Closed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.UsageNo = GetReaderValue_NullableInt32(reader, "UsageNo", null);
                    obj.BOM = GetReaderValue_NullableBoolean(reader, "BOM", null);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.PartWatch = GetReaderValue_NullableBoolean(reader, "PartWatch", null);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DisplayStatus = GetReaderValue_String(reader, "DisplayStatus", "");
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.CompanyOnStop = GetReaderValue_NullableBoolean(reader, "CompanyOnStop", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.UsageName = GetReaderValue_String(reader, "UsageName", "");
                    obj.CustomerRequirementValue = GetReaderValue_Double(reader, "CustomerRequirementValue", 0);
                    obj.ClosedReason = GetReaderValue_String(reader, "ClosedReason", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.AlternateStatus = GetReaderValue_Byte(reader, "AlternateStatus", (byte)0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        // [001] code start
        /// <summary>
        /// GetListForCustomerRequirement 
        /// Calls [usp_selectAll_CustomerRequirement_for_CustomerRequirement]
        /// </summary>
        public override List<CustomerRequirementDetails> GetListForCustomerRequirement(System.Int32? customerRequirementNo, System.Int32? clientID, System.Boolean? IsGSA = null)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CustomerRequirement_for_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientID;
                cmd.Parameters.Add("@IsGSA", SqlDbType.Bit).Value = IsGSA;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.Shortage = GetReaderValue_Boolean(reader, "Shortage", false);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.OriginalCustomerRequirementNo = GetReaderValue_NullableInt32(reader, "OriginalCustomerRequirementNo", null);
                    obj.ReasonNo = GetReaderValue_NullableInt32(reader, "ReasonNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Closed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.UsageNo = GetReaderValue_NullableInt32(reader, "UsageNo", null);
                    obj.BOM = GetReaderValue_NullableBoolean(reader, "BOM", null);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.PartWatch = GetReaderValue_NullableBoolean(reader, "PartWatch", null);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DisplayStatus = GetReaderValue_String(reader, "DisplayStatus", "");
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.CompanyOnStop = GetReaderValue_NullableBoolean(reader, "CompanyOnStop", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.UsageName = GetReaderValue_String(reader, "UsageName", "");
                    obj.CustomerRequirementValue = GetReaderValue_Double(reader, "CustomerRequirementValue", 0);
                    obj.ClosedReason = GetReaderValue_String(reader, "ClosedReason", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.BOMHeader = GetReaderValue_String(reader, "BOMHeader", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMNo", null);
                    obj.RequestToPOHubBy = GetReaderValue_NullableInt32(reader, "RequestToPOHubBy", null);
                    obj.FactorySealed = GetReaderValue_NullableBoolean(reader, "FactorySealed", null);
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");

                    obj.PQA = GetReaderValue_NullableBoolean(reader, "PartialQuantityAcceptable", null);
                    obj.Obsolete = GetReaderValue_NullableBoolean(reader, "Obsolete", null);
                    obj.LastTimeBuy = GetReaderValue_NullableBoolean(reader, "LastTimeBuy", null);
                    obj.RefirbsAcceptable = GetReaderValue_NullableBoolean(reader, "RefirbsAcceptable", null);
                    obj.TestingRequired = GetReaderValue_NullableBoolean(reader, "TestingRequired", null);
                    obj.TargetSellPrice = GetReaderValue_Double(reader, "TargetSellPrice", 0);
                    obj.CompetitorBestOffer = GetReaderValue_Double(reader, "CompetitorBestOffer", 0);
                    obj.CustomerDecisionDate = GetReaderValue_DateTime(reader, "CustomerDecisionDate", DateTime.MinValue);
                    obj.RFQClosingDate = GetReaderValue_DateTime(reader, "RFQClosingDate", DateTime.MinValue);
                    obj.QuoteValidityRequired = GetReaderValue_NullableInt32(reader, "QuoteValidityRequired", null);
                    obj.Type = GetReaderValue_NullableInt32(reader, "ReqType", null);
                    obj.OrderToPlace = GetReaderValue_NullableBoolean(reader, "OrderToPlace", null);
                    obj.RequirementforTraceability = GetReaderValue_NullableInt32(reader, "ReqForTraceability", null);
                    obj.AlternateStatus = GetReaderValue_Byte(reader, "AlternateStatus", (byte)0);
                    //obj.DutyCode = GetReaderValue_String(reader, "DutyCode", null);
                    //obj.DutyRate = GetReaderValue_NullableDouble(reader, "ProductDutyRate", null);
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", null);
                    obj.SupportTeamMemberName = GetReaderValue_String(reader, "SupportTeamMemberName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        // [001] code end

        /// <summary>
        /// GetListOpenForCompany 
		/// Calls [usp_selectAll_CustomerRequirement_open_for_Company]
        /// </summary>
		public override List<CustomerRequirementDetails> GetListOpenForCompany(System.Int32? companyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CustomerRequirement_open_for_Company", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = companyId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.Shortage = GetReaderValue_Boolean(reader, "Shortage", false);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.OriginalCustomerRequirementNo = GetReaderValue_NullableInt32(reader, "OriginalCustomerRequirementNo", null);
                    obj.ReasonNo = GetReaderValue_NullableInt32(reader, "ReasonNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Closed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.UsageNo = GetReaderValue_NullableInt32(reader, "UsageNo", null);
                    obj.BOM = GetReaderValue_NullableBoolean(reader, "BOM", null);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.PartWatch = GetReaderValue_NullableBoolean(reader, "PartWatch", null);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DisplayStatus = GetReaderValue_String(reader, "DisplayStatus", "");
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.CompanyOnStop = GetReaderValue_NullableBoolean(reader, "CompanyOnStop", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.UsageName = GetReaderValue_String(reader, "UsageName", "");
                    obj.CustomerRequirementValue = GetReaderValue_Double(reader, "CustomerRequirementValue", 0);
                    obj.ClosedReason = GetReaderValue_String(reader, "ClosedReason", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListOpenForLogin 
        /// Calls [usp_selectAll_CustomerRequirement_open_for_Login]
        /// </summary>
        public override List<CustomerRequirementDetails> GetListOpenForLogin(System.Int32? loginId, System.Int32? topToSelect)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CustomerRequirement_open_for_Login", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@TopToSelect", SqlDbType.Int).Value = topToSelect;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    obj.CreditLimit = GetReaderValue_NullableDouble(reader, "CreditLimit", null);
                    obj.Balance = GetReaderValue_NullableDouble(reader, "Balance", null);
                    obj.DaysOverdue = GetReaderValue_NullableInt32(reader, "DaysOverdue", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListOverdueForLogin 
        /// Calls [usp_selectAll_CustomerRequirement_overdue_for_Login]
        /// </summary>
        public override List<CustomerRequirementDetails> GetListOverdueForLogin(System.Int32? loginId, System.Int32? topToSelect)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CustomerRequirement_overdue_for_Login", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@TopToSelect", SqlDbType.Int).Value = topToSelect;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    obj.CreditLimit = GetReaderValue_NullableDouble(reader, "CreditLimit", null);
                    obj.Balance = GetReaderValue_NullableDouble(reader, "Balance", null);
                    obj.DaysOverdue = GetReaderValue_NullableInt32(reader, "DaysOverdue", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Source 
        /// Calls [usp_source_CustomerRequirement]
        /// </summary>
        public override List<CustomerRequirementDetails> Source(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            outDate = null;
            try
            {
                if (!hasServerLocal)
                {
                    cn = new SqlConnection(this.GTConnectionString);
                    cmd = new SqlCommand("usp_source_CustomerRequirement_Without_ClientId", cn);
                }
                else
                {
                    cn = new SqlConnection(this.ConnectionString);
                    cmd = new SqlCommand("usp_source_CustomerRequirement", cn);
                }


                cmd.CommandType = CommandType.StoredProcedure;
               // [015] Start
                //cmd.CommandTimeout = 30;  
                cmd.CommandTimeout = 120;
                // [015]
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;
                
                
                
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SalesmanName = GetReaderValue_String(reader, "EmployeeName", "");
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    //[003] start
                    obj.BOMNo = GetReaderValue_Int32(reader, "BOMID", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    //[003] end
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //code added by anand for customer requierment
        /// <summary>
        /// Source 
        /// Calls [usp_CrossMatch_CustomerRequirement]
        /// </summary>
        public override List<CustomerRequirementDetails> SourceCustomerRequirement(System.Int32? clientId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.String sortDir, System.String partSearch, System.String PartMatch, System.String months, System.Int32? monthTime, System.Int32? vendorNo, System.Int32? currencyNo, System.Boolean? isManufaurer, System.Int32? NoOfTopRecord, bool IsServerLocal, System.Boolean? isPohub, System.Int32? BomID, System.Boolean? IncludeAltPart, System.Int32? ReqId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            // outDate = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_CrossMatch_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.NVarChar).Value = sortDir;
                cmd.Parameters.Add("@Parts", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@PartMatch", SqlDbType.NVarChar).Value = PartMatch;
                cmd.Parameters.Add("@Months", SqlDbType.NVarChar).Value = months;
                cmd.Parameters.Add("@MonthTime", SqlDbType.Int).Value = monthTime;
                cmd.Parameters.Add("@VenderTYpe", SqlDbType.Int).Value = vendorNo;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Manufacturer", SqlDbType.Bit).Value = isManufaurer;
                cmd.Parameters.Add("@NoOfTopRecord", SqlDbType.Bit).Value = NoOfTopRecord;
                cmd.Parameters.Add("@BomID", SqlDbType.Int).Value = BomID;
                cmd.Parameters.Add("@IncludeAltPart", SqlDbType.Bit).Value = IncludeAltPart;
                cmd.Parameters.Add("@CustomerReqID", SqlDbType.Int).Value = ReqId;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SalesmanName = GetReaderValue_String(reader, "EmployeeName", "");
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    //[003] start
                    obj.BOMNo = GetReaderValue_Int32(reader, "BOMID", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.isIncludeAltPart = GetReaderValue_Boolean(reader, "isIncludeAltPart", false);
                    obj.RowNum = GetReaderValue_Int32(reader, "RowNum", 0);
                    obj.TotalCount = GetReaderValue_Int32(reader, "TotalCount", 0);
                    //[003] end
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    //outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //code end


        /// <summary>
        /// Update CustomerRequirement
        /// Calls [usp_update_CustomerRequirement]
        /// </summary>
        public override bool Update(System.Int32? customerRequirementId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? bomNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packing, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo, System.Boolean? AS6081)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustomerRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@ReceivedDate", SqlDbType.DateTime).Value = receivedDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@DatePromised", SqlDbType.DateTime).Value = datePromised;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@Instructions", SqlDbType.NVarChar).Value = instructions;
                cmd.Parameters.Add("@Shortage", SqlDbType.Bit).Value = shortage;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = contactNo;
                cmd.Parameters.Add("@UsageNo", SqlDbType.Int).Value = usageNo;
                cmd.Parameters.Add("@Alternate", SqlDbType.Bit).Value = alternate;
                cmd.Parameters.Add("@OriginalCustomerRequirementNo", SqlDbType.Int).Value = originalCustomerRequirementNo;
                cmd.Parameters.Add("@ReasonNo", SqlDbType.Int).Value = reasonNo;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@CustomerPart", SqlDbType.NVarChar).Value = customerPart;
                cmd.Parameters.Add("@Closed", SqlDbType.Bit).Value = closed;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@PartWatch", SqlDbType.Bit).Value = partWatch;
                cmd.Parameters.Add("@BOM", SqlDbType.Bit).Value = bom;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.Bit).Value = FactorySealed;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                //  [002] code start
                cmd.Parameters.Add("@PartialQuantityAcceptable", SqlDbType.Bit).Value = PQA;
                cmd.Parameters.Add("@Obsolete", SqlDbType.Bit).Value = ObsoleteChk;
                cmd.Parameters.Add("@LastTimeBuy", SqlDbType.Bit).Value = LastTimeBuyChk;
                cmd.Parameters.Add("@RefirbsAcceptable", SqlDbType.Bit).Value = RefirbsAcceptableChk;
                cmd.Parameters.Add("@TestingRequired", SqlDbType.Bit).Value = TestingRequiredChk;
                cmd.Parameters.Add("@TargetSellPrice", SqlDbType.Float).Value = TargetSellPrice;
                cmd.Parameters.Add("@CompetitorBestOffer", SqlDbType.Float).Value = CompetitorBestOffer;
                cmd.Parameters.Add("@CustomerDecisionDate", SqlDbType.DateTime).Value = CustomerDecisionDate;
                cmd.Parameters.Add("@RFQClosingDate", SqlDbType.DateTime).Value = RFQClosingDate;
                cmd.Parameters.Add("@QuoteValidityRequired", SqlDbType.Int).Value = QuoteValidityRequired;
                cmd.Parameters.Add("@Type", SqlDbType.Int).Value = Type;
                cmd.Parameters.Add("@OrderToPlace", SqlDbType.Int).Value = OrderToPlace;
                cmd.Parameters.Add("@RequirementForTraceability", SqlDbType.Int).Value = RequirementforTraceability;
                cmd.Parameters.Add("@EAU", SqlDbType.NVarChar).Value = EAU;
                cmd.Parameters.Add("@AlternativesAccepted", SqlDbType.Bit).Value = AlternativesAccepted;
                cmd.Parameters.Add("@RepeatBusiness", SqlDbType.Bit).Value = RepeatBusiness;
                cmd.Parameters.Add("@SupportTeamMemberNo", SqlDbType.Int).Value = SupportTeamMemberNo;

                //IHS code start
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = CountryOfOriginNo;
                cmd.Parameters.Add("@LifeCycleStage", SqlDbType.NVarChar).Value = LifeCycleStage;
                cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = HTSCode;
                cmd.Parameters.Add("@AveragePrice", SqlDbType.Float).Value = AveragePrice;
                cmd.Parameters.Add("@Packing", SqlDbType.NVarChar).Value = Packing;
                cmd.Parameters.Add("@PackagingSize", SqlDbType.NVarChar).Value = PackagingSize;
                cmd.Parameters.Add("@Descriptions", SqlDbType.NVarChar).Value = Descriptions;
                cmd.Parameters.Add("@IHSPartsNo", SqlDbType.Int).Value = IHSPartsId;
                cmd.Parameters.Add("@IHSCurrencyCode", SqlDbType.NVarChar).Value = IHSCurrencyCode;
                cmd.Parameters.Add("@IHSProduct", SqlDbType.NVarChar).Value = IHSProduct;
                cmd.Parameters.Add("@ECCNCode", SqlDbType.NVarChar).Value = ECCNCode;
                cmd.Parameters.Add("@ECCNNo", SqlDbType.Int).Value = ECCNNo;


                //IHS code end     
                if (bomNo > 0)
                {
                    cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = bomNo;
                }
                cmd.Parameters.Add("@AS6081", SqlDbType.Bit).Value = AS6081; //[016] AS6081
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                if (ret == -1)
                {
                    return false;
                }
                else
                {
                    return (ret > 0);
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// Update CustomerRequirement
        /// Calls [usp_update_CustomerRequirement_Close]
        /// </summary>
        public override bool UpdateClose(System.Int32? customerRequirementId, System.Boolean? includeAllRelatedAlternates, System.Int32? reasonNo, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustomerRequirement_Close", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@IncludeAllRelatedAlternates", SqlDbType.Bit).Value = includeAllRelatedAlternates;
                cmd.Parameters.Add("@ReasonNo", SqlDbType.Int).Value = reasonNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// Delete
        /// Calls [[usp_delete_CustomerReq_AlternatePart]]
        /// </summary>
        public override bool DeleteAlternateParts(System.Int32? AlternatePartid)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_CustomerReq_AlternatePart", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = AlternatePartid;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Delete ALternate Customer Parts", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_CustomerRequirementPrint]
        /// </summary>
        public override List<CustomerRequirementDetails> DataListNuggetPrint(System.Int32? clientId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String cmSearch, System.Int32? salesmanNo, System.Int32? companyNo, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.DateTime? datePromisedFrom, System.DateTime? datePromisedTo, System.Int32? contactNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_CustomerRequirementPrint", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@CMSearch", SqlDbType.NVarChar).Value = cmSearch;
                cmd.Parameters.Add("@SalesmanNo", SqlDbType.Int).Value = salesmanNo;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
                cmd.Parameters.Add("@ReceivedDateFrom", SqlDbType.DateTime).Value = receivedDateFrom;
                cmd.Parameters.Add("@ReceivedDateTo", SqlDbType.DateTime).Value = receivedDateTo;
                cmd.Parameters.Add("@DatePromisedFrom", SqlDbType.DateTime).Value = datePromisedFrom;
                cmd.Parameters.Add("@DatePromisedTo", SqlDbType.DateTime).Value = datePromisedTo;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = contactNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// DataListNugget Import
        /// Calls [usp_datalistnugget_CustomerRequirementImport]
        /// </summary>
        public override List<CustomerRequirementDetails> DataListNuggetImport(System.Int32? clientId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String clientBomName, System.Int32? salesmanNo, System.String companyName, System.DateTime? importDateFrom, System.DateTime? importDateTo, System.Int32? status)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_CustomerRequirementImport", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@ClientBOMName", SqlDbType.NVarChar).Value = clientBomName;
                cmd.Parameters.Add("@SalesmanNo", SqlDbType.Int).Value = salesmanNo;
                cmd.Parameters.Add("@CompanyName", SqlDbType.NVarChar).Value = companyName;
                cmd.Parameters.Add("@ImportDateFrom", SqlDbType.DateTime).Value = importDateFrom;
                cmd.Parameters.Add("@ImportDateTo", SqlDbType.DateTime).Value = importDateTo;
                cmd.Parameters.Add("@Status", SqlDbType.Int).Value = status;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.ClientBOMId = GetReaderValue_Int32(reader, "ClientBOMId", 0);
                    obj.ClientBOMCode = GetReaderValue_String(reader, "ClientBOMCode", "");
                    obj.ClientBOMName = GetReaderValue_String(reader, "ClientBOMName", "");
                    obj.ImportDate = GetReaderValue_DateTime(reader, "ImportDate", DateTime.MinValue);
                    //obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.NoOfRequirements = GetReaderValue_Int32(reader, "NoOfRequirements", 0);
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.RecordsProcessed = GetReaderValue_NullableInt32(reader, "RecordsProcessed", 0);
                    obj.RecordsRemaining = GetReaderValue_NullableInt32(reader, "RecordsRemaining", 0);
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMId", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirementsImport", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Print 
        /// Calls [usp_Print_CustomerRequirement_Enquiry_Form]
        /// </summary>
        public override List<CustomerRequirementDetails> GetForPrint(System.String xmlReqNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Print_CustomerRequirement_Enquiry_Form", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@xmlCustReqNo", SqlDbType.VarChar).Value = xmlReqNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.AlternateStatus = GetReaderValue_NullableByte(reader, "AlternateStatus", (byte)0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// ItemSearch 
        /// Calls [usp_itemsearch_CustomerReqsWithBOM]
        /// </summary>
        public override List<CustomerRequirementDetails> ItemSearchWithBOM(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.Int32? client, System.String bomName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_itemsearch_CustomerReqsWithBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@CMSearch", SqlDbType.NVarChar).Value = cmSearch;
                cmd.Parameters.Add("@IncludeClosed", SqlDbType.Bit).Value = includeClosed;
                cmd.Parameters.Add("@CustomerRequirementNoLo", SqlDbType.Int).Value = customerRequirementNoLo;
                cmd.Parameters.Add("@CustomerRequirementNoHi", SqlDbType.Int).Value = customerRequirementNoHi;
                cmd.Parameters.Add("@ReceivedDateFrom", SqlDbType.DateTime).Value = receivedDateFrom;
                cmd.Parameters.Add("@ReceivedDateTo", SqlDbType.DateTime).Value = receivedDateTo;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = client;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.BOMHeader = GetReaderValue_String(reader, "BOMName", "");
                    obj.PHPrice = GetReaderValue_Double(reader, "PHPrice", 0);
                    obj.PHCurrencyCode = GetReaderValue_String(reader, "PHCurrecyCode", "");
                    obj.AS9120 = GetReaderValue_NullableBoolean(reader, "AS9120", false);
                    obj.IsGlobalCurrencySame = GetReaderValue_NullableBoolean(reader, "IsGlobalCurrencySame", false);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// ItemSearch 
        /// Calls [usp_itemsearch_CustRequirementWithoutBOM]
        /// </summary>
        public override List<CustomerRequirementDetails> ItemSearchWithoutBOM(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.Int32? bomId, System.String BOMName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_itemsearch_CustRequirementWithoutBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@CMSearch", SqlDbType.NVarChar).Value = cmSearch;
                cmd.Parameters.Add("@IncludeClosed", SqlDbType.Bit).Value = includeClosed;
                cmd.Parameters.Add("@CustomerRequirementNoLo", SqlDbType.Int).Value = customerRequirementNoLo;
                cmd.Parameters.Add("@CustomerRequirementNoHi", SqlDbType.Int).Value = customerRequirementNoHi;
                cmd.Parameters.Add("@ReceivedDateFrom", SqlDbType.DateTime).Value = receivedDateFrom;
                cmd.Parameters.Add("@ReceivedDateTo", SqlDbType.DateTime).Value = receivedDateTo;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = BOMName;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.BOMHeader = GetReaderValue_String(reader, "BOMName", "");
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        // [001] code start
        /// <summary>
        /// GetBOMListForCustomerRequirement 
        /// Calls [usp_selectAll_CustomerRequirement_for_BOM]
        /// </summary>
        public override List<CustomerRequirementDetails> GetBOMListForCustomerRequirement(System.Int32? BOMNo, System.Int32? clientID, System.Boolean isPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CustomerRequirement_for_BOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = BOMNo;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientID;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Closed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.BOMHeader = GetReaderValue_String(reader, "BOMHeader", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMNo", 0);
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.SourcingResultId = GetReaderValue_NullableInt32(reader, "SourcingResultId", 0);
                    obj.RequestToPOHubBy = GetReaderValue_NullableInt32(reader, "RequestToPOHubBy", 0);
                    obj.BOMFullName = GetReaderValue_String(reader, "BOMFullName", "");
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.ConvertedTargetValue = GetReaderValue_Double(reader, "ConvertedTargetValue", 0);
                    obj.BOMCurrencyCode = GetReaderValue_String(reader, "BOMCurrencyCode", "");
                    obj.PurchaseQuoteId = GetReaderValue_NullableInt32(reader, "PurchaseQuoteId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.POHubCompany = GetReaderValue_NullableInt32(reader, "POHubCompany", 0);
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.FactorySealed = GetReaderValue_Boolean(reader, "FactorySealed", false);
                    obj.AllSorcingHasDelDate = GetReaderValue_Int32(reader, "AllSorcingHasDelDate", 0);
                    obj.AllSorcingHasProduct = GetReaderValue_Int32(reader, "AllSorcingHasProduct", 0);
                    obj.SourcingResult = GetReaderValue_Int32(reader, "SourcingResult", 0);
                    obj.BOMStatus = GetReaderValue_String(reader, "BOMStatus", "");
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.HasClientSourcingResult = GetReaderValue_Boolean(reader, "HasClientSourcingResult", false);
                    obj.HasHubSourcingResult = GetReaderValue_Boolean(reader, "HasHubSourcingResult", false);
                    obj.IsNoBid = GetReaderValue_NullableBoolean(reader, "IsNoBid", false);
                    obj.ExpeditDate = GetReaderValue_NullableDateTime(reader, "ExpediteDate", null);
                    obj.UpdateByPH = GetReaderValue_Int32(reader, "UpdateByPH", 0);
                    obj.AlternateStatus = GetReaderValue_NullableByte(reader, "AlternateStatus", (byte)0);
                    obj.SupportTeamMemberNo = GetReaderValue_Int32(reader, "SupportTeamMemberNo", 0);
                    obj.SupportTeamMemberName = GetReaderValue_String(reader, "SupportTeamMemberName", "");
                    obj.PartWatchHUBIPO = GetReaderValue_Boolean(reader, "PartWatchHUBIPO", false);
                    obj.IsAs6081Required = GetReaderValue_String(reader, "IsAs6081Required", "");
                    obj.AssignedTo= GetReaderValue_String(reader, "AssignedTo", "");
                    obj.AssigneeId = GetReaderValue_String(reader, "AssigneeId", "");
                    obj.CompanyAdvisoryNotes = GetReaderValue_String(reader, "CompanyAdvisoryNotes", "");
                    obj.MfrAdvisoryNotes = GetReaderValue_String(reader, "MfrAdvisoryNotes", "");
                    obj.PriceIssueBuyAndSell = GetReaderValue_Boolean(reader, "PriceIssueBuyAndSell", false);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //add by anand
        /// <summary>
        /// GetBOMListForCustomerRequirement Cross Match
        /// Calls [usp_selectAll_CustomerRequirement_for_BOM_CorssMatchOffer]
        /// </summary>
        public override List<CustomerRequirementDetails> GetBOMListForCustomerRequirement_Offer(System.Int32? BOMNo, System.Int32 customerReqID, System.Int32? clientID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CustomerRequirement_for_BOM_CorssMatchOffer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = BOMNo;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerReqID;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientID;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Closed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.BOMHeader = GetReaderValue_String(reader, "BOMHeader", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMNo", 0);
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.SourcingResultId = GetReaderValue_NullableInt32(reader, "SourcingResultId", 0);
                    obj.RequestToPOHubBy = GetReaderValue_NullableInt32(reader, "RequestToPOHubBy", 0);
                    obj.BOMFullName = GetReaderValue_String(reader, "BOMFullName", "");
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.ConvertedTargetValue = GetReaderValue_Double(reader, "ConvertedTargetValue", 0);
                    obj.BOMCurrencyCode = GetReaderValue_String(reader, "BOMCurrencyCode", "");
                    obj.PurchaseQuoteId = GetReaderValue_NullableInt32(reader, "PurchaseQuoteId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.POHubCompany = GetReaderValue_NullableInt32(reader, "POHubCompany", 0);
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.FactorySealed = GetReaderValue_Boolean(reader, "FactorySealed", false);
                    obj.AllSorcingHasDelDate = GetReaderValue_Int32(reader, "AllSorcingHasDelDate", 0);
                    obj.AllSorcingHasProduct = GetReaderValue_Int32(reader, "AllSorcingHasProduct", 0);
                    obj.SourcingResult = GetReaderValue_Int32(reader, "SourcingResult", 0);
                    obj.BOMStatus = GetReaderValue_String(reader, "BOMStatus", "");
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.HasClientSourcingResult = GetReaderValue_Boolean(reader, "HasClientSourcingResult", false);
                    obj.HasHubSourcingResult = GetReaderValue_Boolean(reader, "HasHubSourcingResult", false);
                    obj.IsNoBid = GetReaderValue_NullableBoolean(reader, "IsNoBid", false);
                    obj.ExpeditDate = GetReaderValue_NullableDateTime(reader, "ExpediteDate", null);
                    obj.UpdateByPH = GetReaderValue_Int32(reader, "UpdateByPH", 0);
                    obj.AlternateStatus = GetReaderValue_NullableByte(reader, "AlternateStatus", (byte)0);
                    //obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    //obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    //obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    //obj.Shortage = GetReaderValue_Boolean(reader, "Shortage", false);
                    //obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    //obj.OriginalCustomerRequirementNo = GetReaderValue_NullableInt32(reader, "OriginalCustomerRequirementNo", null);
                    //obj.ReasonNo = GetReaderValue_NullableInt32(reader, "ReasonNo", null);
                    //obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    //obj.UsageNo = GetReaderValue_NullableInt32(reader, "UsageNo", null);
                    //obj.BOM = GetReaderValue_NullableBoolean(reader, "BOM", null);
                    //obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    //obj.PartWatch = GetReaderValue_NullableBoolean(reader, "PartWatch", null);
                    //obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    //obj.DisplayStatus = GetReaderValue_String(reader, "DisplayStatus", "");
                    //obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    //obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    //obj.CompanyOnStop = GetReaderValue_NullableBoolean(reader, "CompanyOnStop", null);
                    //obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    //obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    //obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    //obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    //obj.UsageName = GetReaderValue_String(reader, "UsageName", "");
                    //obj.CustomerRequirementValue = GetReaderValue_Double(reader, "CustomerRequirementValue", 0);
                    //obj.ClosedReason = GetReaderValue_String(reader, "ClosedReason", "");
                    //obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    //obj.PurchaseQuoteNumber = GetReaderValue_NullableInt32(reader, "PurchaseQuoteNumber", 0);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end by anand

        // [002] code start Here
        /// <summary>
        /// GetBOMListForCustomerRequirement 
        /// Calls [usp_selectCusReqForMail]
        /// </summary>
        public override List<CustomerRequirementDetails> GetHUBRFQForMail(System.Int32? BOMNo, System.Int32? clientID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectCusReqForMail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = BOMNo;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientID;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();

                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.ConvertedTargetValue = GetReaderValue_Double(reader, "ConvertedTargetValue", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.BOMCurrencyCode = GetReaderValue_String(reader, "BOMCurrencyCode", "");
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.FactorySealed = GetReaderValue_Boolean(reader, "FactorySealed", false);
                    obj.ReqTypeText = GetReaderValue_String(reader, "ReqTypeText", "");
                    obj.ReqForTraceabilityText = GetReaderValue_String(reader, "ReqForTraceability", "");
                    obj.AlternativesAccepted = GetReaderValue_Boolean(reader, "AlternativesAccepted", false);
                    obj.RepeatBusiness = GetReaderValue_Boolean(reader, "RepeatBusiness", false);
                    obj.AlternateStatus = GetReaderValue_NullableByte(reader, "AlternateStatus", (byte)0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.LyticaAveragePrice = GetReaderValue_Double(reader, "LyticaAveragePrice", 0);
                    obj.LyticaTargetPrice = GetReaderValue_Double(reader, "LyticaTargetPrice", 0);
                    obj.LyticaMarketLeading = GetReaderValue_Double(reader, "LyticaMarketLeading", 0);
                    obj.LyticaManufacturerRef = GetReaderValue_String(reader, "LyticaManufacturerRef", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        // [002] Code End Here


        /// <summary>
        /// GetBOMListForCustomerRequirement 
        /// Calls [usp_selectAll_CustomerRequirement_for_BOM]
        /// </summary>
        public override List<List<object>> GetBOMListForCRList(System.Int32? BOMNo, System.Int32? clientID, System.Int32? CompanyNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_CustomerRequirements_for_BOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = BOMNo;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientID;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = CompanyNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                if (reader.HasRows)
                {
                    return GetCustReqDataFromReader(reader);
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //[000] code start : BOM
        /// <summary>
        /// Release CustomerRequirement from purchase HUB
        /// Calls [usp_update_CustomerRequirement_Release]
        /// </summary>
        public override bool ReleaseRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy, System.Int32? bomID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustomerRequirement_Release", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomID;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[000] code End : BOM

        //[000] code start : BOM
        /// <summary>
        /// Release CustomerRequirement from purchase HUB
        /// Calls [usp_update_BOM_Release]
        /// </summary>
        public override bool BOMReleaseRequirement(System.Int32? bomID, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOM_Release", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomID;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                int ret = 1;
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[000] code End : BOM
        /// <summary>
        ///Recall NoBid CustomerRequirement from purchase HUB
        /// Calls [usp_update_CustomerRequirement_RecallNoBid]
        /// </summary>
        public override bool RecallNoBidRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustomerRequirement_RecallNoBid", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// NoBid CustomerRequirement from purchase HUB
        /// Calls [usp_update_CustomerRequirement_NoBid]
        /// </summary>
        public override bool NoBidRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy, System.Int32? bomID, string Notes)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustomerRequirement_NoBid", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomID;
                cmd.Parameters.Add("@NoBidNotes", SqlDbType.NVarChar).Value = Notes;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// NoBid CustomerRequirement from purchase HUB
        /// Calls [usp_update_BOM_NoBid]
        /// </summary>
        public override bool BOMNoBidRequirement(System.Int32? bomID, System.Int32? updatedBy, string Notes)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOM_NoBid", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomID;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@NoBidNotes", SqlDbType.NVarChar).Value = Notes;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();

                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override Int32 InsertExpedite(System.Int32? HUBRFQId, System.String expediteNotes, System.Int32? UpdatedBy, string ReqIds, System.Int32? emailSendTo, System.String CCUserID, System.String SendToGroup)
        {
            string GroupId = string.Format("{0:yyyy-MM-dd_hh-mm-ss}", DateTime.Now);
            GroupId = GroupId + Convert.ToString(UpdatedBy);
            GroupId = GroupId.Replace("_", "").Replace("-", "");
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_HUBRFQExpediteNote", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@HUBRFQId", SqlDbType.Int).Value = HUBRFQId; //226470
                cmd.Parameters.Add("@ExpediteNotes", SqlDbType.NVarChar).Value = expediteNotes; //Test1 Anuj
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy; //4408
                cmd.Parameters.Add("@ReqIds", SqlDbType.NVarChar).Value = ReqIds; //"4111068"
                cmd.Parameters.Add("@GroupId", SqlDbType.NVarChar).Value = GroupId; //"202308170523254408"
                cmd.Parameters.Add("@EmailSendTo", SqlDbType.Int).Value = emailSendTo; //3397
                cmd.Parameters.Add("@CCUserID", SqlDbType.NVarChar).Value = CCUserID;//null
                cmd.Parameters.Add("@SendToGroup", SqlDbType.NVarChar).Value = SendToGroup;//"HO"
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (int)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert HUBRFQ expedite note", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override Int32 InsertBOMExpediteNote(System.Int32? BOMId, System.String expediteNotes, System.Int32? UpdatedBy, string ReqIds, System.Int32? emailSendTo, System.String CCUserID, System.String SendToGroup)
        {
            string GroupId = string.Format("{0:yyyy-MM-dd_hh-mm-ss}", DateTime.Now);
            GroupId = GroupId + Convert.ToString(UpdatedBy);
            GroupId = GroupId.Replace("_", "").Replace("-", "");
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_BOMExpediteNote", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = BOMId; //226470
                cmd.Parameters.Add("@ExpediteNotes", SqlDbType.NVarChar).Value = expediteNotes; //Test1 Anuj
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy; //4408
                cmd.Parameters.Add("@ReqIds", SqlDbType.NVarChar).Value = ReqIds; //"4111068"
                cmd.Parameters.Add("@GroupId", SqlDbType.NVarChar).Value = GroupId; //"202308170523254408"
                cmd.Parameters.Add("@EmailSendTo", SqlDbType.Int).Value = emailSendTo; //3397
                cmd.Parameters.Add("@CCUserID", SqlDbType.NVarChar).Value = CCUserID;//null
                cmd.Parameters.Add("@SendToGroup", SqlDbType.NVarChar).Value = SendToGroup;//"HO"
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (int)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert HUBRFQ expedite note", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override Int32 InsertHUBRFQExpedite(System.Int32? HUBRFQId, System.String expediteNotes, System.Int32? UpdatedBy, System.Int32? emailSendTo)
        {

            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_HUBRFQMainInfo_ExpediteNote", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@HUBRFQId", SqlDbType.Int).Value = HUBRFQId;
                cmd.Parameters.Add("@ExpediteNotes", SqlDbType.NVarChar).Value = expediteNotes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@EmailSendTo", SqlDbType.Int).Value = emailSendTo;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (int)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert HUBRFQ MainInfo expedite note", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update CustomerRequirement
        /// Calls [usp_update_CustRequirementByBomID]
        /// </summary>
        public override bool Update(System.Int32? bomID, System.Int32? updatedBy, System.Int32? clientNo, System.String ReqsId, System.Int32? BOMStatus)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustRequirementByBomID", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Bomid", SqlDbType.Int).Value = bomID;
                cmd.Parameters.Add("@ReqIds", SqlDbType.NVarChar).Value = ReqsId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@BOMStatus", SqlDbType.Int).Value = BOMStatus;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// delete 
        /// Calls [usp_delete_CustomerRequirement_Bom]
        /// </summary>
        public override bool DeleteBomItem(int? bomId, int? requirementId, System.Int32? LoginID, System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_CustomerRequirement_Bom", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomID", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@RequirementID", SqlDbType.Int).Value = requirementId;
                cmd.Parameters.Add("@LoginID", SqlDbType.Int).Value = LoginID;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                int ret = cmd.ExecuteNonQuery();// ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to delete CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// delete 
        /// Calls [usp_UnRelease_CustomerRequirement_Bom]
        /// </summary>
        public override bool UnReleaseBomItem(int? bomId, int? requirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_UnRelease_CustomerRequirement_Bom", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = requirementId;
                cn.Open();
                int ret = cmd.ExecuteNonQuery();
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Revoke CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// DataListNugget  for HURFQ
        /// Calls [usp_datalistnugget_CustomerRequirementForHUBRFQ]
        ///  add start date and end date  for searching by umendra
        /// </summary>
        public override List<CustomerRequirementDetails> DataListNuggetHUBRFQ(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.DateTime? RequiredStartdate, System.DateTime? RequiredEndDate, System.Int32? salesPerson,System.Int32? AS6081Required, System.Int32? SelectedLoginId = null)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_CustomerRequirementForHUBRFQ", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;

                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@BOMCode", SqlDbType.NVarChar).Value = BOMCode;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = selectedClientNo;
                cmd.Parameters.Add("@BomStatus", SqlDbType.Int).Value = bomStatus;
                if (assignedUser > 0)
                {
                    cmd.Parameters.Add("@AssignedUser", SqlDbType.Int).Value = assignedUser;
                }

                cmd.Parameters.Add("@Manufacturer", SqlDbType.NVarChar).Value = Manufacturer;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = Part;
                cmd.Parameters.Add("@ClientDivisionNo", SqlDbType.Int).Value = intDivision;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startdate;//[004]  
                cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = enddate;//[004]  
                cmd.Parameters.Add("@RequiredStartDate", SqlDbType.DateTime).Value = RequiredStartdate;//[004]  
                cmd.Parameters.Add("@RequiredEndDate", SqlDbType.DateTime).Value = RequiredEndDate;
                cmd.Parameters.Add("@SalesPerson", SqlDbType.NVarChar).Value = salesPerson;//[003]
                cmd.Parameters.Add("@AS6081Required", SqlDbType.Int).Value = AS6081Required;
                cmd.Parameters.Add("@SelectedLoginId", SqlDbType.Int).Value = SelectedLoginId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMId", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.BOMStatus = GetReaderValue_String(reader, "Status", "");
                    obj.Price = GetReaderValue_Double(reader, "Price", 0.00);
                    obj.PHPrice = GetReaderValue_Double(reader, "PHPrice", 0.00);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyId", 0);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DateRequestToPOHub = GetReaderValue_DateTime(reader, "DateRequestToPOHub", DateTime.MinValue);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.AlternateStatus = GetReaderValue_NullableByte(reader, "AlternateStatus", (byte)0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", null);
                    obj.SupportTeamMemberName = GetReaderValue_String(reader, "SupportTeamMemberName", "");
                    obj.RequiredDate = GetReaderValue_NullableDateTime(reader, "RequiredDate", null);
                    obj.RequiredDateStatus = GetReaderValue_String(reader, "RequiredDateStatus", "");
                    obj.ExpediteNotes = GetReaderValue_String(reader, "ExpediteNotes", "");
                    obj.AssignedTo = GetReaderValue_String(reader, "AssignedTo", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

            /// <summary>
        /// DataListNugget  for HURFQ
        /// Calls [usp_datalistnugget_CustomerRequirementForHUBRFQ]
        ///  add start date and end date  for searching by umendra
        /// </summary>
        public override List<CustomerRequirementDetails> DataListNuggetHUBRFQBOMManager(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.Int32? salesPerson, System.Int32? MailGroupId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_CustomerRequirementForHUBRFQBOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;

                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@BOMManagerCode", SqlDbType.NVarChar).Value = BOMCode;
                cmd.Parameters.Add("@BOMManagerName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = selectedClientNo;
                cmd.Parameters.Add("@BOMManagerStatus", SqlDbType.Int).Value = bomStatus;
                if (assignedUser > 0)
                {
                    cmd.Parameters.Add("@AssignedUser", SqlDbType.Int).Value = assignedUser;
                }

                cmd.Parameters.Add("@Manufacturer", SqlDbType.NVarChar).Value = Manufacturer;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = Part;
                cmd.Parameters.Add("@ClientDivisionNo", SqlDbType.Int).Value = intDivision;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startdate;//[004]  
                cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = enddate;//[004]  
                cmd.Parameters.Add("@SalesPerson", SqlDbType.NVarChar).Value = salesPerson;//[003]
                cmd.Parameters.Add("@MailGroupId", SqlDbType.Int).Value = MailGroupId;//[018]
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.BOMCode = GetReaderValue_String(reader, "BOMManagerCode", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMManagerId", 0);
                    //obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.BOMName = GetReaderValue_String(reader, "BOMManagerName", "");
                    obj.BOMStatus = GetReaderValue_String(reader, "Status", "");
                    obj.Price = GetReaderValue_Double(reader, "Price", 0.00);
                    obj.PHPrice = GetReaderValue_Double(reader, "PHPrice", 0.00);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyId", 0);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.DateRequestToPOHub = GetReaderValue_DateTime(reader, "DateRequestToPOHub", DateTime.MinValue);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.AlternateStatus = GetReaderValue_NullableByte(reader, "AlternateStatus", (byte)0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", null);
                    obj.SupportTeamMemberName = GetReaderValue_String(reader, "SupportTeamMemberName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<CustomerRequirementDetails> GetHUBRFQReqNos(string ReqIds, System.Int32? clientID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_HUBRFQItemByReqIds", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ReqIds", SqlDbType.NVarChar).Value = ReqIds;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientID;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();

                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);

                    obj.BOMHeader = GetReaderValue_String(reader, "BOMHeader", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMNo", 0);

                    obj.RequestToPOHubBy = GetReaderValue_NullableInt32(reader, "RequestToPOHubBy", 0);
                    obj.BOMFullName = GetReaderValue_String(reader, "BOMFullName", "");
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");

                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);



                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// usp_update_CustReqAlternate_Status
        /// </summary>
        /// <param name="customerRequirementId"></param>
        /// <param name="altStatus"></param>
        /// <param name="updatedBy"> </param>
        /// <returns></returns>
        public override bool ChangeAltStatus(System.Int32? customerRequirementId, System.Byte? altStatus, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustReqAlternate_Status", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@AltStatus", SqlDbType.TinyInt).Value = altStatus;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement Status", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable DataListNuggetHUBRFQ_Export(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.Int32? salesPerson)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_CustomerRequirementForHUBRFQ_Export", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;

                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@BOMCode", SqlDbType.NVarChar).Value = BOMCode;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = selectedClientNo;
                cmd.Parameters.Add("@BomStatus", SqlDbType.Int).Value = bomStatus;
                if (assignedUser > 0)
                {
                    cmd.Parameters.Add("@AssignedUser", SqlDbType.Int).Value = assignedUser;
                }

                cmd.Parameters.Add("@Manufacturer", SqlDbType.NVarChar).Value = Manufacturer;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = Part;
                cmd.Parameters.Add("@ClientDivisionNo", SqlDbType.Int).Value = intDivision;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startdate;//[004]  
                cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = enddate;//[004]  
                cmd.Parameters.Add("@SalesPerson", SqlDbType.NVarChar).Value = salesPerson;//[003]

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);

                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //IHS Export to csv
        public override DataTable DataListNuggetIHS_Export(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String MfrSearch, System.Int32? countryOforigin, System.String MSL, System.String HtcCode, System.String Description, System.Boolean? recentOnly, int? IsPoHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_IHSCatalogue_Export", cn);
                //cmd = new SqlCommand("usp_datalistnugget_IHSCatalogue_Export_Backup_08_02_2021", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Manufacturer", SqlDbType.NVarChar).Value = MfrSearch;
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = countryOforigin;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = HtcCode;
                cmd.Parameters.Add("@Description", SqlDbType.NVarChar).Value = Description;
                cmd.Parameters.Add("@RecentOnly", SqlDbType.Bit).Value = recentOnly;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Int).Value = IsPoHub ?? 0;
                //cmd.CommandTimeout = 60;
                //cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                //cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                //cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                //cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                //cmd.Parameters.Add("@Manufacturer", SqlDbType.NVarChar).Value = Manufacturer;
                //cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = Part;
                //cmd.Parameters.Add("@CountryOfOrigin", SqlDbType.NVarChar).Value = countryOforigin;
                //cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                //cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = HtcCode;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);

                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get IHS Catalogue Export To Csv", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //IHS Export to csv end

        public override DataTable GetBOMDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int BomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetBomData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@displayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@displayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@sortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@sortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@search", SqlDbType.NVarChar, 255).Value = search;//[004]  
                cmd.Parameters.Add("@userid", SqlDbType.Int).Value = userid;//[003]
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = BomId;//[003]
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetCustTableAllColumn()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetAllColunCustReq", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get column list", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void SaveMappingDetail(int columnIndex, int columnId, int bomId, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_saveColumnMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ColumnIndex", SqlDbType.Int).Value = columnIndex;
                cmd.Parameters.Add("@CustReqColumnNo", SqlDbType.Int).Value = columnId;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@CreatedBy", SqlDbType.Int).Value = userId;
                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void AutoMapRemainingColumn(int bomId, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AutoMapRemainingColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@CreatedBy", SqlDbType.Int).Value = userId;
                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool ValidateBOMData(string recordIds, int bomId, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_ValidateBOMData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@RecordIds", SqlDbType.NVarChar, 3000).Value = recordIds;
                cmd.Parameters.Add("@BOMId", SqlDbType.NVarChar, 3000).Value = bomId;
                cmd.Parameters.Add("@UserId", SqlDbType.NVarChar, 3000).Value = userId;
                cmd.Parameters.Add("@ValidationPassed", SqlDbType.Bit).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                return (bool)cmd.Parameters["@ValidationPassed"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to validate bom detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int SaveCompanyColumnMapping(int bomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SaveCompanyColumnMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = bomId;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert company mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void SaveUpdateRecord(DataTable dt, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SaveUpdateTempReqData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@RecordId", SqlDbType.Int).Value = int.Parse(dt.Rows[0]["RecordId"].ToString());
                cmd.Parameters.Add("@Column1", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column1"].ToString();
                cmd.Parameters.Add("@Column2", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column2"].ToString();
                cmd.Parameters.Add("@Column3", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column3"].ToString();
                cmd.Parameters.Add("@Column4", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column4"].ToString();
                cmd.Parameters.Add("@Column5", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column5"].ToString();
                cmd.Parameters.Add("@Column6", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column6"].ToString();
                cmd.Parameters.Add("@Column7", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column7"].ToString();
                cmd.Parameters.Add("@Column8", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column8"].ToString();
                cmd.Parameters.Add("@Column9", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column9"].ToString();
                cmd.Parameters.Add("@Column10", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column10"].ToString();
                cmd.Parameters.Add("@Column11", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column11"].ToString();
                cmd.Parameters.Add("@Column12", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column12"].ToString();
                cmd.Parameters.Add("@Column13", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column13"].ToString();
                cmd.Parameters.Add("@Column14", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column14"].ToString();
                cmd.Parameters.Add("@Column15", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column15"].ToString();
                cmd.Parameters.Add("@Column16", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column16"].ToString();
                cmd.Parameters.Add("@Column17", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column17"].ToString();
                cmd.Parameters.Add("@Column18", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column18"].ToString();
                cmd.Parameters.Add("@Column19", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column19"].ToString();
                cmd.Parameters.Add("@Column20", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column20"].ToString();
                cmd.Parameters.Add("@Column21", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column21"].ToString();
                cmd.Parameters.Add("@Column22", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column22"].ToString();
                cmd.Parameters.Add("@Column23", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column23"].ToString();
                cmd.Parameters.Add("@Column24", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column24"].ToString();
                cmd.Parameters.Add("@Column25", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column25"].ToString();
                cmd.Parameters.Add("@Column26", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column26"].ToString();
                cmd.Parameters.Add("@Column27", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column27"].ToString();
                cmd.Parameters.Add("@Column28", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column28"].ToString();
                cmd.Parameters.Add("@Column29", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column29"].ToString();
                cmd.Parameters.Add("@Column30", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column30"].ToString();
                cmd.Parameters.Add("@Column31", SqlDbType.NVarChar, 500).Value = dt.Rows[0]["Column31"].ToString();
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = int.Parse(dt.Rows[0]["BOMId"].ToString());
                cmd.Parameters.Add("@ManufacturerId", SqlDbType.Int).Value = int.Parse(dt.Rows[0]["ManufacturerId"].ToString());
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Value = int.Parse(dt.Rows[0]["ProductId"].ToString());
                cmd.Parameters.Add("@CreatedBy", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@OtherMapping", SqlDbType.NVarChar, 4000).Value = "";

                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void DeleteTempMapping(int bomId, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Del_Temp_Mapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;

                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int DeleteRecord(int recordId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_deleteTempRecord", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@RecordId", SqlDbType.Int).Value = recordId;
                cn.Open();
                return ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetBOMInfo(int BOMId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetBOMDetail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = BOMId;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetCompanyAndOtherMasterData(int CompanyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetCompany_And_OtherMasterData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CompanyId", SqlDbType.Int).Value = CompanyId;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get master data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetSalespersonList(int ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_Login_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientId;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get sales person data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetAllColumn(int bomId, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_AllColumn", cn);
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetMappedColumn(int clientBomId, int loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_ColMappingDetail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = clientBomId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get mapped column detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetRecordDetail(int bomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_RecordDetail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get record detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetValidationError(int bomId, int userId, string RecordIds)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetValidationError", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@RecordIds", SqlDbType.VarChar, 3000).Value = RecordIds;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get validation error detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int ProcessRecord(int bomId, int userId, int currentIndex, int pageSize, string recordIds)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Trans_TempReq_MainReq", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@CurrentIndex", SqlDbType.Int).Value = currentIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@RecordIds", SqlDbType.NVarChar, 3000).Value = recordIds;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();
                cmd.ExecuteNonQuery();
                return (Int32)cmd.Parameters["@RecordCount"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to process record", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override int UpdateBOMStatus(int bomId, string status)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_UpdateBOMStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@Status", SqlDbType.VarChar, 50).Value = status;

                cn.Open();
                return cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update bom status", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void saveExcelData(DataTable testBOM, int bomId)
        {
            //SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                using (SqlConnection dbConnection = new SqlConnection(this.ConnectionString))
                {
                    dbConnection.Open();
                    if (dbConnection.State == ConnectionState.Open)
                    {

                        using (var tran = dbConnection.BeginTransaction(IsolationLevel.ReadCommitted))
                        {
                            using (SqlBulkCopy s = new SqlBulkCopy(dbConnection, SqlBulkCopyOptions.Default, tran))
                            {
                                s.DestinationTableName = testBOM.TableName;

                                foreach (var column in testBOM.Columns)
                                    s.ColumnMappings.Add(column.ToString(), column.ToString());

                                s.WriteToServer(testBOM);
                                tran.Commit();
                                //update status of bom
                                if (testBOM.Rows.Count > 0)
                                    this.UpdateBOMStatus(bomId, "Open");
                            }
                        }
                        // update record processed count
                        cmd = new SqlCommand("usp_UpdateBomProcessedRecord", dbConnection);
                        cmd.CommandType = CommandType.StoredProcedure;
                        cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                        cmd.ExecuteNonQuery();
                    }
                }

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert excel data", sqlex);
            }

        }
        public override Int32 saveBomFileInfo(int bomId, string caption, string filename, int loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SaveBomExcelInfo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientBomNo", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar, 1000).Value = caption;
                cmd.Parameters.Add("@Filename", SqlDbType.NVarChar, 1000).Value = filename;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@FileLogId", SqlDbType.Int).Direction = ParameterDirection.Output;


                cn.Open();
                cmd.ExecuteNonQuery();
                return (Int32)cmd.Parameters["@FileLogId"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to update bom file info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        // [007] code start
        /// <summary>
        /// GetBOMListForCustomerRequirement 
        /// Calls [usp_selectAll_CustomerRequirement_for_ClientBOM]
        /// </summary>
        public override List<CustomerRequirementDetails> GetClientBOMListForCustomerRequirement(System.Int32? BOMNo, System.Int32? clientID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CustomerRequirement_for_ClientBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = BOMNo;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientID;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.Closed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.Alternate = GetReaderValue_Boolean(reader, "Alternate", false);
                    obj.Instructions = GetReaderValue_String(reader, "Instructions", "");
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.DatePromised = GetReaderValue_DateTime(reader, "DatePromised", DateTime.MinValue);
                    obj.BOMHeader = GetReaderValue_String(reader, "BOMHeader", "");
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMNo", 0);
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.SourcingResultId = GetReaderValue_NullableInt32(reader, "SourcingResultId", 0);
                    //obj.RequestToPOHubBy = GetReaderValue_NullableInt32(reader, "RequestToPOHubBy", 0);
                    obj.BOMFullName = GetReaderValue_String(reader, "BOMFullName", "");
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.ConvertedTargetValue = GetReaderValue_Double(reader, "ConvertedTargetValue", 0);
                    obj.BOMCurrencyCode = GetReaderValue_String(reader, "BOMCurrencyCode", "");
                    obj.PurchaseQuoteId = GetReaderValue_NullableInt32(reader, "PurchaseQuoteId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.POHubCompany = GetReaderValue_NullableInt32(reader, "POHubCompany", 0);
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.FactorySealed = GetReaderValue_Boolean(reader, "FactorySealed", false);
                    obj.AllSorcingHasDelDate = GetReaderValue_Int32(reader, "AllSorcingHasDelDate", 0);
                    obj.AllSorcingHasProduct = GetReaderValue_Int32(reader, "AllSorcingHasProduct", 0);
                    obj.SourcingResult = GetReaderValue_Int32(reader, "SourcingResult", 0);
                    obj.BOMStatus = GetReaderValue_String(reader, "BOMStatus", "");
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.HasClientSourcingResult = GetReaderValue_Boolean(reader, "HasClientSourcingResult", false);
                    obj.HasHubSourcingResult = GetReaderValue_Boolean(reader, "HasHubSourcingResult", false);
                    obj.IsNoBid = GetReaderValue_NullableBoolean(reader, "IsNoBid", false);
                    obj.ExpeditDate = GetReaderValue_NullableDateTime(reader, "ExpediteDate", null);
                    obj.UpdateByPH = GetReaderValue_Int32(reader, "UpdateByPH", 0);
                    obj.AlternateStatus = GetReaderValue_NullableByte(reader, "AlternateStatus", (byte)0);


                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        // [007] code end


        //[008] code start
        /// <summary>
        /// ItemSearch 
        /// Calls [usp_itemsearch_CustRequirementWithoutBOM]
        /// </summary>
        public override List<CustomerRequirementDetails> ItemSearchWithoutClientBOM(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Boolean? includeClosed, System.Int32? customerRequirementNoLo, System.Int32? customerRequirementNoHi, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.Int32? bomId, System.String BOMName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_itemsearch_CustRequirementWithoutClientBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@CMSearch", SqlDbType.NVarChar).Value = cmSearch;
                cmd.Parameters.Add("@IncludeClosed", SqlDbType.Bit).Value = includeClosed;
                cmd.Parameters.Add("@CustomerRequirementNoLo", SqlDbType.Int).Value = customerRequirementNoLo;
                cmd.Parameters.Add("@CustomerRequirementNoHi", SqlDbType.Int).Value = customerRequirementNoHi;
                cmd.Parameters.Add("@ReceivedDateFrom", SqlDbType.DateTime).Value = receivedDateFrom;
                cmd.Parameters.Add("@ReceivedDateTo", SqlDbType.DateTime).Value = receivedDateTo;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = BOMName;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNumber = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.BOMHeader = GetReaderValue_String(reader, "BOMName", "");
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[008] code end

        /// <summary>
        /// Update CustomerRequirement
        /// Calls [usp_update_CustRequirementByClientBomID]
        /// </summary>
        public override bool ClientBOMItemsUpdate(System.Int32? bomID, System.Int32? updatedBy, System.Int32? clientNo, System.String ReqsId, System.Int32? BOMStatus)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustRequirementByClientBomID", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Bomid", SqlDbType.Int).Value = bomID;
                cmd.Parameters.Add("@ReqIds", SqlDbType.NVarChar).Value = ReqsId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@BOMStatus", SqlDbType.Int).Value = BOMStatus;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<PDFDocumentDetails> GetBomUploadedFiles(int bomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetBomExcelFiles", cn);
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomId;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PDFDocumentDetails> lstPDF = new List<PDFDocumentDetails>();
                while (reader.Read())
                {
                    PDFDocumentDetails obj = new PDFDocumentDetails();
                    obj.PDFDocumentId = GetReaderValue_Int32(reader, "BomExcelId", 0);
                    obj.FileName = GetReaderValue_String(reader, "Caption", "");
                    obj.GeneratedFileName = GetReaderValue_String(reader, "Filename", "");
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Bom File detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void ResetBomData(int bomId, int userId, int filelogid)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_ResetBomData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@FileLogId", SqlDbType.Int).Value = filelogid;

                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to reset bom detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update CustomerRequirement
        /// Calls [usp_update_CustRequirementByClientBomID]
        /// </summary>
        public override bool ClientBOMCustomerRequirementWithHUBRFQUpdate(System.Int32? bomID, System.Int32? CompanyNo, System.Int32? CustomerRequirementNumber, System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_CustomerRequirementWithHUBRFQ", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Bomid", SqlDbType.Int).Value = bomID;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.NVarChar).Value = CompanyNo;
                cmd.Parameters.Add("@CustomerRequirementNumber", SqlDbType.Int).Value = CustomerRequirementNumber;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void RearrangeColumnData(int bomId, int userId, string insertColumnList, string selectColumnList)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_ArrangeTempColumnData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@InsertColumnList", SqlDbType.NVarChar, 1000).Value = insertColumnList;
                cmd.Parameters.Add("@SelectColumnList", SqlDbType.NVarChar, 1000).Value = selectColumnList;

                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to reset bom detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void ReArrangeCompanyMapping(int columnIndex, int columnId, int bomId, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_ReArrangeCompanyMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ColumnIndex", SqlDbType.Int).Value = columnIndex;
                cmd.Parameters.Add("@CustReqColumnNo", SqlDbType.Int).Value = columnId;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@CreatedBy", SqlDbType.Int).Value = userId;
                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to rearrange company column mapping", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void UpdateCompanyMapping(int bomId, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_UpdateCompanyMapping", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@CreatedBy", SqlDbType.Int).Value = userId;
                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update company column mapping", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool IsCompanyMappingExists(int bomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_IsCompanyMappingExists", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;

                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = bomId;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                bool isCompanyMappingExists = false;
                while (reader.Read())
                {
                    isCompanyMappingExists = GetReaderValue_Boolean(reader, "CompanyMappingExists", false);
                }
                return isCompanyMappingExists;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to check company column mapping", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override void UpdateCell(DataTable dt, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_updatecell", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@RecordId", SqlDbType.Int).Value = int.Parse(dt.Rows[0]["RecordId"].ToString());
                cmd.Parameters.Add("@ColumnName", SqlDbType.NVarChar, 100).Value = dt.Rows[0]["ColumnName"].ToString();
                cmd.Parameters.Add("@ColumnValue", SqlDbType.NVarChar, 4000).Value = dt.Rows[0]["ColumnValue"].ToString();
                cmd.Parameters.Add("@ManufacturerId", SqlDbType.Int).Value = dt.Rows[0]["ManufacturerId"].ToString();
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Value = dt.Rows[0]["ProductId"].ToString();
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;


                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert mapping detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[006] start
        public override List<CustomerRequirementDetails> GetListCustomerAllInfo(System.String DocNo, System.String actionType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_AllDocument_Detail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@DocNo", SqlDbType.NVarChar, 50).Value = DocNo;
                cmd.Parameters.Add("@ActionType", SqlDbType.Char, 10).Value = actionType;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CustomerRequirementDetails> lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();

                    obj.ID = GetReaderValue_Int32(reader, "ID", 0);
                    obj.Number = GetReaderValue_String(reader, "Number", "");
                    obj.ResultType = GetReaderValue_String(reader, "ResultType", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirements", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //[006] end
        public override void SaveExcelHeader(string columnList, string insertColumnList, int bomId, int loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_saveExcelColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InserColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@ClientBomId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;


                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to update bom file info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetExcelHeader(System.Int32 clientBomId, System.Int32 userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetExcelHeaderColumn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientBomId", SqlDbType.Int).Value = clientBomId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get excel header", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetCurrencyCode(string currencyCode)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_getCurrencyCode", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CurrencyCode", SqlDbType.Char, 3).Value = currencyCode;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get currency code", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //start code by Manish
        public override DataTable GetClientCurrencyCode(string currencyCode, System.Int32? ClientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_getClientCurrencyCode", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CurrencyCode", SqlDbType.VarChar, 15).Value = currencyCode;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cmd.CommandTimeout = 60;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get currency code", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        
        //start code by umendra
        public override void SaveUpdateTempCusReqData(int tempid, int bomid, int clientid, string manufacturename)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SaveUpdateTempCustReqData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@TempId", SqlDbType.Int).Value = tempid;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomid;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientid;
                cmd.Parameters.Add("@ManufacturerName", SqlDbType.NVarChar, 3000).Value = manufacturename;
                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Update Customer Requirement data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void SaveUpdateTempSourcingData(int sourcingresultid, string suppliername, int clientid, string manufacturename)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SaveUpdateTempSourcingData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SourcingResultId", SqlDbType.Int).Value = sourcingresultid;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientid;
                cmd.Parameters.Add("@ManufacturerName", SqlDbType.NVarChar, 3000).Value = manufacturename;
                cmd.Parameters.Add("@SupplierName", SqlDbType.NVarChar, 3000).Value = suppliername;
                cn.Open();
                ExecuteNonQuery(cmd);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Update Sourcing Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end code by umendra
        /// <summary>
        /// Clone the existing customer requirement and also add existing HUBRFQ
        /// Calls [usp_CloneRequirementDataHUBRFQ]
        /// </summary>
        /// <param name="customerRequirementNumber"></param>
        /// <param name="hubrfqId"></param>
        /// <param name="clientNo"></param>
        /// <param name="loginId"></param>
        /// <returns></returns>
        //public override Int32 CloneRequirementDataHUBRFQ(System.Int32 customerRequirementNumber, System.Int32 hubrfqId, System.Int32? clientNo, System.Int32 loginId)
        //{
        //    SqlConnection cn = null;
        //    SqlCommand cmd = null;
        //    try
        //    {
        //        cn = new SqlConnection(this.ConnectionString);
        //        cmd = new SqlCommand("usp_CloneRequirementDataHUBRFQ", cn);
        //        cmd.CommandType = CommandType.StoredProcedure;
        //        cmd.Parameters.Add("@CustReqNum", SqlDbType.Int).Value = customerRequirementNumber;
        //        cmd.Parameters.Add("@HUBRFQId", SqlDbType.Int).Value = hubrfqId;
        //        cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
        //        cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = loginId;
        //        cmd.Parameters.Add("@NewCustomerRequirementId", SqlDbType.Int).Direction = ParameterDirection.Output;
        //        cn.Open();
        //        int ret = ExecuteNonQuery(cmd);
        //        return (Int32)cmd.Parameters["@NewCustomerRequirementId"].Value;
        //    }
        //    catch (SqlException sqlex)
        //    {
        //        throw new Exception("Failed to insert CloneRequirementDataHUBRFQ", sqlex);
        //    }
        //    finally
        //    {
        //        cmd.Dispose();
        //        cn.Close();
        //        cn.Dispose();
        //    }
        //}
        /// <summary>
        /// Calls [usp_CloneRequirementDataHUB]
        /// </summary>
        /// <param name="customerRequirementNumber"></param>
        /// <param name="clientNo"></param>
        /// <param name="loginId"></param>
        /// <returns></returns>
        //public override Int32 CloneRequirementDataHUB(System.Int32 customerRequirementNumber, System.Int32? clientNo, System.Int32 loginId)
        //{
        //    SqlConnection cn = null;
        //    SqlCommand cmd = null;
        //    try
        //    {
        //        cn = new SqlConnection(this.ConnectionString);
        //        cmd = new SqlCommand("usp_CloneRequirementDataHUB", cn);
        //        cmd.CommandType = CommandType.StoredProcedure;
        //        cmd.Parameters.Add("@CustReqNum", SqlDbType.Int).Value = customerRequirementNumber;
        //        cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
        //        cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = loginId;
        //        cmd.Parameters.Add("@NewCustomerRequirementId", SqlDbType.Int).Direction = ParameterDirection.Output;
        //        cn.Open();
        //        int ret = ExecuteNonQuery(cmd);
        //        return (Int32)cmd.Parameters["@NewCustomerRequirementId"].Value;
        //    }
        //    catch (SqlException sqlex)
        //    {
        //        throw new Exception("Failed to insert CloneRequirementDataHUB", sqlex);
        //    }
        //    finally
        //    {
        //        cmd.Dispose();
        //        cn.Close();
        //        cn.Dispose();
        //    }
        //}


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_CloneRequirementDataHUBRFQ]
        /// </summary>
        public override Int32 CloneRequirementDataHUBRFQ(System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.Int32? CustReqNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packing, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_CloneRequirementDataHUBRFQ", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@ReceivedDate", SqlDbType.DateTime).Value = receivedDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@DatePromised", SqlDbType.DateTime).Value = datePromised;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@Instructions", SqlDbType.NVarChar).Value = instructions;
                cmd.Parameters.Add("@Shortage", SqlDbType.Bit).Value = shortage;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = 0;
                cmd.Parameters.Add("@UsageNo", SqlDbType.Int).Value = usageNo;
                cmd.Parameters.Add("@Alternate", SqlDbType.Bit).Value = alternate;
                cmd.Parameters.Add("@OriginalCustomerRequirementNo", SqlDbType.Int).Value = originalCustomerRequirementNo;
                cmd.Parameters.Add("@ReasonNo", SqlDbType.Int).Value = reasonNo;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@CustomerPart", SqlDbType.NVarChar).Value = customerPart;
                cmd.Parameters.Add("@Closed", SqlDbType.Bit).Value = closed;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@PartWatch", SqlDbType.Bit).Value = partWatch;
                cmd.Parameters.Add("@BOM", SqlDbType.Bit).Value = bom;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = BOMNo;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.Bit).Value = FactorySealed;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;

                cmd.Parameters.Add("@PartialQuantityAcceptable", SqlDbType.Bit).Value = PQA;
                cmd.Parameters.Add("@Obsolete", SqlDbType.Bit).Value = ObsoleteChk;
                cmd.Parameters.Add("@LastTimeBuy", SqlDbType.Bit).Value = LastTimeBuyChk;
                cmd.Parameters.Add("@RefirbsAcceptable", SqlDbType.Bit).Value = RefirbsAcceptableChk;
                cmd.Parameters.Add("@TestingRequired", SqlDbType.Bit).Value = TestingRequiredChk;
                cmd.Parameters.Add("@TargetSellPrice", SqlDbType.Float).Value = TargetSellPrice;
                cmd.Parameters.Add("@CompetitorBestOffer", SqlDbType.Float).Value = CompetitorBestOffer;
                cmd.Parameters.Add("@CustomerDecisionDate", SqlDbType.DateTime).Value = CustomerDecisionDate;
                cmd.Parameters.Add("@RFQClosingDate", SqlDbType.DateTime).Value = RFQClosingDate;
                cmd.Parameters.Add("@QuoteValidityRequired", SqlDbType.Int).Value = QuoteValidityRequired;
                cmd.Parameters.Add("@Type", SqlDbType.Int).Value = Type;
                cmd.Parameters.Add("@OrderToPlace", SqlDbType.Int).Value = OrderToPlace;
                cmd.Parameters.Add("@RequirementForTraceability", SqlDbType.Int).Value = RequirementforTraceability;
                cmd.Parameters.Add("@EAU", SqlDbType.NVarChar).Value = EAU;
                cmd.Parameters.Add("@AlternativesAccepted", SqlDbType.Bit).Value = AlternativesAccepted;
                cmd.Parameters.Add("@RepeatBusiness", SqlDbType.Bit).Value = RepeatBusiness;
                cmd.Parameters.Add("@SupportTeamMemberNo", SqlDbType.Int).Value = SupportTeamMemberNo;
                cmd.Parameters.Add("@CustReqNum", SqlDbType.Int).Value = CustReqNo;
                //IHS code start
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = CountryOfOriginNo;
                cmd.Parameters.Add("@LifeCycleStage", SqlDbType.NVarChar).Value = LifeCycleStage;
                cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = HTSCode;
                cmd.Parameters.Add("@AveragePrice", SqlDbType.Float).Value = AveragePrice;
                cmd.Parameters.Add("@Packing", SqlDbType.NVarChar).Value = Packing;
                cmd.Parameters.Add("@PackagingSize", SqlDbType.NVarChar).Value = PackagingSize;
                cmd.Parameters.Add("@Descriptions", SqlDbType.NVarChar).Value = Descriptions;
                cmd.Parameters.Add("@IHSPartsNo", SqlDbType.Int).Value = IHSPartsId;
                cmd.Parameters.Add("@IHSCurrencyCode", SqlDbType.NVarChar).Value = IHSCurrencyCode;
                cmd.Parameters.Add("@IHSProduct", SqlDbType.NVarChar).Value = IHSProduct;
                cmd.Parameters.Add("@ECCNCode", SqlDbType.NVarChar).Value = ECCNCode;
                cmd.Parameters.Add("@ECCNNo", SqlDbType.Int).Value = ECCNNo;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@CustomerRequirementId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Clone Requirement Data HUBRFQ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_CloneRequirementDataHUB]
        /// </summary>
        public override Int32 CloneRequirementDataHUB(System.Int32? clientNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? BOMNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.Int32? CustReqNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packing, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_CloneRequirementDataHUB", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@ReceivedDate", SqlDbType.DateTime).Value = receivedDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@DatePromised", SqlDbType.DateTime).Value = datePromised;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@Instructions", SqlDbType.NVarChar).Value = instructions;
                cmd.Parameters.Add("@Shortage", SqlDbType.Bit).Value = shortage;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = 0;
                cmd.Parameters.Add("@UsageNo", SqlDbType.Int).Value = usageNo;
                cmd.Parameters.Add("@Alternate", SqlDbType.Bit).Value = alternate;
                cmd.Parameters.Add("@OriginalCustomerRequirementNo", SqlDbType.Int).Value = originalCustomerRequirementNo;
                cmd.Parameters.Add("@ReasonNo", SqlDbType.Int).Value = reasonNo;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@CustomerPart", SqlDbType.NVarChar).Value = customerPart;
                cmd.Parameters.Add("@Closed", SqlDbType.Bit).Value = closed;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@PartWatch", SqlDbType.Bit).Value = partWatch;
                cmd.Parameters.Add("@BOM", SqlDbType.Bit).Value = bom;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = BOMNo;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.Bit).Value = FactorySealed;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;

                cmd.Parameters.Add("@PartialQuantityAcceptable", SqlDbType.Bit).Value = PQA;
                cmd.Parameters.Add("@Obsolete", SqlDbType.Bit).Value = ObsoleteChk;
                cmd.Parameters.Add("@LastTimeBuy", SqlDbType.Bit).Value = LastTimeBuyChk;
                cmd.Parameters.Add("@RefirbsAcceptable", SqlDbType.Bit).Value = RefirbsAcceptableChk;
                cmd.Parameters.Add("@TestingRequired", SqlDbType.Bit).Value = TestingRequiredChk;
                cmd.Parameters.Add("@TargetSellPrice", SqlDbType.Float).Value = TargetSellPrice;
                cmd.Parameters.Add("@CompetitorBestOffer", SqlDbType.Float).Value = CompetitorBestOffer;
                cmd.Parameters.Add("@CustomerDecisionDate", SqlDbType.DateTime).Value = CustomerDecisionDate;
                cmd.Parameters.Add("@RFQClosingDate", SqlDbType.DateTime).Value = RFQClosingDate;
                cmd.Parameters.Add("@QuoteValidityRequired", SqlDbType.Int).Value = QuoteValidityRequired;
                cmd.Parameters.Add("@Type", SqlDbType.Int).Value = Type;
                cmd.Parameters.Add("@OrderToPlace", SqlDbType.Int).Value = OrderToPlace;
                cmd.Parameters.Add("@RequirementForTraceability", SqlDbType.Int).Value = RequirementforTraceability;
                cmd.Parameters.Add("@EAU", SqlDbType.NVarChar).Value = EAU;
                cmd.Parameters.Add("@AlternativesAccepted", SqlDbType.Bit).Value = AlternativesAccepted;
                cmd.Parameters.Add("@RepeatBusiness", SqlDbType.Bit).Value = RepeatBusiness;
                cmd.Parameters.Add("@SupportTeamMemberNo", SqlDbType.Int).Value = SupportTeamMemberNo;
                cmd.Parameters.Add("@CustReqNum", SqlDbType.Int).Value = CustReqNo;
                //IHS code start
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = CountryOfOriginNo;
                cmd.Parameters.Add("@LifeCycleStage", SqlDbType.NVarChar).Value = LifeCycleStage;
                cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = HTSCode;
                cmd.Parameters.Add("@AveragePrice", SqlDbType.Float).Value = AveragePrice;
                cmd.Parameters.Add("@Packing", SqlDbType.NVarChar).Value = Packing;
                cmd.Parameters.Add("@PackagingSize", SqlDbType.NVarChar).Value = PackagingSize;
                cmd.Parameters.Add("@Descriptions", SqlDbType.NVarChar).Value = Descriptions;
                cmd.Parameters.Add("@IHSPartsNo", SqlDbType.Int).Value = IHSPartsId;
                cmd.Parameters.Add("@IHSCurrencyCode", SqlDbType.NVarChar).Value = IHSCurrencyCode;
                cmd.Parameters.Add("@IHSProduct", SqlDbType.NVarChar).Value = IHSProduct;
                cmd.Parameters.Add("@ECCNCode", SqlDbType.NVarChar).Value = ECCNCode;
                cmd.Parameters.Add("@ECCNNo", SqlDbType.Int).Value = ECCNNo;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.CommandTimeout = 90;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@CustomerRequirementId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Clone Requirement Data HUB", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetPartMachInfo(System.Int32? clientId, System.String partSearch, System.Int32? CustomerRequirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_partwatch_matching_Offer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustomerRequirementId;
                cmd.Parameters.Add("@MatchCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        public override DataTable GetPartMachHUBIPOInfo(System.Int32? clientId, System.Int32? updatedBy, System.Int32? CustomerRequirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_partwatch_HUBIPO_matching_Offer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@updatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustomerRequirementId;
                cmd.Parameters.Add("@MatchCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //public override void AddSourcingResultsforMatchedOffers(System.Int32? CustomerRequirementId, DataTable dtOffer)
        //{
        //    SqlConnection cn = null;
        //    SqlCommand cmd = null;
        //    try
        //    {
        //        cn = new SqlConnection(this.ConnectionString);
        //        cmd = new SqlCommand("usp_partwatch_match_Offer_Add_SourceResult", cn);
        //        cmd.CommandType = CommandType.StoredProcedure;
        //        cmd.CommandTimeout = 120;
        //        cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = CustomerRequirementId;
        //        cmd.Parameters.Add("@MatchedOffer", SqlDbType.Structured).Value= dtOffer;
        //        cn.Open();
        //        ExecuteNonQuery(cmd);
        //    }
        //    catch (SqlException sqlex)
        //    {
        //        //LogException(sqlex);
        //        throw new Exception("Failed to update data", sqlex);
        //    }
        //    finally
        //    {
        //        cmd.Dispose();
        //        cn.Close();
        //        cn.Dispose();
        //    }
        //}
        /// <summary>
        /// Calls [usp_insert_CustomerRequirement_as_Alternate]
        /// </summary>
        /// <param name="customerRequirementId"></param>
        /// <param name="part"></param>
        /// <param name="manufacturerNo"></param>
        /// <param name="dateCode"></param>
        /// <param name="packageNo"></param>
        /// <param name="quantity"></param>
        /// <param name="price"></param>
        /// <param name="currencyNo"></param>
        /// <param name="receivedDate"></param>
        /// <param name="salesman"></param>
        /// <param name="datePromised"></param>
        /// <param name="notes"></param>
        /// <param name="instructions"></param>
        /// <param name="shortage"></param>
        /// <param name="companyNo"></param>
        /// <param name="contactNo"></param>
        /// <param name="usageNo"></param>
        /// <param name="alternate"></param>
        /// <param name="originalCustomerRequirementNo"></param>
        /// <param name="reasonNo"></param>
        /// <param name="productNo"></param>
        /// <param name="customerPart"></param>
        /// <param name="closed"></param>
        /// <param name="rohs"></param>
        /// <param name="updatedBy"></param>
        /// <param name="partWatch"></param>
        /// <param name="bom"></param>
        /// <param name="bomName"></param>
        /// <param name="bomNo"></param>
        /// <param name="FactorySealed"></param>
        /// <param name="MSL"></param>
        /// <param name="PQA"></param>
        /// <param name="ObsoleteChk"></param>
        /// <param name="LastTimeBuyChk"></param>
        /// <param name="RefirbsAcceptableChk"></param>
        /// <param name="TestingRequiredChk"></param>
        /// <param name="TargetSellPrice"></param>
        /// <param name="CompetitorBestOffer"></param>
        /// <param name="CustomerDecisionDate"></param>
        /// <param name="RFQClosingDate"></param>
        /// <param name="QuoteValidityRequired"></param>
        /// <param name="Type"></param>
        /// <param name="OrderToPlace"></param>
        /// <param name="RequirementforTraceability"></param>
        /// <param name="EAU"></param>
        /// <param name="AlternativesAccepted"></param>
        /// <param name="RepeatBusiness"></param>
        /// <param name="SupportTeamMemberNo"></param>
        /// <param name="CountryOfOrigin"></param>
        /// <param name="CountryOfOriginNo"></param>
        /// <param name="LifeCycleStage"></param>
        /// <param name="HTSCode"></param>
        /// <param name="AveragePrice"></param>
        /// <param name="Packing"></param>
        /// <param name="PackagingSize"></param>
        /// <param name="Descriptions"></param>
        /// <param name="IHSPartsId"></param>
        /// <param name="IHSCurrencyCode"></param>
        /// <param name="IHSProduct"></param>
        /// <param name="ECCNCode"></param>
        /// /// <param name="ECCNNo"></param>
        /// <returns></returns>
        public override Int32 AddAlternateNew(System.Int32? customerRequirementId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? receivedDate, System.Int32? salesman, System.DateTime? datePromised, System.String notes, System.String instructions, System.Boolean? shortage, System.Int32? companyNo, System.Int32? contactNo, System.Int32? usageNo, System.Boolean? alternate, System.Int32? originalCustomerRequirementNo, System.Int32? reasonNo, System.Int32? productNo, System.String customerPart, System.Boolean? closed, System.Byte? rohs, System.Int32? updatedBy, System.Boolean? partWatch, System.Boolean? bom, System.String bomName, System.Int32? bomNo, System.Boolean? FactorySealed, System.String MSL, System.Boolean? PQA, System.Boolean? ObsoleteChk, System.Boolean? LastTimeBuyChk, System.Boolean? RefirbsAcceptableChk, System.Boolean? TestingRequiredChk, System.Double? TargetSellPrice, System.Double? CompetitorBestOffer, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, System.Int32? Type, System.Boolean? OrderToPlace, System.Int32? RequirementforTraceability, System.String EAU, System.Boolean? AlternativesAccepted, System.Boolean? RepeatBusiness, System.Int32? SupportTeamMemberNo, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String LifeCycleStage, System.String HTSCode, System.Double? AveragePrice, System.String Packing, System.String PackagingSize, System.String Descriptions, System.Int32? IHSPartsId, System.String IHSCurrencyCode, System.String IHSProduct, System.String ECCNCode, System.Int32? ECCNNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_CustomerRequirement_as_Alternate", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@ReceivedDate", SqlDbType.DateTime).Value = receivedDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@DatePromised", SqlDbType.DateTime).Value = datePromised;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@Instructions", SqlDbType.NVarChar).Value = instructions;
                cmd.Parameters.Add("@Shortage", SqlDbType.Bit).Value = shortage;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = contactNo;
                cmd.Parameters.Add("@UsageNo", SqlDbType.Int).Value = usageNo;
                cmd.Parameters.Add("@Alternate", SqlDbType.Bit).Value = alternate;
                cmd.Parameters.Add("@OriginalCustomerRequirementNo", SqlDbType.Int).Value = originalCustomerRequirementNo;
                cmd.Parameters.Add("@ReasonNo", SqlDbType.Int).Value = reasonNo;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@CustomerPart", SqlDbType.NVarChar).Value = customerPart;
                cmd.Parameters.Add("@Closed", SqlDbType.Bit).Value = closed;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@PartWatch", SqlDbType.Bit).Value = partWatch;
                cmd.Parameters.Add("@BOM", SqlDbType.Bit).Value = bom;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.Bit).Value = FactorySealed;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                //  [002] code start
                cmd.Parameters.Add("@PartialQuantityAcceptable", SqlDbType.Bit).Value = PQA;
                cmd.Parameters.Add("@Obsolete", SqlDbType.Bit).Value = ObsoleteChk;
                cmd.Parameters.Add("@LastTimeBuy", SqlDbType.Bit).Value = LastTimeBuyChk;
                cmd.Parameters.Add("@RefirbsAcceptable", SqlDbType.Bit).Value = RefirbsAcceptableChk;
                cmd.Parameters.Add("@TestingRequired", SqlDbType.Bit).Value = TestingRequiredChk;
                cmd.Parameters.Add("@TargetSellPrice", SqlDbType.Float).Value = TargetSellPrice;
                cmd.Parameters.Add("@CompetitorBestOffer", SqlDbType.Float).Value = CompetitorBestOffer;
                cmd.Parameters.Add("@CustomerDecisionDate", SqlDbType.DateTime).Value = CustomerDecisionDate;
                cmd.Parameters.Add("@RFQClosingDate", SqlDbType.DateTime).Value = RFQClosingDate;
                cmd.Parameters.Add("@QuoteValidityRequired", SqlDbType.Int).Value = QuoteValidityRequired;
                cmd.Parameters.Add("@Type", SqlDbType.Int).Value = Type;
                cmd.Parameters.Add("@OrderToPlace", SqlDbType.Int).Value = OrderToPlace;
                cmd.Parameters.Add("@RequirementForTraceability", SqlDbType.Int).Value = RequirementforTraceability;
                cmd.Parameters.Add("@EAU", SqlDbType.NVarChar).Value = EAU;
                cmd.Parameters.Add("@AlternativesAccepted", SqlDbType.Bit).Value = AlternativesAccepted;
                cmd.Parameters.Add("@RepeatBusiness", SqlDbType.Bit).Value = RepeatBusiness;
                cmd.Parameters.Add("@SupportTeamMemberNo", SqlDbType.Int).Value = SupportTeamMemberNo;

                //IHS code start
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = CountryOfOriginNo;
                cmd.Parameters.Add("@LifeCycleStage", SqlDbType.NVarChar).Value = LifeCycleStage;
                cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = HTSCode;
                cmd.Parameters.Add("@AveragePrice", SqlDbType.Float).Value = AveragePrice;
                cmd.Parameters.Add("@Packing", SqlDbType.NVarChar).Value = Packing;
                cmd.Parameters.Add("@PackagingSize", SqlDbType.NVarChar).Value = PackagingSize;
                cmd.Parameters.Add("@Descriptions", SqlDbType.NVarChar).Value = Descriptions;
                cmd.Parameters.Add("@IHSPartsNo", SqlDbType.Int).Value = IHSPartsId;
                cmd.Parameters.Add("@IHSCurrencyCode", SqlDbType.NVarChar).Value = IHSCurrencyCode;
                cmd.Parameters.Add("@IHSProduct", SqlDbType.NVarChar).Value = IHSProduct;
                cmd.Parameters.Add("@ECCNCode", SqlDbType.NVarChar).Value = ECCNCode;
                cmd.Parameters.Add("@ECCNNo", SqlDbType.Int).Value = ECCNNo;


                //IHS code end     
                if (bomNo > 0)
                {
                    cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = bomNo;
                }
                cmd.Parameters.Add("@NewGenerateId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewGenerateId"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to add alternate of CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
        /// Calls [usp_select_PartFoundInReverseLogistics]
        /// </summary>
        public override CustomerRequirementDetails GetRLPart(System.String PartNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_PartFoundInReverseLogistics", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@PartNo", SqlDbType.VarChar).Value = PartNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.ReverseLogisticid = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.Partcount = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");

                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get RL Part Match", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override string GetPartLinesRL(System.String strpartNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Notifiy_RLVendor", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NChar).Value = strpartNo;
                cn.Open();
                string PartLinesRL = "";
                DbDataReader reader = cmd.ExecuteReader(CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    PartLinesRL = GetReaderValue_String(reader, "EmailBody", "");
                }
                return PartLinesRL;
             }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Part lines for RL vendor mail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<LyticaAPI> UpsertLyticaAPIData(string APIResponseJson, int? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_UpsertLyticaAPI", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@JSON", SqlDbType.NVarChar).Value = APIResponseJson;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);

                List<LyticaAPI> lst = new List<LyticaAPI>();
                while (reader.Read())
                {
                    LyticaAPI obj = new LyticaAPI();
                    obj.LyticaAPIId = GetReaderValue_Int32(reader, "SupplierAPIID", 0);
                    obj.Commodity = GetReaderValue_String(reader, "Commodity", "");
                    obj.OriginalPartSearched = GetReaderValue_String(reader, "OriginalPartSearched", "");
                    obj.Manufacturer = GetReaderValue_String(reader, "Manufacturer", "");
                    obj.AveragePrice = GetReaderValue_NullableDouble(reader, "AveragePrice", 0);
                    obj.TargetPrice = GetReaderValue_NullableDouble(reader, "TargetPrice", 0);
                    obj.MarketLeading = GetReaderValue_NullableDouble(reader, "MarketLeading", 0);
                    obj.LifeCycle = GetReaderValue_String(reader, "LifeCycle", "");
                    obj.lifeCycleStatus = GetReaderValue_String(reader, "lifeCycleStatus", "");
                    obj.OverAllRisk = GetReaderValue_String(reader, "OverAllRisk", "");
                    obj.PartBreadth = GetReaderValue_String(reader, "PartBreadth", "");
                    obj.ManufacturerBreadth = GetReaderValue_String(reader, "ManufacturerBreadth", "");
                    obj.DueDiligence = GetReaderValue_String(reader, "DueDiligence", "");
                    obj.PartConcentration = GetReaderValue_String(reader, "PartConcentration", "");
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Insert or Update Lytica API Data when create requirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool IsExistLyticaAPIDataOver3Days(string partNo, string mfrCode, int mfrNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_Get_LyticaAPIData_ByKey", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@partNo", SqlDbType.NVarChar).Value = partNo;
                cmd.Parameters.Add("@mfrCode", SqlDbType.NVarChar).Value = mfrCode;
                cmd.Parameters.Add("@mfrNo", SqlDbType.Int).Value = mfrNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                return reader.HasRows;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Get Lytica API Data by part and manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override LyticaAPI GetLyticaDataByPartMfr(System.String partNo, System.String manufacturerCode, System.String manufacturerName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_Get_LyticaAPIData_By_PartMfr", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@partNo", SqlDbType.NVarChar).Value = partNo;
                cmd.Parameters.Add("@manufacturerCode", SqlDbType.NVarChar).Value = manufacturerCode;
                cmd.Parameters.Add("@manufacturerName", SqlDbType.NVarChar).Value = manufacturerName;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);

                if (reader.Read())
                {
                    LyticaAPI obj = new LyticaAPI();
                    obj.LyticaAPIId = GetReaderValue_Int32(reader, "SupplierAPIID", 0);
                    obj.Commodity = GetReaderValue_String(reader, "Commodity", "");
                    obj.OriginalPartSearched = GetReaderValue_String(reader, "OriginalPartSearched", "");
                    obj.Manufacturer = GetReaderValue_String(reader, "Manufacturer", "");
                    obj.AveragePrice = GetReaderValue_NullableDouble(reader, "AveragePrice", 0);
                    obj.TargetPrice = GetReaderValue_NullableDouble(reader, "TargetPrice", 0);
                    obj.MarketLeading = GetReaderValue_NullableDouble(reader, "MarketLeading", 0);
                    obj.LifeCycle = GetReaderValue_String(reader, "LifeCycle", "");
                    obj.lifeCycleStatus = GetReaderValue_String(reader, "lifeCycleStatus", "");
                    obj.OverAllRisk = GetReaderValue_String(reader, "OverAllRisk", "");
                    obj.PartBreadth = GetReaderValue_String(reader, "PartBreadth", "");
                    obj.ManufacturerBreadth = GetReaderValue_String(reader, "ManufacturerBreadth", "");
                    obj.DueDiligence = GetReaderValue_String(reader, "DueDiligence", "");
                    obj.PartConcentration = GetReaderValue_String(reader, "PartConcentration", "");
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);

                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Get Lytica API Data by part and Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override string GetManufacturerNameByCode(string manufacturerCode, string mfrNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string manufacturerName = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_Get_Manufacturer_By_Code", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@manufacturerCode", SqlDbType.NVarChar).Value = manufacturerCode;
                cmd.Parameters.Add("@mfrNo ", SqlDbType.NVarChar).Value = mfrNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);

                while (reader.Read())
                {
                    manufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                }
                return manufacturerName;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Get ManufacturerName By Code", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<ProspectiveOfferForPowerApp> ListEmailPowerApp(List<int> customerRequirementIds, string flowName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_List_CusReq_PowerApp_By_Ids", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementIds", SqlDbType.VarChar).Value = String.Join(",", customerRequirementIds);
                cmd.Parameters.Add("@FlowName", SqlDbType.VarChar).Value = flowName;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                var lst = new List<ProspectiveOfferForPowerApp>();
                while (reader.Read())
                {
                    ProspectiveOfferForPowerApp obj = new ProspectiveOfferForPowerApp();
                    obj.Email = GetReaderValue_String(reader, "EMail", "");
                    obj.PowerAppUri = GetReaderValue_String(reader, "PowerAppUri", "");
                    obj.SalemanId = GetReaderValue_Int32(reader, "Salesman", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get CustomerRequirement for PowerApp", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<CustomerRequirementDetails> List(List<int> customerRequirementIds)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_List_CustomerRequirement_Details_By_Ids", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRequirementIds", SqlDbType.VarChar).Value = String.Join(",", customerRequirementIds);
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                var lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMNo", null);
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.LyticaAveragePrice = GetReaderValue_Double(reader, "LyticaAvgPrice", 0);
                    obj.IHSAveragePrice = GetReaderValue_Double(reader, "IHSAvgPrice", 0);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SalesmanName = GetReaderValue_String(reader, "EmployeeName", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "Currency", "");
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.NewOfferPriceFromProspective = GetReaderValue_NullableDouble(reader, "NewOfferPriceFromProspective", null);
                    obj.ClientCurrencyNo = GetReaderValue_NullableInt32(reader, "ClientCurrencyNo", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirement", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<CustomerRequirementDetails> GetHUBRFQHasRLStock(int? bomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_CusReq_Having_Available_RLStock", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                var lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMNo", null);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.ClientGlobalCurrencyNo = GetReaderValue_Int32(reader, "ClientGlobalCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.ReqGlobalCurrencyNo = GetReaderValue_Int32(reader, "ReqGlobalCurrencyNo", 0);
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirement has RLStock by BOMId", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<CustomerRequirementDetails> GetHUBRFQHasRLStockByPart(string part)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_CusReq_Having_Available_RLStock_By_Part", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                var lst = new List<CustomerRequirementDetails>();
                while (reader.Read())
                {
                    CustomerRequirementDetails obj = new CustomerRequirementDetails();
                    obj.BOMNo = GetReaderValue_NullableInt32(reader, "BOMNo", null);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.ClientGlobalCurrencyNo = GetReaderValue_Int32(reader, "ClientGlobalCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.ReqGlobalCurrencyNo = GetReaderValue_Int32(reader, "ReqGlobalCurrencyNo", 0);
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CustomerRequirement has RLStock by Part", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override LyticaAPI GetLyticaDataOnHUBRFQByPartMfr(System.Int32 cusReqNo, System.String mfrName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_GetLyticaDataOnHUBRFQ_ByPartMfr", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@mfrName", SqlDbType.NVarChar).Value = mfrName;
                cmd.Parameters.Add("@CusReqNo", SqlDbType.Int).Value = cusReqNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);

                if (reader.Read())
                {
                    LyticaAPI obj = new LyticaAPI();
                    obj.Manufacturer = GetReaderValue_String(reader, "LyticaManufacturerRef", "");
                    obj.AveragePrice = GetReaderValue_NullableDouble(reader, "AveragePrice", 0);
                    obj.TargetPrice = GetReaderValue_NullableDouble(reader, "TargetPrice", 0);
                    obj.MarketLeading = GetReaderValue_NullableDouble(reader, "MarketLeading", 0);
                    obj.lifeCycleStatus = GetReaderValue_String(reader, "LifeCycleStatus", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Get Lytica API Data by part and Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
