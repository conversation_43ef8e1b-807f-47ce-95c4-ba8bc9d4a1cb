﻿using AutoFixture;
using GlobalTrader2.Aggregator.UseCases.Account.LoginForSendEmai.Queries.Dtos;
using GlobalTrader2.Aggregator.UseCases.Account.LoginPreference.LoginPreference.Queries;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyECCNMessageSO;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.MailGroupMembers;
using GlobalTrader2.Dto.MailMessages;
using GlobalTrader2.Dto.PurchaseRequisition;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseRequisition.Queries;
using GlobalTrader2.Settings.UseCases.CompanySettings.MailGroupMembers.Queries;
using GlobalTrader2.UserAccount.UseCases.MailMessages.Commands.SendNewMessage;
using MediatR;
using Moq;
using System.Net.Mail;

namespace GlobalTrader2.Aggregator.Test.Orders.Notify
{
    public class NotifyECCNMessageSOHandlerTest
    {
        private readonly Mock<ISender> _senderMock;
        private readonly Mock<IRazorViewToStringService> _razorViewToStringServiceMock;
        private readonly Mock<IEmailService> _emailServiceMock;
        private readonly NotifyECCNMessageSOHandler _handler;
        private readonly Fixture _fixture;

        public NotifyECCNMessageSOHandlerTest()
        {
            _senderMock = new Mock<ISender>();
            _razorViewToStringServiceMock = new Mock<IRazorViewToStringService>();
            _emailServiceMock = new Mock<IEmailService>();
            _handler = new NotifyECCNMessageSOHandler(_senderMock.Object, _razorViewToStringServiceMock.Object, _emailServiceMock.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task Handle_Success_SendsInternalAndExternalNotifications()
        {
            // Arrange
            var request = _fixture.Build<NotifyECCNMessageSOCommand>()
                .With(x => x.Recipient, new List<RecipientRequest> { new RecipientRequest(1, 1), new RecipientRequest(6978, 2) })
                .With(x => x.SenderEmail, "<EMAIL>")
                .With(x => x.SenderName, "Sender Name")
                .With(x => x.SalesOrderLineId, 123)
                .Create();

            var saleOrderLineDetail = new SalesOrderLineDto
            {
                SalesOrderNo = 1001,
                SalesOrderNumber = 2002,
                Part = "PART-123",
                ECCNCode = "ECCN-456",
                WarningMessage = "Warning",
                ECCNMessage = "ECCN Message",
                ECCNSubject = "ECCN Subject",
                ECCNClientNotify = true
            };

            _senderMock.Setup(s => s.Send(It.IsAny<GetSalesOrderLineQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<SalesOrderLineDto>
                {
                    Success = true,
                    Data = saleOrderLineDetail
                });

            _razorViewToStringServiceMock.Setup(r => r.RenderViewToStringAsync(
                It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync("RenderedContent");

            _senderMock.Setup(s => s.Send(It.IsAny<SendNewMessageCommand>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<int> { Success = true, Data = 0 });

            _senderMock.Setup(s => s.Send(It.IsAny<GetAllLoginForSendEmailQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<IEnumerable<LoginForSendEmailDto>>
                {
                    Success = true,
                    Data = new List<LoginForSendEmailDto>
                    {
                        new LoginForSendEmailDto{ SendEmail = true, Email = "<EMAIL>" }
                    }
                });

            _senderMock.Setup(s => s.Send(It.IsAny<GetMailGroupMembersQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<List<MailGroupMemberDto>>
                {
                    Success = true,
                    Data = [new MailGroupMemberDto
                    {
                        LoginNo = 6978,
                    },
                    new MailGroupMemberDto
                    {
                        LoginNo = 6979,
                    }
                    ]
                });

            _emailServiceMock.Setup(e => e.TrySendEmailAsync(
                It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<List<string>>(),
                It.IsAny<List<string>>(), It.IsAny<string>(), It.IsAny<string>(),
                It.IsAny<List<string>>(), It.IsAny<List<Attachment>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.True(result.Data);

            _senderMock.Verify(s => s.Send(It.IsAny<SendNewMessageCommand>(), It.IsAny<CancellationToken>()), Times.Once);
            _emailServiceMock.Verify(e => e.TrySendEmailAsync(
                request.SenderEmail,
                It.Is<List<string>>(l => l.Contains("<EMAIL>")),
                It.IsAny<List<string>>(),
                It.IsAny<List<string>>(),
                saleOrderLineDetail.ECCNSubject,
                "RenderedContent",
                It.IsAny<List<string>>(),
                It.IsAny<List<Attachment>>(),
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ReturnsEarly_WhenSaleOrderLineNotFound()
        {
            // Arrange
            var request = _fixture.Create<NotifyECCNMessageSOCommand>();
            _senderMock.Setup(s => s.Send(It.IsAny<GetSalesOrderLineQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<SalesOrderLineDto> { Success = false, Data = null });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            _razorViewToStringServiceMock.Verify(r => r.RenderViewToStringAsync(It.IsAny<string>(), It.IsAny<object>()), Times.Never);
            _senderMock.Verify(s => s.Send(It.IsAny<SendNewMessageCommand>(), It.IsAny<CancellationToken>()), Times.Never);
            _emailServiceMock.Verify(e => e.TrySendEmailAsync(
                It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<List<string>>(),
                It.IsAny<List<string>>(), It.IsAny<string>(), It.IsAny<string>(),
                It.IsAny<List<string>>(), It.IsAny<List<Attachment>>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task Handle_ReturnsEarly_WhenECCNCodeIsNullOrEmpty()
        {
            // Arrange
            var request = _fixture.Create<NotifyECCNMessageSOCommand>();
            var saleOrderLineDetail = new SalesOrderLineDto
            {
                SalesOrderNo = 1001,
                SalesOrderNumber = 2002,
                Part = "PART-123",
                ECCNCode = "",
                WarningMessage = "Warning",
                ECCNMessage = "ECCN Message",
                ECCNSubject = "ECCN Subject"
            };

            _senderMock.Setup(s => s.Send(It.IsAny<GetSalesOrderLineQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<SalesOrderLineDto>
                {
                    Success = true,
                    Data = saleOrderLineDetail
                });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            _razorViewToStringServiceMock.Verify(r => r.RenderViewToStringAsync(It.IsAny<string>(), It.IsAny<object>()), Times.Never);
            _senderMock.Verify(s => s.Send(It.IsAny<SendNewMessageCommand>(), It.IsAny<CancellationToken>()), Times.Never);
            _emailServiceMock.Verify(e => e.TrySendEmailAsync(
                It.IsAny<string>(), It.IsAny<List<string>>(), It.IsAny<List<string>>(),
                It.IsAny<List<string>>(), It.IsAny<string>(), It.IsAny<string>(),
                It.IsAny<List<string>>(), It.IsAny<List<Attachment>>(), It.IsAny<CancellationToken>()), Times.Never);
        }
    }
}
