﻿namespace GlobalTrader2.Orders.UserCases.Orders.PriceRequest
{
    public class GetPOQuoteLineDataListNuggetQuery : IRequest<BaseResponse<IEnumerable<POQuoteLineDataListNuggetDto>>>
    {
        public int? ClientId { get; set; }
        public int? TeamId { get; set; }
        public int? DivisionId { get; set; }
        public int? LoginId { get; set; }
        public int? OrderBy { get; set; }
        public int? SortDir { get; set; }
        public int? PageIndex { get; set; }
        public int? PageSize { get; set; }
        public string? PartSearch { get; set; }
        public string? CmSearch { get; set; }
        public int? SalesmanSearch { get; set; }
        public bool? IncludeClosed { get; set; }
        public int? PoQuoteNoLo { get; set; }
        public int? PoQuoteNoHi { get; set; }
        public DateTime? DateQuotedFrom { get; set; }
        public DateTime? DateQuotedTo { get; set; }
        public bool? RecentOnly { get; set; }
    }

}
