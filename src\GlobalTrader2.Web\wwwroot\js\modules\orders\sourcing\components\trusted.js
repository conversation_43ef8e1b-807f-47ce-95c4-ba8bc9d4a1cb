﻿$(() => {
    const isPoHub = $("#is-po-hub").val() === "true";

    initializeTable({
        tableId: "trusted-table",
        legendId: "legend-Trusted",
        rowId: "excessId",
        columns: [
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [trustedLocalizedStrings.quantity, trustedLocalizedStrings.status]),
                render: (row) => GlobalTrader.DataTablesHelper.createStackedCell([row?.quantity ?? 0, row?.offerStatus ?? ""]),
                type: "string",
                className: "text-break",
            },
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [trustedLocalizedStrings.partNo, trustedLocalizedStrings.additionalNotesFromSupplier]),
                render: (row) => GlobalTrader.DataTablesHelper.createStackedCell([getPartNoAndRohsStatus(row.part, row.rohs), row.notes]),
                className: "text-break",
            },
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [trustedLocalizedStrings.mfr, trustedLocalizedStrings.dc]),
                render: function (row) {
                    let mfrDisplayText = getMfrContent(row.manufacturerNo, row.manufacturerCode ?? row.manufacturerName, row.isRestrictedManufacturer);
                    return GlobalTrader.DataTablesHelper.createStackedCell([mfrDisplayText, row.dateCode])
                },
                className: "text-break",
            },
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [trustedLocalizedStrings.product, trustedLocalizedStrings.package]),
                render: (row) => GlobalTrader.DataTablesHelper.createStackedCell([row.productName, row.packageName]),
                className: "text-break",
            },
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [trustedLocalizedStrings.supplier, trustedLocalizedStrings.type]),
                render: (row) => GlobalTrader.DataTablesHelper.createStackedCell([
                    getSuplierMessage(row, isPoHub),
                    row.supplierType]),
                className: "text-break",
            },
            {
                data: null, title: GlobalTrader.DataTablesHelper.createStackedHeader(
                    [trustedLocalizedStrings.offered, trustedLocalizedStrings.by]),
                render: (row) => GlobalTrader.DataTablesHelper.createStackedCell(
                    [GlobalTrader.DatetimeHelper.formatDate(new Date(row.offerStatusChangeDate)),
                    row.salesmanName ?? row.offerStatusChangeEmployeeName]),
                className: "text-break",
            },
            {
                data: null, title: trustedLocalizedStrings.unitPrice,
                render: (row) => formatCurrencyRate(row.price, row.currencyCode),
                type: "string",
                className: "text-break",
            }
        ],
        onSelectDeselect: (table) => {
            const selectedRows = table.rows({ selected: true }).data().toArray();
            const selectedRow = selectedRows.length === 1 ? selectedRows[0] : null;

            const canEditTrusted = selectedRow?.currentClient;
            setButtonState("#edit-trusted-button", !!canEditTrusted);

            const hasUnrestricted = selectedRows.some(row => row.isRestrictedManufacturer === false);
            const canAddToRequirement = (!isRequirementPage() || !stateValue?.selectedRequirementDetails?.closed) && (selectedRows.length > 0 && hasUnrestricted);
            setButtonState("#trusted-add-to-requirement", canAddToRequirement);

            const canCloneAndAddToRequirement = selectedRow && isHUBRFQDetails() && !stateValue?.selectedRequirementDetails?.closed && selectedRows.length && hasUnrestricted;
            setButtonState("#clone-trusted-and-add-to-requirement", canCloneAndAddToRequirement);
        }
    });
});
